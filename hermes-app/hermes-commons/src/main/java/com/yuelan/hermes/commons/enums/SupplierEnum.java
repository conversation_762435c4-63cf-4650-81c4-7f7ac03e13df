package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SupplierEnum implements IEnum<SupplierEnum> {
    YUE_LAN(0, "悦蓝"),
    RYT(1, "软游通"),
    BIAN_WA(2, "变蛙"),
    QI_QU(3, "奇趣创享"),
    KNHQ(4, "科能恒启"),
    WANG_YI_QMX(5, "网易(全明星,永劫)"),
    XIMI(6, "西米"),
    TE_ZHEN(7, "特祯"),
    FEI_HAN(8, "飞翰"),
    T_MALL(9, "天猫"),
    BAO_MI_HUA(10, "爆米花"),
    NET_EASE_PARTY(11, "网易-蛋仔派对"),
    LAN_JIN(12, "蓝鲸"),
    JIN_XI(13, "今溪")
    ;

    private Integer code;
    private String desc;

    @Override
    public SupplierEnum getDefault() {
        return YUE_LAN;
    }

    public static SupplierEnum of(Integer code) {
        for (SupplierEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
