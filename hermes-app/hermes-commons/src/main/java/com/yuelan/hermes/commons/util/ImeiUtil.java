package com.yuelan.hermes.commons.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Objects;
import java.util.Random;

/**
 * <AUTHOR> 2025/6/18
 * @since 2025/6/18
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ImeiUtil {

    public static boolean verifyImei(String imei) {
        if (Objects.isNull(imei)) {
            return false;
        }
        if (imei.length() != 15) {
            return false;
        }
        int sum = 0;
        for (int i = 0; i < imei.length(); i++) {
            int digit = Character.getNumericValue(imei.charAt(i));
            if (i % 2 == 0) {
                sum += digit;
            } else {
                sum += digit * 2 % 10 + digit / 5;
            }
        }
        return sum % 10 == 0;
    }

    public static String generateImei() {
        Random random = new Random();
        StringBuilder imeiBuilder = new StringBuilder();

        // 生成前14位随机数字
        for (int i = 0; i < 14; i++) {
            imeiBuilder.append(random.nextInt(10));
        }

        // 计算第15位校验位
        int sum = 0;
        for (int i = 0; i < 14; i++) {
            int digit = Character.getNumericValue(imeiBuilder.charAt(i));
            if (i % 2 == 0) {
                sum += digit;
            } else {
                sum += digit * 2 % 10 + digit / 5;
            }
        }

        // 计算校验位（第15位）
        int checksum = (10 - (sum % 10)) % 10;
        imeiBuilder.append(checksum);

        return imeiBuilder.toString();
    }

    public static void main(String[] args) {
        for (int i = 0; i < 100; i++) {
            String imei = generateImei();
            System.out.println(imei);
            if (!verifyImei(imei)) {
                System.out.println("校验失败");
            }
        }

    }
}
