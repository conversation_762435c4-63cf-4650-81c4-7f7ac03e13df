package com.yuelan.hermes.commons.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR> 2025/6/18
 * @since 2025/6/18
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ImeiUtil {

    public static boolean verifyImei(String imei) {
        if (Objects.isNull(imei)) {
            return false;
        }
        if (imei.length() != 15) {
            return false;
        }
        int sum = 0;
        for (int i = 0; i < imei.length(); i++) {
            int digit = Character.getNumericValue(imei.charAt(i));
            if (i % 2 == 0) {
                sum += digit;
            } else {
                sum += digit * 2 % 10 + digit / 5;
            }
        }
        return sum % 10 == 0;
    }
}
