package com.yuelan.hermes.commons.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * ICCID验证工具类
 *
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class IccidUtil {

    /**
     * 验证ICCID格式
     *
     * @param iccid ICCID号码
     * @return 是否有效
     */
    public static boolean verifyIccid(String iccid) {
        if (Objects.isNull(iccid)) {
            return false;
        }

        // 检查长度：19-20位
        if (iccid.length() < 19 || iccid.length() > 20) {
            return false;
        }

        // 检查是否全为数字
        if (!iccid.matches("\\d+")) {
            return false;
        }

        return true;
    }

    /**
     * Luhn校验算法
     *
     * @param number 待校验的数字字符串
     * @return 是否通过校验
     */
    private static boolean luhnCheck(String number) {
        int sum = 0;
        boolean alternate = false;

        // 从右到左遍历
        for (int i = number.length() - 1; i >= 0; i--) {
            int digit = Character.getNumericValue(number.charAt(i));

            if (alternate) {
                digit *= 2;
                if (digit > 9) {
                    digit = (digit % 10) + 1;
                }
            }

            sum += digit;
            alternate = !alternate;
        }

        return (sum % 10) == 0;
    }


}
