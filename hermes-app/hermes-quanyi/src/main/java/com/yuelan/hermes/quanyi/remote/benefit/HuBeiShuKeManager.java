package com.yuelan.hermes.quanyi.remote.benefit;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.common.pojo.properties.HuBeiShuKeProperties;
import com.yuelan.hermes.quanyi.remote.hbsk.request.H5OrderLaunchReq;
import com.yuelan.hermes.quanyi.remote.hbsk.request.H5OrderQueryReq;
import com.yuelan.hermes.quanyi.remote.hbsk.request.ImusicCallbackReq;
import com.yuelan.hermes.quanyi.remote.hbsk.response.H5OrderLaunchResp;
import com.yuelan.hermes.quanyi.remote.hbsk.response.H5OrderQueryResp;
import com.yuelan.hermes.quanyi.remote.hbsk.response.HuBeiShuKeCallbackResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 湖北数科
 *
 * <AUTHOR> 2025/7/17
 * @since 2025/7/17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HuBeiShuKeManager {

    private static final String H5_ORDER_LAUNCH_URL = "http://api.118100.cn/openapi/services/v3/packageservice/confirm_open_order_launched_ex.json";
    private static final String H5_ORDER_QUERY_URL = "http://api.118100.cn/openapi/services/v3/packageservice/query_h5_order.json";
    private static final String PACKAGE_SUBSCRIBE_QUERY_URL = "http://api.118100.cn/openapi/services/v3/packageservice/query_package_subscribe.json";
    private static final String PACKAGE_UNSUBSCRIBE_URL = "http://api.118100.cn/openapi/services/v3/packageservice/unsubscribe_package.json";
    private static final String VIDEO_MUSIC_BOX_QUERY_URL = "http://api.118100.cn/openapi/services/v3/packageservice/query_video_music_box.json";
    private static final String VIDEO_RINGTONE_QUERY_URL = "http://api.118100.cn/openapi/services/v3/packageservice/query_video_ringtone.json";
    private static final String GET_USER_PAY_TOKEN_URL = "https://api.118100.cn/openapi/services/v3/packageservice/h5portal_sdk_get_tokn.json";

    private final HuBeiShuKeProperties huBeiShuKeProperties;

    /**
     * 解密方法，复现 JavaScript 中的 H.decrypt 逻辑
     *
     * @param encryptedBase64 经过 Base64 编码的加密数据
     * @param timestamp       时间戳 (imtimestamp)
     * @param randomnum       随机数 (imrandomnum)
     * @param encryptkey      加密密钥 (imencryptkey)
     * @return 解密后的明文字符串
     */
    public static String decrypt(String encryptedBase64, String timestamp, String randomnum, String encryptkey) {
        // 1. 生成密钥 (Key)
        String md5Random = DigestUtil.md5Hex(randomnum);
        String base64Timestamp = cn.hutool.core.codec.Base64.encode(timestamp, StandardCharsets.UTF_8);
        String keySource = base64Timestamp + encryptkey + md5Random;
        // 截取前16个字符作为最终的 key
        String key = DigestUtil.md5Hex(keySource).substring(0, 16);
        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);

        // 2. 生成初始化向量 (IV)
        String md5Timestamp = DigestUtil.md5Hex(timestamp);
        String base64Random = Base64.encode(randomnum, StandardCharsets.UTF_8);
        String ivSource = base64Random + encryptkey + md5Timestamp;
        // 截取前16个字符作为最终的 IV
        String iv = DigestUtil.md5Hex(ivSource).substring(0, 16);
        byte[] ivBytes = iv.getBytes(StandardCharsets.UTF_8);

        // 3. 创建 AES 实例
        // 算法: AES, 模式: CBC, 填充: PKCS5Padding (与JS中的PKCS7Padding兼容)
        AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, keyBytes, ivBytes);

        // 4. 解密
        // Hutool的decrypt方法会自动处理Base64解码
        return aes.decryptStr(encryptedBase64, StandardCharsets.UTF_8);
    }

    /**
     * 解密由 H.encrypt() 加密的数据。
     * 此方法使用了与 H.encrypt() 完全相同的 Key 和 IV 生成逻辑。
     *
     * @param encryptedBase64 经过 Base64 编码的加密数据 (来自请求的 formData)
     * @param timestamp       用于加密的时间戳 (imtimestamp)
     * @param randomnum       用于加密的随机数 (imrandomnum)
     * @param encryptkey      用于加密的加密密钥 (imencryptkey)
     * @return 解密后的明文字符串
     */
    public static String decryptRequestData(String encryptedBase64, String timestamp, String randomnum, String encryptkey) {
        // 1. 生成密钥 (Key) - 与 H.encrypt 的逻辑保持一致
        String md5Timestamp = DigestUtil.md5Hex(timestamp);
        String base64Random = Base64.encode(randomnum, StandardCharsets.UTF_8);
        String keySource = base64Random + md5Timestamp + encryptkey;
        String key = DigestUtil.md5Hex(keySource).substring(0, 16);
        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);

        // 2. 生成初始化向量 (IV) - 与 H.encrypt 的逻辑保持一致
        String md5Random = DigestUtil.md5Hex(randomnum);
        String base64Timestamp = Base64.encode(timestamp, StandardCharsets.UTF_8);
        String ivSource = base64Timestamp + md5Random + encryptkey;
        String iv = DigestUtil.md5Hex(ivSource).substring(0, 16);
        byte[] ivBytes = iv.getBytes(StandardCharsets.UTF_8);

        // 3. 创建 AES 实例
        AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, keyBytes, ivBytes);

        // 4. 解密
        return aes.decryptStr(encryptedBase64, StandardCharsets.UTF_8);
    }

    /**
     * 主函数，用于演示
     */
    public static void main(String[] args) {
        // --- 示例数据 ---
        // 以下数据需要从实际的网络请求中抓取才能成功解密
        String encryptedData = "b4L5I5qP1f5Q2pe8kUqZO4LHLFBxxcsd+0 r98Ualw0yMAu05FSdPuiN3F8KBZvzcuGdmfVbaemb2zhPbMXaWQL2jLIZuEzwcdsgruwtv5hE="; // 示例密文，请替换为实际抓取的数据
        // encryptedData="VaaAL30vRnEWU8if+HOP4f1HkIRuXkj/iVBzacp+FQE7dd+/wLVyN5OC+8uh4qvyuqwMdTimgrWr+JG0VPPLjUib36Jmi56m2aIX1fJkx9/8JvWH899Xc8KkinmBhkQoHMhdhR5ZEQ97GVzSrLxjigXlzUJdO/Ps9DQc456BUNf7BFKYRA7mH3PseiGvk9nE4Wsuy7IjdnhJhpj+JsiR5A==";
        String timestamp = "1752806855875"; // 示例时间戳
        String randomnum = "w72rtms6ya8yjin6"; // 示例随机数
        String encryptkey = "bc3d0d631ed9cd5eca484f651e805a0a"; // 示例加密密钥


        System.out.println("--- 解密参数 ---");
        System.out.println("密文 (Base64): " + encryptedData);
        System.out.println("时间戳: " + timestamp);
        System.out.println("随机数: " + randomnum);
        System.out.println("加密密钥: " + encryptkey);
        System.out.println("----------------");

        try {
            String decryptedData = decrypt(encryptedData, timestamp, randomnum, encryptkey);
            System.out.println("解密成功: ");
            System.out.println(decryptedData);
        } catch (Exception e) {
            System.err.println("解密失败，请检查加密数据和密钥是否正确。");
            e.printStackTrace();
        }

        // 请求body 解密过程
        String encryptedDataFromRequest = "79oGcIuegdxXdgg662Mdhhplv+XZjKfUAMZZ1WnUn4nheYsY35Y5sVNNPXGTnqdrDx80yzMckCKIiiH/9qhlHaBScaUutFSc0baquV+XPTpPU5mTgSQ8eduQq5LZ6niPkixs/oosO2c71LxhEe6MWRUqa7CxEBwY/0tDLlTLG6wgSON9QMKjIpz3dgBhRXx2";
        String decryptedData = decryptRequestData(encryptedDataFromRequest, timestamp, randomnum, encryptkey);
        System.out.println("解密成功: ");
        System.out.println(decryptedData);

        //      生成规则详解
        //     e = String((new Date).getTime())
        // t = function(e) {
        //                 e = e || 32;
        //                 for (var t = "abcdefhijkmnprstwxyz2345678", i = t.length, a = "", s = 0; s < e; s++)
        //                     a += t.charAt(Math[floor](Math[random]() * i));
        //                 return a
        //}(16)
        // , i = y(B[encode](y(e) + t) + t);
        System.out.println(generateI(timestamp, randomnum));

    }

    /**
     * 根据时间戳和随机字符串生成最终的加密密钥 i。
     *
     * @param timestamp    时间戳字符串 (e)
     * @param randomString 随机字符串 (t)
     * @return 生成的加密密钥 (i)
     */
    public static String generateI(String timestamp, String randomString) {
        // 1. 对时间戳 e 进行 MD5 哈希
        String md5OfTimestamp = SecureUtil.md5(timestamp);

        // 2. 哈希结果与随机字符串 t 拼接
        String stringToEncode = md5OfTimestamp + randomString;

        // 3. 对拼接后的字符串进行 Base64 编码
        String base64Encoded = Base64.encode(stringToEncode.getBytes(StandardCharsets.UTF_8));

        // 4. 编码结果再次与随机字符串 t 拼接
        String finalStringToHash = base64Encoded + randomString;

        // 5. 对最终的字符串进行 MD5 哈希
        return SecureUtil.md5(finalStringToHash);

    }

    /**
     * H5计费下单发起接口
     *
     * @param req 下单请求
     * @return H5计费下单响应
     */
    public H5OrderLaunchResp launchH5Order(H5OrderLaunchReq req) {
        // 构建请求参数
        Map<String, Object> params = buildRequestParams(req);

        // 构建完整的请求头
        Map<String, String> headers = buildRequestHeaders(params);

        HttpRequest httpRequest = HttpRequest.post(H5_ORDER_LAUNCH_URL)
                .headerMap(headers, true)
                .form(params);

        log.info("爱音乐H5计费下单请求: {}", httpRequest);

        try (HttpResponse response = httpRequest.execute()) {
            String body = response.body();
            log.info("爱音乐H5计费下单返回参数: {}", body);
            return JSON.parseObject(body, H5OrderLaunchResp.class);
        }
    }

    /**
     * H5计费订单详情查询
     *
     * @param req 查询请求
     * @return 订单详情响应
     */
    public H5OrderQueryResp queryH5Order(H5OrderQueryReq req) {
        // 构建请求参数
        Map<String, Object> params = buildRequestParams(req);

        // 构建完整的请求头
        Map<String, String> headers = buildRequestHeaders(params);

        HttpRequest httpRequest = HttpRequest.post(H5_ORDER_QUERY_URL)
                .headerMap(headers, true)
                .form(params);

        log.info("爱音乐H5订单查询请求: {}", httpRequest);

        try (HttpResponse response = httpRequest.execute()) {
            String body = response.body();
            log.info("爱音乐H5订单查询返回参数: {}", body);
            return JSON.parseObject(body, H5OrderQueryResp.class);
        }
    }

    /**
     * 处理订购退订消息回调
     *
     * @param req 回调请求
     * @return 回调响应
     */
    public HuBeiShuKeCallbackResp handleCallback(ImusicCallbackReq req) {
        log.info("爱音乐订购退订回调请求: {}", JSON.toJSONString(req));

        try {
            // 验证签名
            if (!verifyCallbackSignature(req)) {
                log.error("爱音乐回调签名验证失败");
                return HuBeiShuKeCallbackResp.error("签名验证失败");
            }

            // 处理回调业务逻辑
            processCallback(req);

            log.info("爱音乐回调处理成功，手机号: {}, 产品ID: {}", req.getMobile(), req.getProductid());
            return HuBeiShuKeCallbackResp.success();

        } catch (Exception e) {
            log.error("爱音乐回调处理异常", e);
            return HuBeiShuKeCallbackResp.error("处理异常: " + e.getMessage());
        }
    }

    /**
     * 获取用户发起支付的 token
     */
    public void getUserPayToken(String mobile) {
        String url = GET_USER_PAY_TOKEN_URL + "?mobile=" + mobile;
        Map<String, Object> params = new HashMap<>();
        params.put("mobile", mobile);

        // 构建完整的请求头
        Map<String, String> headers = buildRequestHeaders(params);

        HttpRequest httpRequest = HttpRequest.get(url)
                .headerMap(headers, true);

        try (HttpResponse response = httpRequest.execute()) {
            String body = response.body();
            log.info("爱音乐获取用户支付token返回参数: {}", body);
        }
    }

    /**
     * 验证回调签名 - 根据最新文档的签名算法
     */
    private boolean verifyCallbackSignature(ImusicCallbackReq req) {
        try {
            // 根据文档：签名原文 = timestamp + mobile + productid + state
            String signData = req.getTimestamp() + req.getMobile() + req.getProductid() + req.getState();

            // 使用 HmacSHA256 + Base64 算法
            String expectedSignature = generateHmacSha256Base64Signature(huBeiShuKeProperties.getSecret(), signData);

            return expectedSignature.equals(req.getSignatureHmacsha256());
        } catch (Exception e) {
            log.error("爱音乐验证回调签名异常", e);
            return false;
        }
    }

    /**
     * 生成 HmacSHA256 + Base64 签名 - 用于回调验证
     */
    private String generateHmacSha256Base64Signature(String secret, String data) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] signatureBytes = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.encode(signatureBytes);
        } catch (Exception e) {
            log.error("爱音乐生成HmacSHA256签名失败", e);
            throw new RuntimeException("HmacSHA256签名生成失败", e);
        }
    }

    /**
     * 处理回调业务逻辑
     */
    private void processCallback(ImusicCallbackReq req) {
        String state = req.getState();
        String mobile = req.getMobile();
        String productid = req.getProductid();

        switch (state) {
            case "0":
                log.info("爱音乐处理订购回调，手机号: {}, 产品ID: {}", mobile, productid);
                // TODO: 处理订购成功逻辑
                break;
            case "1":
                log.info("爱音乐处理退订回调，手机号: {}, 产品ID: {}", mobile, productid);
                // TODO: 处理退订逻辑
                break;
            case "2":
                log.info("爱音乐处理延迟计费生效回调，手机号: {}, 产品ID: {}", mobile, productid);
                // TODO: 处理延迟计费生效逻辑
                break;
            case "3":
                log.info("爱音乐处理延迟计费退订回调，手机号: {}, 产品ID: {}", mobile, productid);
                // TODO: 处理延迟计费退订逻辑
                break;
            default:
                log.warn("爱音乐未知的回调状态: {}，手机号: {}, 产品ID: {}", state, mobile, productid);
                break;
        }
    }

    /**
     * 构建请求参数
     */
    private Map<String, Object> buildRequestParams(Object request) {
        return JSON.parseObject(JSON.toJSONString(request));
    }

    /**
     * 构建请求头 - 根据最新文档的平台接口授权机制
     */
    private Map<String, String> buildRequestHeaders(Map<String, Object> params) {
        Map<String, String> headers = new HashMap<>();

        // 基础请求头
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

        // 业务相关请求头
        if (CharSequenceUtil.isNotBlank(huBeiShuKeProperties.getReferer())) {
            headers.put("Referer", huBeiShuKeProperties.getReferer());
        }

        // 认证相关请求头
        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = generateNonce();

        headers.put("auth-deviceid", huBeiShuKeProperties.getDeviceId());
        headers.put("auth-timestamp", timestamp);
        headers.put("auth-nonce", nonce);
        headers.put("auth-signature", generateSignature(params, timestamp, nonce));

        return headers;
    }

    /**
     * 生成随机数
     */
    private String generateNonce() {
        return String.valueOf(System.currentTimeMillis() + (int) (Math.random() * 1000));
    }

    /**
     * 生成签名 - 根据最新文档要求
     */
    private String generateSignature(Map<String, Object> params, String timestamp, String nonce) {
        try {
            // 1. 参数排序
            TreeMap<String, Object> sortedParams = new TreeMap<>(params);

            // 2. 构建签名字符串
            StringBuilder signStr = new StringBuilder();

            // 添加固定参数
            signStr.append("deviceid=").append(huBeiShuKeProperties.getDeviceId());
            signStr.append("&timestamp=").append(timestamp);
            signStr.append("&nonce=").append(nonce);

            // 添加业务参数
            for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
                if (entry.getValue() != null && CharSequenceUtil.isNotBlank(entry.getValue().toString())) {
                    signStr.append("&").append(entry.getKey()).append("=").append(entry.getValue());
                }
            }

            // 添加密钥
            signStr.append("&key=").append(huBeiShuKeProperties.getSecret());

            log.debug("爱音乐签名原始字符串: {}", signStr.toString());

            // 3. MD5加密
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(signStr.toString().getBytes(StandardCharsets.UTF_8));

            // 4. 转换为16进制字符串并转大写
            StringBuilder hexStr = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexStr.append('0');
                }
                hexStr.append(hex);
            }

            String signature = hexStr.toString().toUpperCase();
            log.debug("爱音乐生成签名: {}", signature);

            return signature;

        } catch (Exception e) {
            log.error("爱音乐生成签名失败", e);
            throw new RuntimeException("签名生成失败", e);
        }
    }


}
