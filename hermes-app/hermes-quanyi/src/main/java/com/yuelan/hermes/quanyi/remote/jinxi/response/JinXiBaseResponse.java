package com.yuelan.hermes.quanyi.remote.jinxi.response;

import lombok.Data;

/**
 * 今溪基础响应格式
 *
 * <AUTHOR> 2025/8/2
 * @since 2025/8/2
 */
@Data
public class JinXiBaseResponse<T> {

    /**
     * 返回码
     */
    private String code;

    /**
     * 返回码描述
     */
    private String message;

    /**
     * 响应结果
     */
    private T data;

    /**
     * 创建成功响应
     */
    public static <T> JinXiBaseResponse<T> success(T data) {
        JinXiBaseResponse<T> response = new JinXiBaseResponse<>();
        response.setCode("200");
        response.setMessage("成功");
        response.setData(data);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static <T> JinXiBaseResponse<T> fail(String code, String message) {
        JinXiBaseResponse<T> response = new JinXiBaseResponse<>();
        response.setCode(code);
        response.setMessage(message);
        return response;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }
}
