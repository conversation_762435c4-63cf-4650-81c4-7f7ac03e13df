package com.yuelan.hermes.quanyi.common.util;

import cn.hutool.core.util.ReUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.regex.Pattern;

/**
 * <AUTHOR> 2025/6/20
 * @since 2025/6/20
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class NumCardPromptPolishingUtil {

    private static final HashMap<Pattern, String> promptPolishingMap = new HashMap<>();
    private static final String SYS_ERROR_DEFAULT_PROMPT = "系统异常，请您稍后重新再试";
    private static final String ID_CARD_LIMIT_PROMPT = "您的身份证办卡数量已达上限，请更换其他身份证办理";
    private static final String SELECT_NUMBER_FAIL_PROMPT = "选占号码失败，请选择其他号码下单";
    private static final String SELECTED_NUMBER_FAIL_PROMPT = "您选的号码已被其他用户预定，请重新选择其他号码";
    private static final String ID_CARD_ERROR_PROMPT = "您的身份证信息存在问题，请更换其他身份证办理";

    static {
        promptPolishingMap.put(Pattern.compile(".*不支持该地区办理.*"), "不支持该地区办理");
        promptPolishingMap.put(Pattern.compile(".*同一证件15天内存在未激活订单.*"), "您的身份证存在其他订单，请更换其他身份证办理");
        promptPolishingMap.put(Pattern.compile(".*选占号码证件号码不相同.*"), "选占号码失败，请重新选择其他号码下单");
        promptPolishingMap.put(Pattern.compile(".*只允许上架、预配、选占号码做选占"), SELECT_NUMBER_FAIL_PROMPT);
        promptPolishingMap.put(Pattern.compile("风控拦截:获取推荐人编码失败"), "风控拦截:获取推荐人编码失败");
        promptPolishingMap.put(Pattern.compile("风控拦截:您的身份证办卡数量已达上限"), ID_CARD_LIMIT_PROMPT);
        promptPolishingMap.put(Pattern.compile("验证身份证信息失败:您的证件办理量已达最大数量，请更换证件办理！"), ID_CARD_LIMIT_PROMPT);
        promptPolishingMap.put(Pattern.compile("登录失败"), SYS_ERROR_DEFAULT_PROMPT);
        promptPolishingMap.put(Pattern.compile("风控拦截：多地开卡"), "多地开卡，请更换其他身份证及联系号码办理");
        promptPolishingMap.put(Pattern.compile("风控拦截：恶意地址"), "地址存在异常，请更换有效地址");
        promptPolishingMap.put(Pattern.compile("风控拦截：30天内同一证件、同一联电、同一收货地址下单超限"), "该信息30天内已有订单，请更换其他身份证、联系号码及地址办理");
        promptPolishingMap.put(Pattern.compile("风控拦截：待确认地址"), "您提供的地址信息待确认，请更换有效地址");
        promptPolishingMap.put(Pattern.compile("风控拦截：未填写详细地址"), "您还未填写详细地址，请更换有效地址");
        promptPolishingMap.put(Pattern.compile("风控拦截：证件90天内订单超过限制"), ID_CARD_LIMIT_PROMPT);
        promptPolishingMap.put(Pattern.compile("风控拦截:身份证号码和姓名不匹配"), "身份证号码和姓名不匹配，请修改");
        promptPolishingMap.put(Pattern.compile("确认单失败:号码\\d+的池为空"), "号码已被其他用户预定，请重新选择其他号码");
        promptPolishingMap.put(Pattern.compile("风控拦截：联系电话超限"), "联系电话多次下单，请更换联系号码");
        promptPolishingMap.put(Pattern.compile("风控拦截：风控校验未通过"), ID_CARD_ERROR_PROMPT);
        promptPolishingMap.put(Pattern.compile("预下单失败:资源ID和下单链接不能为空!"), "预下单失败:资源ID和下单链接不能为空!");
        promptPolishingMap.put(Pattern.compile("风控拦截：鹰眼涉诈涉扰"), ID_CARD_ERROR_PROMPT);
        promptPolishingMap.put(Pattern.compile("风控拦截：省分CB红牌用户"), ID_CARD_ERROR_PROMPT);
        promptPolishingMap.put(Pattern.compile("风控拦截：配送地址冲突"), "地址存在异常，请核对并更换有效地址");
        promptPolishingMap.put(Pattern.compile("确认单失败:服务异常"), SYS_ERROR_DEFAULT_PROMPT);
        promptPolishingMap.put(Pattern.compile("风控拦截：公安涉案"), ID_CARD_ERROR_PROMPT);
        promptPolishingMap.put(Pattern.compile(".*ZOP请求异常.*"), SYS_ERROR_DEFAULT_PROMPT);
        promptPolishingMap.put(Pattern.compile("风控拦截：请求超时，请稍后重试"), SYS_ERROR_DEFAULT_PROMPT);
        promptPolishingMap.put(Pattern.compile("预下单失败:服务异常"), "系统异常，请您稍后重新再试。");
        promptPolishingMap.put(Pattern.compile("风控拦截：电子围栏电话黑名单"), "电子围栏电话黑名单，请更换联系号码");
        promptPolishingMap.put(Pattern.compile("预下单失败:系统异常请联系管理员！"), "系统异常，请刷新页面重新下单");
        promptPolishingMap.put(Pattern.compile("风控拦截：配送地址完全一样"), "地址存在异常，请核对并更换有效地址");
        promptPolishingMap.put(Pattern.compile("确认单失败:系统异常！"), "系统异常，请刷新页面重新下单");
        promptPolishingMap.put(Pattern.compile("预下单失败:下单校验服务超时"), "下单校验服务超时，请刷新页面重新下单");
        promptPolishingMap.put(Pattern.compile("预下单失败"), "预下单失败，请刷新页面重新下单");
        promptPolishingMap.put(Pattern.compile("确认单失败:调用号卡系统超时，请稍后重试。"), "调用号卡系统超时，请稍后重试。");
        promptPolishingMap.put(Pattern.compile("风控拦截：服务调用超时,请稍后重试"), "服务调用超时,请稍后重试");
        promptPolishingMap.put(Pattern.compile("确认单失败:当前首月资费方式不存在1"), "确认单失败:当前首月资费方式不存在1");
        promptPolishingMap.put(Pattern.compile("确认订购结果异常"), "确认订购结果异常");
        promptPolishingMap.put(Pattern.compile("提交订单异常"), "提交订单异常");
        promptPolishingMap.put(Pattern.compile("验证身份证信息失败:此身份证名下号码存在异常，暂不支持线上购买，请前往线下营业厅咨询！"), ID_CARD_ERROR_PROMPT);
        promptPolishingMap.put(Pattern.compile("下单界面操作异常"), "下单界面操作异常，请刷新页面重新下单");

        // 我爱号码网络
        promptPolishingMap.put(Pattern.compile("证件年龄\\d+不满足办理需求；"), "证件年龄需满足18-60周岁，请更换符合要求的身份证办理");
        promptPolishingMap.put(Pattern.compile("接口1002错误：相同的证件号和联系电话7天内重复下单"), "该信息7天内已有订单，请更换其他身份证及联系号码办理");
        promptPolishingMap.put(Pattern.compile("正式订单同步失败：您的信息存在安全风险，暂不能下单，请联系10010！"), "您的信息存在安全风险，暂不能下单，请更换其他身份证办理");
        promptPolishingMap.put(Pattern.compile("接口1002错误：产品配置异常"), "请刷新页面重新下单");
        promptPolishingMap.put(Pattern.compile("正式订单同步失败：您的身份证信息未在公安系统登记，请您核对或更换证件信息。"), "您的身份证信息存在问题，请您核对或更换证件信息。");
        promptPolishingMap.put(Pattern.compile("应风控要(\\S+)地址暂不发货；"), "该地址目前暂无法发货，请更换地址。");
        promptPolishingMap.put(Pattern.compile("正式订单同步失败：对不起，您选择的号码已被预定，请重新选择号码！"), SELECTED_NUMBER_FAIL_PROMPT);
        promptPolishingMap.put(Pattern.compile("预订单同步失败：您的信息存在安全风险，暂不能线上办理，请联系10010"), "您的信息存在安全风险，暂不能下单，请更换其他身份证办理");

        // 广电
        promptPolishingMap.put(Pattern.compile(".*黑名单.*"), "您的身份证暂时无法下单，请更换其他身份证。");
        promptPolishingMap.put(Pattern.compile(".*身份证(\\S+)存在正在执行中的订单"), "您的身份证存在订单，请更换其他身份证下单");
        promptPolishingMap.put(Pattern.compile(".*收货地址目前无法配送.*"), "该地址目前暂无法发货，请更换地址。");
        promptPolishingMap.put(Pattern.compile(".*号码归属省份与邮寄地址不一致.*"), "号码归属省份与邮寄地址需一致，请您核对后重新下单");
        promptPolishingMap.put(Pattern.compile(".*需为同一人信息.*"), "姓名、身份证号与手机号需为同一人信息，请核对后重新下单");
        promptPolishingMap.put(Pattern.compile(".*收货地址长度不能超过.*"), "收货地址过长，请修改地址后重新下单");
        promptPolishingMap.put(Pattern.compile(".*号码未用表中不存在或状态不符合条件.*"), SELECTED_NUMBER_FAIL_PROMPT);
        promptPolishingMap.put(Pattern.compile(".*CC_10037.*"), "您的身份证办理号码超限，请更换其他身份证。");
        promptPolishingMap.put(Pattern.compile(".*CC_10008.*"), "实名认证过于频繁，请您稍后刷新重新尝试");
        promptPolishingMap.put(Pattern.compile(".*RES_NUMBER_INTF_00070.*"), "当前您已经预约3个号码，不能再次选号，请更换其他身份证及联系号码重新下单。");
        promptPolishingMap.put(Pattern.compile("联系号码信息未找到，请确认是否新办或已注销"), "联系号码为新办或已注销，请更换其他身份证及联系号码重新下单");
        promptPolishingMap.put(Pattern.compile(".*接口请求失败.*"), "接口请求失败，请您稍后刷新重新尝试");
        promptPolishingMap.put(Pattern.compile(".*RES_NUMBER_INTF_00049.*"), SELECTED_NUMBER_FAIL_PROMPT);
        promptPolishingMap.put(Pattern.compile(".*为维护您的信息和财产安全，您本次购买的号卡产品无法下单.*"), "您的身份证暂时无法下单，请更换其他身份证重新下单。");
        promptPolishingMap.put(Pattern.compile(".*ReadTimeoutException.*"), "提交订单失败，请您稍后刷新重新尝试");
        promptPolishingMap.put(Pattern.compile(".*日后可办理新入网业务。感谢您的理解.*"), "您的身份证暂时无法下单，请更换其他身份证。");
        promptPolishingMap.put(Pattern.compile(".*当前子套餐未在下单号码所在省份投放.*"), "提交订单失败，请您稍后刷新重新尝试");
        promptPolishingMap.put(Pattern.compile(".*系统正在维护中.*"), "系统正在维护中，请您稍后刷新重新尝试");

        // 卡赛
        promptPolishingMap.put(Pattern.compile(".*ORDR80201.*"), ID_CARD_ERROR_PROMPT);
        promptPolishingMap.put(Pattern.compile(".*ORDR80202.*"), ID_CARD_LIMIT_PROMPT);
        promptPolishingMap.put(Pattern.compile(".*ORDR80203.*"), "联系电话多次下单，请更换联系号码");
        promptPolishingMap.put(Pattern.compile(".*ORDR80204.*"), "联系电话存在问题，请更换联系号码");
        promptPolishingMap.put(Pattern.compile(".*ORDR80205.*"), ID_CARD_LIMIT_PROMPT);
        promptPolishingMap.put(Pattern.compile(".*ORDR80206.*"), "您的身份证暂时无法下单，请更换其他身份证。");
        promptPolishingMap.put(Pattern.compile(".*ORDR80207.*"), "号码归属省份与邮寄地址需一致，请您核对后重新下单");
        promptPolishingMap.put(Pattern.compile(".*ORDR80208.*"), ID_CARD_ERROR_PROMPT);
        promptPolishingMap.put(Pattern.compile(".*ORDR80209.*"), "商品不存在");
        promptPolishingMap.put(Pattern.compile(".*ORDR80210.*"), "地址存在异常，请更换有效地址");
        promptPolishingMap.put(Pattern.compile(".*ORDR80211.*"), "联系号码黑名单，请更换其他联系号码重新下单");
        promptPolishingMap.put(Pattern.compile(".*ORDR80212.*"), ID_CARD_ERROR_PROMPT);
        promptPolishingMap.put(Pattern.compile(".*ORDR80213.*"), "联系号码黑名单，请更换其他联系号码重新下单");
        promptPolishingMap.put(Pattern.compile(".*ORDR80214.*"), "号码类型黑名单，请更换其他号码重新下单");
        promptPolishingMap.put(Pattern.compile(".*ORDR80215.*"), "该地址目前暂无法发货，请更换地址。");
        promptPolishingMap.put(Pattern.compile(".*ORDR80216.*"), "您的身份证暂时无法下单，请更换其他身份证。");
        promptPolishingMap.put(Pattern.compile(".*ORDR80217.*"), "您的身份证暂时无法下单，请更换其他身份证。");
        promptPolishingMap.put(Pattern.compile(".*ORDR80218.*"), "您的身份证暂时无法下单，请更换其他身份证。");
        promptPolishingMap.put(Pattern.compile(".*ORDR80219.*"), "联系号码黑名单，请更换其他联系号码重新下单");
        promptPolishingMap.put(Pattern.compile(".*ORDR80220.*"), "联系号码黑名单，请更换其他联系号码重新下单");
        promptPolishingMap.put(Pattern.compile(".*ORDR80221.*"), ID_CARD_ERROR_PROMPT);
    }

    /**
     * 获取提示语
     *
     * @param prompt 提示
     * @return 提示语
     */
    public static String getPrompt(String prompt) {
        try {
            if (StringUtils.isBlank(prompt)) {
                return prompt;
            }
            for (Pattern pattern : promptPolishingMap.keySet()) {
                boolean contains = ReUtil.contains(pattern, prompt);
                if (contains) {
                    return promptPolishingMap.get(pattern);
                }
            }
        } catch (Exception e) {
            log.error("获取提示语异常", e);
        }
        log.info("未进行润色处理的提示语: {}", prompt);
        return prompt;
    }

}
