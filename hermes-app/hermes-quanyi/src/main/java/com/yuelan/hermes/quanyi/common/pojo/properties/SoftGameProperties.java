package com.yuelan.hermes.quanyi.common.pojo.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 软游通v2配置
 *
 * <AUTHOR> 2025/7/31
 * @since 2025/7/31
 */
@Data
@Component
@ConfigurationProperties(prefix = "soft-game")
public class SoftGameProperties {

    /**
     * 商户号
     */
    private String businessId = "Num15003";

    /**
     * 密钥、卡密解密密钥
     */
    private String key = "";

    /**
     * 订单提交接口地址
     */
    private String orderSubmitUrl = "https://supi.900sup.cn/Service/OrderReceive.ashx";

    /**
     * 订单查询接口地址
     */
    private String orderQueryUrl = "https://supq.900sup.cn/Service/CommOrderQry.ashx";

    /**
     * 异步通知回调地址
     */
    private String noticeUrl = "";

    /**
     * 是否启用沙箱环境
     */
    private boolean sandbox = false;

    /**
     * 沙箱环境配置
     */
    private SandboxConfig sandboxConfig = new SandboxConfig();

    /**
     * 获取当前环境的商户号
     */
    public String getCurrentBusinessId() {
        return sandbox ? sandboxConfig.getBusinessId() : businessId;
    }

    /**
     * 获取当前环境的密钥
     */
    public String getCurrentKey() {
        return sandbox ? sandboxConfig.getKey() : key;
    }

    /**
     * 获取当前环境的订单提交地址
     */
    public String getCurrentOrderSubmitUrl() {
        return sandbox ? sandboxConfig.getOrderSubmitUrl() : orderSubmitUrl;
    }

    /**
     * 获取当前环境的订单查询地址
     */
    public String getCurrentOrderQueryUrl() {
        return sandbox ? sandboxConfig.getOrderQueryUrl() : orderQueryUrl;
    }

    @Data
    public static class SandboxConfig {
        /**
         * 沙箱商户号
         */
        private String businessId = "Num10001";

        /**
         * 沙箱密钥
         */
        private String key = "86b67b65d54c4b64b3bb2db9818a02f2";

        /**
         * 沙箱订单提交地址
         */
        private String orderSubmitUrl = "http://suptest.900sup.cn:7102/Service/OrderReceive.ashx";

        /**
         * 沙箱订单查询地址
         */
        private String orderQueryUrl = "http://suptest.900sup.cn:7103/Service/CommOrderQry.ashx";

        /**
         * 测试商品ID - 返回失败
         */
        private String testFailGoodsId = "TestFail001jk";

        /**
         * 测试商品ID - 返回成功
         */
        private String testSuccessGoodsId = "TestSuc001jk";

        /**
         * 测试商品ID - 卡密测试
         */
        private String testCardGoodsId = "TestCami001jk";
    }
}
