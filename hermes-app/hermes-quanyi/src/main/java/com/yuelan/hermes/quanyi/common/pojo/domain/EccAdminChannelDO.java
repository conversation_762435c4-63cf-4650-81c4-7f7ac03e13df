package com.yuelan.hermes.quanyi.common.pojo.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.yuelan.hermes.quanyi.biz.handler.typeHandler.ListLongTypeHandler;
import com.yuelan.result.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 2024/6/7 上午10:57
 *  后台管理账号对应的可以查询的渠道
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "ecc_admin_channel", autoResultMap = true)
public class EccAdminChannelDO extends BaseDO implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 账号id
     */
    @TableField(value = "admin_id")
    private Long adminId;

    /**
     * 账号名字
     */
    @TableField(value = "admin_name")
    private String adminName;

    /**
     * 内部渠道和外部渠道共用渠道限制，内部渠道是id>700的不在一张表
     */
    @TableField(value = "channel_ids", typeHandler = ListLongTypeHandler.class)
    private List<Long> channelIds;

    @TableLogic
    private Integer deleted;


}