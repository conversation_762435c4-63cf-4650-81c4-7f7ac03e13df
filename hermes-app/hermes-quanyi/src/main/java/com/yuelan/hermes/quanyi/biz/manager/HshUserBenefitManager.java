package com.yuelan.hermes.quanyi.biz.manager;

import com.alibaba.fastjson2.JSONArray;
import com.yuelan.hermes.commons.enums.SendTypeEnum;
import com.yuelan.hermes.quanyi.biz.handler.CacheHandler;
import com.yuelan.hermes.quanyi.biz.handler.impl.PackageOrderStrategy;
import com.yuelan.hermes.quanyi.biz.service.*;
import com.yuelan.hermes.quanyi.common.enums.BenefitDispatchTimingEnum;
import com.yuelan.hermes.quanyi.common.enums.BenefitItemOrderStatus;
import com.yuelan.hermes.quanyi.common.enums.error.BenefitErrorEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.HshUserSessionUser;
import com.yuelan.hermes.quanyi.common.pojo.domain.*;
import com.yuelan.hermes.quanyi.controller.request.HshBenefitsPackageRedeemReq;
import com.yuelan.hermes.quanyi.controller.response.NChoseOneBenefitsResp;
import com.yuelan.hermes.quanyi.controller.response.UserBenefitItemStatusResp;
import com.yuelan.hermes.quanyi.controller.response.UserBenefitPackageOrderResp;
import com.yuelan.hermes.quanyi.controller.response.UserBenefitPackagesResp;
import com.yuelan.result.enums.YesOrNoEnum;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/7/30 下午7:44
 * 惠生活权益
 */
@Slf4j
@Component
@AllArgsConstructor
public class HshUserBenefitManager {
    private final BenefitsPackageService benefitsPackageService;
    private final BenefitsPackageOrderService benefitsPackageOrderService;
    private final BenefitItemService benefitItemService;
    private final BenefitItemOrderService benefitItemOrderService;
    private final CacheHandler cacheHandler;
    private final BenefitDispatchManager benefitDispatchManager;
    private final PackageOrderStrategy packageOrderStrategy;
    private final BenefitOrderExtensionService benefitOrderExtensionService;

    /**
     * 权益包订单查询
     *
     * @param sessionUser 用户信息
     * @param packageCode 权益包唯一编码
     */
    public UserBenefitPackageOrderResp queryPackageOrder(HshUserSessionUser sessionUser, String packageCode) {
        BenefitsPackageDO packageDO = benefitsPackageService.getAvailablePackageAndItems(packageCode);
        // 查询最后一条未过期的权益包订单（但是可能是退款状态）
        BenefitsPackageOrderDO packageOrder = benefitsPackageOrderService.findLastPackageOrder(sessionUser.getPhone(), packageDO.getPackageId());
        List<Long> optionalIds = JSONArray.parseArray(packageOrder.getOptionalIds(), Long.class);
        List<Long> instantIds = JSONArray.parseArray(packageOrder.getInstantIds(), Long.class);
        List<BenefitItemDO> optionalItems = benefitItemService.listUpItemsByIds(optionalIds);
        List<BenefitItemDO> instantItems = benefitItemService.listUpItemsByIds(instantIds);
        List<BenefitItemOrderDO> itemOrders = benefitItemOrderService.listByExtensionId(packageOrder.getExtensionId());
        return buildResp(packageOrder, optionalItems, instantItems, itemOrders);
    }

    /**
     * @param packageOrder  订单
     * @param optionalItems 可选权益集合（订单的）非实时查询的关联关系
     * @param instantItems  及时下发的全集集合（订单的）非实时查询的关联关系
     * @param itemOrders    和这个权益包订单有关权益订单记录
     */
    private UserBenefitPackageOrderResp buildResp(BenefitsPackageOrderDO packageOrder, List<BenefitItemDO> optionalItems, List<BenefitItemDO> instantItems, List<BenefitItemOrderDO> itemOrders) {
        UserBenefitPackageOrderResp resp = new UserBenefitPackageOrderResp();
        resp.setRefund(YesOrNoEnum.isYes(packageOrder.getIsRefunded()));
        boolean isExpired = packageOrder.getRedemptionDeadline().isBefore(LocalDateTime.now());
        resp.setExpired(isExpired);
        resp.setExpiredTime(packageOrder.getRedemptionDeadline());
        resp.setOrderTime(packageOrder.getCreateTime());

        Map<Long, List<BenefitItemOrderDO>> itemId2OrdersMap = itemOrders.stream()
                .collect(Collectors.groupingBy(BenefitItemOrderDO::getBenefitItemId));

        // 处理即时发放权益
        List<UserBenefitItemStatusResp> instantBenefitsStatus = instantItems.stream()
                .map(item -> buildUserBenefitItemStatusResp(item, itemId2OrdersMap))
                .collect(Collectors.toList());
        resp.setInstantBenefits(instantBenefitsStatus);

        // 处理N选1权益
        List<UserBenefitItemStatusResp> nChoseOneBenefits = optionalItems.stream()
                .map(item -> buildUserBenefitItemStatusResp(item, itemId2OrdersMap))
                .collect(Collectors.toList());

        // 计算N选1剩余可选数量
        long nChoseOneReceivedOrDispatchingCount = nChoseOneBenefits.stream()
                .map(statusResp -> GetStatusEnum.getByCode(statusResp.getGetStatus()))
                .filter(statusEnum -> GetStatusEnum.DISPATCHING.equals(statusEnum) || GetStatusEnum.RECEIVED.equals(statusEnum))
                .count();

        int remainCount = packageOrder.getMaxSelectable() - (int) nChoseOneReceivedOrDispatchingCount;

        NChoseOneBenefitsResp nChoseOneBenefitsResp = new NChoseOneBenefitsResp();
        nChoseOneBenefitsResp.setMaxSelectable(packageOrder.getMaxSelectable());
        nChoseOneBenefitsResp.setRemainCount(remainCount);
        nChoseOneBenefitsResp.setBenefits(nChoseOneBenefits);
        resp.setChoseBenefits(nChoseOneBenefitsResp);

        return resp;
    }

    /**
     * 构建单个权益项的状态响应对象
     *
     * @param benefitItem      权益项信息
     * @param itemId2OrdersMap 权益项ID到订单列表的映射
     * @return 权益项状态响应对象
     */
    private UserBenefitItemStatusResp buildUserBenefitItemStatusResp(BenefitItemDO benefitItem, Map<Long, List<BenefitItemOrderDO>> itemId2OrdersMap) {
        UserBenefitItemStatusResp itemStatusResp = new UserBenefitItemStatusResp();
        itemStatusResp.setBenefitItemId(benefitItem.getBenefitItemId());
        itemStatusResp.setBenefitItemName(benefitItem.getBenefitName());
        itemStatusResp.setBenefitItemIcon(benefitItem.getItemImg());

        List<BenefitItemOrderDO> itemOrderDOList = itemId2OrdersMap.getOrDefault(benefitItem.getBenefitItemId(), new ArrayList<>());

        BenefitItemOrderDO successOrDispatchingOrder = null;
        GetStatusEnum getStatusEnum = GetStatusEnum.NOT_RECEIVED;

        // 使用迭代器遍历
        for (BenefitItemOrderDO benefitItemOrderDO : itemOrderDOList) {
            BenefitItemOrderStatus itemStatus = BenefitItemOrderStatus.getByCode(benefitItemOrderDO.getOrderStatus());
            if (BenefitItemOrderStatus.SUCCESS == itemStatus) {
                successOrDispatchingOrder = benefitItemOrderDO;
                getStatusEnum = GetStatusEnum.RECEIVED;
                break;
            } else if (BenefitItemOrderStatus.DISPATCHING == itemStatus) {
                successOrDispatchingOrder = benefitItemOrderDO;
                getStatusEnum = GetStatusEnum.DISPATCHING;
                break;
            }
        }

        itemStatusResp.setGetStatus(getStatusEnum.getCode());

        if (successOrDispatchingOrder != null) {
            itemStatusResp.setDeliveryType(successOrDispatchingOrder.getDeliveryType());
            itemStatusResp.setRedeemCode(successOrDispatchingOrder.getRedeemCode());
            itemStatusResp.setRedeemCodePwd(successOrDispatchingOrder.getRedeemCodePwd());
            itemStatusResp.setRedeemCodeExpiredTime(successOrDispatchingOrder.getRedeemCodeExpireTime());
            // 从list中移除 算是被这个订单消费了 (注意：这里直接修改传入的Map中的List)
            // 如果不希望修改原始Map，需要先复制List
            itemOrderDOList.remove(successOrDispatchingOrder);
        }

        return itemStatusResp;
    }

    /**
     * 查询用户订购过哪些权益包
     *
     * @return 权益包简单信息
     */
    public List<UserBenefitPackagesResp> userPackages(HshUserSessionUser sessionUser) {
        List<BenefitsPackageOrderDO> packageOrder = benefitsPackageOrderService.selectUserPackageOrder(sessionUser.getPhone());

        List<UserBenefitPackagesResp> respList = new ArrayList<>();
        List<Long> packageIds = new ArrayList<>();
        for (BenefitsPackageOrderDO orderDO : packageOrder) {
            if (packageIds.contains(orderDO.getPackageId())) {
                continue;
            }
            packageIds.add(orderDO.getPackageId());

            UserBenefitPackagesResp resp = new UserBenefitPackagesResp();
            resp.setPackageCode(orderDO.getPackageCode());
            resp.setPackageName(orderDO.getPackageName());
            respList.add(resp);
        }
        return respList;
    }


    /**
     * 选发权益领取
     *
     * @param sessionUser 用户信息
     * @param req         领取请求
     * @return 权益领取状态
     */
    public UserBenefitItemStatusResp redeem(HshUserSessionUser sessionUser, HshBenefitsPackageRedeemReq req) {
        validateRedeemRequest(req);
        Long benefitItemId = req.getBenefitItemId();
        Long packageOrderId = req.getPackageOrderId();
        Integer dispatchTiming = req.getDispatchTiming();
        String userPhone = sessionUser.getPhone();

        RLock rlock = cacheHandler.getUserOperatorBenefitLock(userPhone);
        BizException.assertCheck(Objects.nonNull(rlock), BenefitErrorEnum.ORDER_PROCESSING, "您点的太快了，请刷新后再试");

        try {
            BenefitsPackageOrderDO packageOrder = getAndCheckPackageOrder(userPhone, packageOrderId);
            BenefitItemDO benefitItemDO = benefitItemService.getById(benefitItemId);
            BizException.assertCheck(Objects.nonNull(benefitItemDO), BenefitErrorEnum.REDEEM_ERROR, "领取失败：权益信息不存在");

            List<BenefitItemOrderDO> userAllBenefitsOrder = benefitItemOrderService.listByExtensionId(packageOrder.getExtensionId());
            BenefitDispatchTimingEnum dispatchTimingEnum = BenefitDispatchTimingEnum.getByCode(dispatchTiming);
            BenefitItemOrderDO lastOrder;

            if (BenefitDispatchTimingEnum.INSTANT.equals(dispatchTimingEnum)) {
                lastOrder = handleInstantDispatch(packageOrder, benefitItemId, userAllBenefitsOrder, dispatchTiming);
            } else if (BenefitDispatchTimingEnum.USER_SELECT.equals(dispatchTimingEnum)) {
                lastOrder = handleUserSelectDispatch(packageOrder, benefitItemDO, benefitItemId, userAllBenefitsOrder, dispatchTiming);
            } else {
                throw BizException.create(BenefitErrorEnum.REDEEM_ERROR, "领取失败：无效的发放时机");
            }

            return buildRedeemResponse(benefitItemDO, lastOrder);

        } catch (BizException ex) {
            throw ex;
        } catch (Exception e) {
            log.error("用户领取权益包权益异常, packageOrderId={}, benefitItemId={}, phone={}", packageOrderId, benefitItemId, userPhone, e);
            throw BizException.create(BenefitErrorEnum.REDEEM_ERROR, "领取失败：系统错误");
        } finally {
            cacheHandler.releaseLock(rlock);
        }
    }


    /**
     * 比如有补单的情况下会，返回id=3的这条记录结果展示给用户
     * ID=1 原始订单 失败
     * ID=2 补单 原始订单id =1  失败
     * ID=3 补单 原始订单id =1  返回这条
     */
    private BenefitItemOrderDO getLastOrder(List<BenefitItemOrderDO> userAllBenefitsOrder, Long benefitItemId, Integer dispatchTiming) {
        List<BenefitItemOrderDO> orders = userAllBenefitsOrder.stream()
                .filter(item -> item.getBenefitItemId().equals(benefitItemId) && item.getDispatchTiming().equals(dispatchTiming))
                .collect(Collectors.toList());
        BenefitItemOrderDO original = null;
        BenefitItemOrderDO reissue = null;
        for (BenefitItemOrderDO order : orders) {
            if (SendTypeEnum.ORIGINAL_ORDER.getType().equals(order.getOrderType())) {
                original = order;
                break;
            }
        }
        if (original != null) {
            // 倒序排序 非常重要
            orders.sort(Comparator.comparingLong(BenefitItemOrderDO::getItemOrderId).reversed());
            for (BenefitItemOrderDO order : orders) {
                if (order.getOriginalItemOrderId().equals(original.getItemOrderId())) {
                    reissue = order;
                    break;
                }
            }
        }
        return reissue != null ? reissue : original;
    }

    /**
     * 校验领取请求参数
     */
    private void validateRedeemRequest(HshBenefitsPackageRedeemReq req) {
        if (Objects.isNull(req.getBenefitItemId()) || Objects.isNull(req.getPackageOrderId()) || Objects.isNull(req.getDispatchTiming())) {
            throw BizException.create(BaseErrorCodeEnum.INTERNAL_SERVER_ERROR, "领取失败：缺少参数");
        }
    }

    /**
     * 获取并检查权益包订单
     */
    private BenefitsPackageOrderDO getAndCheckPackageOrder(String phone, Long packageOrderId) {
        BenefitsPackageOrderDO packageOrder = benefitsPackageOrderService.getByIdAndPhone(phone, packageOrderId);
        BizException.assertCheck(Objects.nonNull(packageOrder), BenefitErrorEnum.ORDER_NOT_FOUND, "领取失败：订单不存在");
        if (YesOrNoEnum.YES.getCode().equals(packageOrder.getIsRefunded())) {
            throw BizException.create(BenefitErrorEnum.REDEEM_ERROR, "领取失败：已退款订单不可领取");
        }
        if (YesOrNoEnum.NO.getCode().equals(packageOrder.getIsValid())) {
            throw BizException.create(BenefitErrorEnum.ORDER_NOT_FOUND, "领取失败：订单已失效请重新刷新页面");
        }
        if (packageOrder.getRedemptionDeadline().isBefore(LocalDateTime.now())) {
            throw BizException.create(BenefitErrorEnum.REDEEM_ERROR, "领取失败：订单已过期");
        }
        return packageOrder;
    }

    /**
     * 处理即时发放权益
     */
    private BenefitItemOrderDO handleInstantDispatch(BenefitsPackageOrderDO packageOrder, Long benefitItemId, List<BenefitItemOrderDO> userAllBenefitsOrder, Integer dispatchTiming) {
        List<Long> instantIds = JSONArray.parseArray(packageOrder.getInstantIds(), Long.class);
        if (!instantIds.contains(benefitItemId)) {
            throw BizException.create(BenefitErrorEnum.REDEEM_ERROR, "领取失败：权益不存在于即时发放列表");
        }
        BenefitItemOrderDO lastOrder = getLastOrder(userAllBenefitsOrder, benefitItemId, dispatchTiming);
        // 即时发放理论上应该已经有订单了
        BizException.assertCheck(Objects.nonNull(lastOrder), BenefitErrorEnum.REDEEM_ERROR, "领取失败：未找到即时发放权益订单");
        checkOrderStatusFail(lastOrder);
        return lastOrder;
    }

    /**
     * 处理用户选择发放权益
     */
    private BenefitItemOrderDO handleUserSelectDispatch(BenefitsPackageOrderDO packageOrder, BenefitItemDO benefitItemDO, Long benefitItemId, List<BenefitItemOrderDO> userAllBenefitsOrder, Integer dispatchTiming) {
        List<Long> optionalIds = JSONArray.parseArray(packageOrder.getOptionalIds(), Long.class);
        if (!optionalIds.contains(benefitItemId)) {
            throw BizException.create(BenefitErrorEnum.REDEEM_ERROR, "领取失败：权益不存在于可选列表");
        }

        BenefitItemOrderDO lastOrder = getLastOrder(userAllBenefitsOrder, benefitItemId, dispatchTiming);

        if (Objects.isNull(lastOrder)) {
            // 检查是否超出可选数量限制
            checkMultiChoseOneLimit(packageOrder, userAllBenefitsOrder);
            // 如果没有订单，则创建新订单并发放
            BenefitOrderExtensionDO orderExtensionDO = benefitOrderExtensionService.getById(packageOrder.getExtensionId());
            benefitItemDO.setDispatchTiming(dispatchTiming);
            BenefitItemOrderDO newOrder = packageOrderStrategy.buildBenefitItemOrderList(orderExtensionDO, packageOrder, Collections.singletonList(benefitItemDO)).get(0);
            benefitItemOrderService.save(newOrder);
            benefitDispatchManager.tryDispatch(newOrder);
            lastOrder = benefitItemOrderService.getById(newOrder.getItemOrderId());
            BizException.assertCheck(Objects.nonNull(lastOrder), BenefitErrorEnum.REDEEM_ERROR, "领取失败：创建订单后查询失败");
        }
        checkOrderStatusFail(lastOrder);
        return lastOrder;
    }

    /**
     * 检查N选1权益是否超出可选数量限制
     */
    private void checkMultiChoseOneLimit(BenefitsPackageOrderDO packageOrder, List<BenefitItemOrderDO> userAllBenefitsOrder) {
        List<Long> optionalIds = JSONArray.parseArray(packageOrder.getOptionalIds(), Long.class);
        long receivedOrDispatchingCount = userAllBenefitsOrder.stream()
                // 只关心可选权益的订单
                .filter(order -> optionalIds.contains(order.getBenefitItemId()))
                .filter(order -> BenefitItemOrderStatus.SUCCESS.getCode().equals(order.getOrderStatus()) || BenefitItemOrderStatus.DISPATCHING.getCode().equals(order.getOrderStatus()))
                .map(BenefitItemOrderDO::getBenefitItemId)
                .distinct() // 按权益项去重，防止补单导致计数错误
                .count();

        if (receivedOrDispatchingCount >= packageOrder.getMaxSelectable()) {
            throw BizException.create(BenefitErrorEnum.REDEEM_ERROR, "领取失败：已达最大可选数量");
        }
    }

    /**
     * 检查订单状态是否失败
     */
    private void checkOrderStatusFail(BenefitItemOrderDO order) {
        if (BenefitItemOrderStatus.FAIL.getCode().equals(order.getOrderStatus())) {
            throw BizException.create(BenefitErrorEnum.REDEEM_ERROR, "领取失败：供应商下单失败");
        }
    }

    /**
     * 构建领取响应
     */
    private UserBenefitItemStatusResp buildRedeemResponse(BenefitItemDO benefitItemDO, BenefitItemOrderDO lastOrder) {
        UserBenefitItemStatusResp resp = new UserBenefitItemStatusResp();
        resp.setBenefitItemId(benefitItemDO.getBenefitItemId());
        resp.setBenefitItemName(benefitItemDO.getBenefitName());
        resp.setBenefitItemIcon(benefitItemDO.getItemImg());
        resp.setDeliveryType(lastOrder.getDeliveryType());
        //
        GetStatusEnum getStatusEnum = GetStatusEnum.NOT_RECEIVED;
        BenefitItemOrderStatus itemStatus = BenefitItemOrderStatus.getByCode(lastOrder.getOrderStatus());

        if (BenefitItemOrderStatus.SUCCESS == itemStatus) {
            getStatusEnum = GetStatusEnum.RECEIVED;
        } else if (BenefitItemOrderStatus.DISPATCHING == itemStatus) {
            getStatusEnum = GetStatusEnum.DISPATCHING;
        } else if (BenefitItemOrderStatus.FAIL == itemStatus) {
            // 这个检查理论上在调用此方法前已经处理，但为了健壮性再加一层
            log.warn("构建响应时发现订单状态为失败, orderId={}", lastOrder.getItemOrderId());
            throw BizException.create(BenefitErrorEnum.REDEEM_ERROR, "领取失败：供应商处理失败");
        }

        resp.setGetStatus(getStatusEnum.getCode());
        resp.setRedeemCode(lastOrder.getRedeemCode());
        resp.setRedeemCodePwd(lastOrder.getRedeemCodePwd());
        resp.setRedeemCodeExpiredTime(lastOrder.getRedeemCodeExpireTime());
        return resp;
    }

    @AllArgsConstructor
    @Getter
    private static enum GetStatusEnum {
        // 领取状态：0-未领取 1-下发中 2-已经领
        NOT_RECEIVED(0, "未领取"),
        DISPATCHING(1, "下发中"),
        RECEIVED(2, "已领取"),
        ;
        private final Integer code;
        private final String desc;

        public static GetStatusEnum getByCode(Integer code) {
            for (GetStatusEnum value : GetStatusEnum.values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return null;
        }
    }


}
