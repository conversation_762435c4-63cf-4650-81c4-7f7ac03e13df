package com.yuelan.hermes.quanyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 2024/7/10 上午9:38
 */
@AllArgsConstructor
@Getter
public enum FileExportTaskCodeEnum {

    BENEFIT_ORDER_EXPORT("BOE", "权益包订单导出"),
    BENEFIT_ORDER_ITEM_EXPORT("BOIE", "权益商品订单导出"),
    ECC_ORDER_EXPORT("EOE", "电商卡订单导出"),
    ECC_GD_ORDER_EXPORT("EGOE", "广电卡订单导出"),
    ECC_NC_ORDER_EXPORT("ENOE", "号卡订单导出"),
    E_SPORT_ORDER_EXPORT("ESOE", "电竞卡订单导出"),
    BENEFIT_ITEM_ORDER_EXPORT("BIOE", "权益订单导出"),
    BENEFIT_PACKAGE_ORDER_EXPORT("BPOE", "权益组合包订单导出"),
    E_SPORT_ORDER_ITEM_EXPORT("ESOIE", "电竞商品订单导出"),
    THIRD_ORDER_EXPORT("TOE", "第三方订单导出"),

    ;
    private final String code;
    private final String desc;

    public static FileExportTaskCodeEnum of(String code) {
        for (FileExportTaskCodeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }


}
