package com.yuelan.hermes.quanyi.common.util.ningxia;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

public class OrderTool {
    // 使用原子计数器确保线程安全
    private static final AtomicLong counter = new AtomicLong(0);
    // 上次生成订单号的时间戳
    private static volatile String lastTimestamp = "";

    /**
     * 创建订单号
     *
     * @return
     */
    public static synchronized String generateOrderNumber(String channelCode) {
        // 获取当前时间戳（精确到毫秒）
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));

        // 如果同一毫秒内多次生成，增加序列号
        String sequence;
        if (timestamp.equals(lastTimestamp)) {
            sequence = String.format("%03d", counter.incrementAndGet() % 1000);
        } else {
            counter.set(0);
            sequence = "000";
            lastTimestamp = timestamp;
        }

        // 组合各部分生成订单号
        String orderNumber = channelCode + timestamp + sequence;

        // 确保长度为32位，不足部分用随机数填充
        if (orderNumber.length() < 32) {
            orderNumber += generateRandomDigits(32 - orderNumber.length());
        }

        return orderNumber;
    }

    // 生成指定位数的随机数字
    private static String generateRandomDigits(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append((int) (Math.random() * 10));
        }
        return sb.toString();
    }

}
