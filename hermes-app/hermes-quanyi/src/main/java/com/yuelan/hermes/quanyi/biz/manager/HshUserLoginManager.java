package com.yuelan.hermes.quanyi.biz.manager;

import cn.dev33.satoken.session.SaSession;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.boot.utils.HttpServletRequestUtil;
import com.yuelan.hermes.commons.enums.SmsCodeType;
import com.yuelan.hermes.commons.enums.SocialTypeEnum;
import com.yuelan.hermes.commons.enums.WxMpScopeEnum;
import com.yuelan.hermes.quanyi.biz.service.AppConnectDOService;
import com.yuelan.hermes.quanyi.biz.service.SmsCodeService;
import com.yuelan.hermes.quanyi.biz.service.UserDOService;
import com.yuelan.hermes.quanyi.common.pojo.bo.HshUserSessionUser;
import com.yuelan.hermes.quanyi.common.pojo.domain.AppConnectDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.UserDO;
import com.yuelan.hermes.quanyi.config.WxConfig;
import com.yuelan.hermes.quanyi.config.satoken.StpHshUserUtil;
import com.yuelan.hermes.quanyi.controller.request.HshSmsLoginReq;
import com.yuelan.hermes.quanyi.controller.request.HshUserBindReq;
import com.yuelan.hermes.quanyi.controller.request.WxCodeReq;
import com.yuelan.hermes.quanyi.controller.response.MpLoginResp;
import com.yuelan.hermes.quanyi.controller.response.UserAuthResp;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> 2024/7/27 下午3:27
 */
@Component
@AllArgsConstructor
public class HshUserLoginManager {

    private final WxConfig wxConfig;
    private final AppConnectDOService appConnectDOService;
    private final UserDOService userDOService;
    private final SmsCodeService smsCodeService;

    /**
     * 公众号登陆
     */
    public Object mpLogin(WxCodeReq req) throws WxErrorException {
        Integer SocialType = SocialTypeEnum.MP.getValue();
        String code = req.getCode();
        WxMpScopeEnum wxMpScopeEnum = WxMpScopeEnum.of(req.getScope());
        // 授权登录
        WxOAuth2AccessToken wxAccessToken = wxConfig.getWxMpService().getOAuth2Service().getAccessToken(code);
        String openId = wxAccessToken.getOpenId();
        String unionId = wxAccessToken.getUnionId();
        // 判断是否关注了
        WxMpUser wxMpUser = wxConfig.getWxMpService().getUserService().userInfo(openId);
        Boolean subscribe = wxMpUser.getSubscribe();
        // 查询头像/昵称信息信息
        WxOAuth2UserInfo wxUserInfo = null;
        if (WxMpScopeEnum.SNSAPI_USERINFO.equals(wxMpScopeEnum)) {
            wxUserInfo = wxConfig.getWxMpService().getOAuth2Service().getUserInfo(wxAccessToken, null);
        }
        // 查询数据库
        AppConnectDO appConnectDO = appConnectDOService.getByBizUserIdAndSocialType(openId, SocialType);
        if (Objects.isNull(appConnectDO)) {
            appConnectDO = new AppConnectDO();
            appConnectDO.setSocialType(SocialType);
            appConnectDO.setBizUserId(openId);
            appConnectDO.setBizUnionid(unionId);
            appConnectDO.setTempToken(IdUtil.fastSimpleUUID());
            setProfileInfo(appConnectDO, wxUserInfo);
            appConnectDOService.save(appConnectDO);
        } else {
            appConnectDO.setBizUnionid(unionId);
            setProfileInfo(appConnectDO, wxUserInfo);
            appConnectDOService.updateById(appConnectDO);
        }
        Long userId = appConnectDO.getUserId();
        // 绑定完手机才会有userId
        if (Objects.nonNull(userId)) {
            UserDO userDO = userDOService.getById(userId);
            return loginAndReturn(userDO);
        }
        return mpLoginResp(appConnectDO, subscribe);
    }


    /**
     * 发送 惠生活 验证短信
     */
    public void sendHshSms(String phone) {
        if (isDevTestPhone(phone)) {
            return;
        }
        smsCodeService.reqCaptcha(phone, SmsCodeType.HSH_USER_VERIFY);
    }

    /**
     * 是否是测试环境的测试号码
     * 后 9 位一样的号码不会下发验证码 任意验证码登录
     */
    private boolean isDevTestPhone(String phone) {
        if (!AppConstants.isReal()) {
            // 测试环境号码后 9 位一样的号码不会下发验证码 任意验证码登录
            String phoneLast9 = StrUtil.sub(phone, 2, phone.length());
            boolean isTestPhone = true;
            for (int i = 0; i < phoneLast9.length(); i++) {
                if (phoneLast9.charAt(i) != phoneLast9.charAt(0)) {
                    isTestPhone = false;
                    break;
                }
            }
            return isTestPhone;
        }
        return false;
    }

    /**
     * 绑定/并且登录
     */
    @Transactional(rollbackFor = Exception.class)
    public UserAuthResp bindAndLogin(HshUserBindReq req) {
        String phone = req.getPhone();
        String smsCode = req.getSmsCode();
        AppConnectDO appConnectDO = appConnectDOService.getByTempToken(req.getTempToken());
        if (Objects.isNull(appConnectDO)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "未查询到用户");
        }
        Long userId = appConnectDO.getUserId();
        if (Objects.nonNull(userId)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "已绑定过号码，请先解绑");
        }
        // 不是测试环境的测试号码都需要验证
        if (!isDevTestPhone(phone)) {
            smsCodeService.verifyCaptcha(phone, smsCode, SmsCodeType.HSH_USER_VERIFY);
        }
        UserDO phoneBindUser = userDOService.findByMobile(req.getPhone());
        if (Objects.nonNull(phoneBindUser)) {
            AppConnectDO phoneBindConnectDo = appConnectDOService.getByHshUserId(phoneBindUser.getUserId());
            if (Objects.nonNull(phoneBindConnectDo)) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "该手机号已绑定其他账号");
            }
        } else {
            // 插入数据库绑定用户
            phoneBindUser = createAndSaveNewHshUser(phone);
        }
        // 更新关联用户id
        AppConnectDO updateDO = new AppConnectDO();
        updateDO.setId(appConnectDO.getId());
        updateDO.setUserId(phoneBindUser.getUserId());
        appConnectDOService.updateById(updateDO);
        return loginAndReturn(phoneBindUser);
    }


    public UserAuthResp smsLogin(HshSmsLoginReq req) {
        String phone = req.getPhone();
        String smsCode = req.getSmsCode();
        smsCodeService.verifyCaptcha(phone, smsCode, SmsCodeType.HSH_USER_VERIFY);
        UserDO userDO = userDOService.findByMobile(req.getPhone());
        if (Objects.isNull(userDO)) {
            userDO = createAndSaveNewHshUser(phone);
        }
        return loginAndReturn(userDO);
    }


    private UserDO createAndSaveNewHshUser(String phone) {
        UserDO userDO = new UserDO();
        // 手机尾号
        String phoneLast4 = phone.substring(7);
        userDO.setNickName("惠生活会员" + phoneLast4);
        userDO.setUserPhone(phone);
        userDO.setRegIp(ServletUtil.getClientIP(HttpServletRequestUtil.getCurrentRequest()));
        userDO.setCreateTime(new Date());
        userDOService.save(userDO);
        return userDO;
    }


    /**
     * 解除绑定
     */
    public void unbindPhone() {
        long userId = StpHshUserUtil.getLoginIdAsLong();
        AppConnectDO phoneBindConnectDo = appConnectDOService.getByHshUserId(userId);
        Boolean b = appConnectDOService.resetUserIdNull(phoneBindConnectDo.getId());
        if (!b) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "系统异常重置失败");
        }
        StpHshUserUtil.logout(userId);
    }


    /**
     * jwtToken登录
     */
    public UserAuthResp tokenLogin() {
        long userId = StpHshUserUtil.getLoginIdAsLong();
        // 自动续期了
        String jwtToken = StpHshUserUtil.getTokenValue();
        UserDO user = userDOService.getById(userId);
        return new UserAuthResp()
                .setNickName(user.getNickName())
                .setPhone(user.getUserPhone())
                .setJwtToken(jwtToken);
    }

    /**
     * jwtToken退出
     */
    public void logOut() {
        StpHshUserUtil.logout();
    }

    /**
     * 设置用户信息
     */
    private void setProfileInfo(AppConnectDO appConnectDO, WxOAuth2UserInfo wxUserInfo) {
        if (Objects.isNull(wxUserInfo)) {
            return;
        }
        appConnectDO.setNickName(wxUserInfo.getNickname());
        appConnectDO.setImageUrl(wxUserInfo.getHeadImgUrl());
        appConnectDO.setBizUserId(wxUserInfo.getOpenid());
        if (Objects.nonNull(wxUserInfo.getUnionId())) {
            appConnectDO.setBizUnionid(wxUserInfo.getUnionId());
        }
    }


    /**
     * 已经绑定用户的返回
     *
     * @param user 用户对象
     */
    private UserAuthResp loginAndReturn(UserDO user) {
        Long userId = user.getUserId();
        StpHshUserUtil.login(userId);
        StpHshUserUtil.getSession().set(SaSession.USER, HshUserSessionUser.buildSessionUser(user));
        String jwtToken = StpHshUserUtil.getTokenValue();
        return new UserAuthResp()
                .setNickName(user.getNickName())
                .setPhone(user.getUserPhone())
                .setJwtToken(jwtToken);
    }

    /**
     * 未绑定用户的返回
     */
    private MpLoginResp mpLoginResp(AppConnectDO appConnectDO, Boolean subscribe) {
        return new MpLoginResp()
                .setTempToken(appConnectDO.getTempToken())
                .setSubscribe(subscribe);
    }


}
