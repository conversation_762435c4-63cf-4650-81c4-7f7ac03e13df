package com.yuelan.hermes.quanyi.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccImeiDO;
import com.yuelan.hermes.quanyi.controller.request.EccImeiReq;
import com.yuelan.hermes.quanyi.controller.response.EccImeiResp;
import com.yuelan.hermes.quanyi.controller.response.ImportResp;
import com.yuelan.result.entity.PageData;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 2025/6/17
 * @since 2025/6/17
 */
public interface EccImeiService extends IService<EccImeiDO> {
    /**
     * 导入ECC设备IMEI
     *
     * @param file       文件
     * @param channelId  渠道id
     * @param productId  号卡产品 id
     * @param deviceInfo 设备信息
     */
    ImportResp importEccImei(MultipartFile file,
                             Integer channelType,
                             Long channelId,
                             Long productId,
                             String deviceInfo
    ) throws IOException;

    /**
     * 分页查询
     */
    PageData<EccImeiResp> pageEccImei(EccImeiReq req);

    void downloadTemplate(HttpServletResponse response) throws IOException;

    EccImeiDO getByImei(String imei);

    /**
     * 更新IMEI的发货时间
     *
     * @param imei         IMEI号
     * @param deliveryTime 发货时间
     */
    void updateDeliveryTimeByImei(@NotEmpty(message = "IMEI号不能为空") String imei, LocalDateTime deliveryTime);
}
