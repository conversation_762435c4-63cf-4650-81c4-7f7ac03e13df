package com.yuelan.hermes.quanyi.controller.response;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.yuelan.hermes.commons.excel.LocalDateTimeConverter;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccNcOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.excel.converter.EccCardStatusConverter;
import com.yuelan.hermes.quanyi.common.pojo.excel.converter.EccGdOrderStatusConverter;
import com.yuelan.hermes.quanyi.common.pojo.excel.converter.ExpressStatusConverter;
import com.yuelan.hermes.quanyi.common.pojo.excel.converter.PhoneNumSelectTypeConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * EccNcOrderResp
 *
 * <AUTHOR>
 */
@Data
@ContentRowHeight(15)
@HeadRowHeight(20)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class EccNcOrderResp {

    // 字段名常量，用于权限控制
    public static final String FIELD_DEDUCTION_CHANNEL_LEVEL = "deductionChannelLevel";
    public static final String FIELD_DEDUCTION_CHANNEL_ID = "deductionChannelId";
    public static final String FIELD_DEDUCTION_CHANNEL_NAME = "deductionChannelName";

    @Schema(description = "主键id")
    @ColumnWidth(20)
    @ExcelProperty("主键id")
    private Long orderId;

    @Schema(description = "我方订单号")
    @ColumnWidth(20)
    @ExcelProperty("我方订单号")
    private String orderNo;

    @Schema(description = "渠道订单号")
    @ColumnWidth(20)
    @ExcelProperty("渠道订单号")
    private String channelOrderNo;

    @Schema(description = "服务提供商订单号")
    @ColumnWidth(20)
    @ExcelProperty("服务提供商订单号")
    private String spOrderNo;

    @Schema(description = "原始订单号（补发类型订单特有）")
    @ColumnWidth(20)
    @ExcelProperty("原始订单号（补发类型订单特有）")
    private String originalOrderNo;

    @Schema(description = "运营商")
    @ColumnWidth(20)
    @ExcelProperty("运营商")
    private Integer operator;

    @Schema(description = "我方号卡产品id")
    @ColumnWidth(20)
    @ExcelProperty("我方号卡产品id")
    private Long prodId;

    @Schema(description = "权益包商品名字")
    @ColumnWidth(20)
    @ExcelProperty("权益包商品名字")
    private String prodName;

    @Schema(description = "推广渠道id")
    @ColumnWidth(20)
    @ExcelProperty("推广渠道id")
    private Long channelId;

    @Schema(description = "推广渠道类型:1-内部渠道，2-外部渠道")
    @ColumnWidth(20)
    @ExcelProperty("推广渠道类型")
    private Integer channelType;

    @Schema(description = "推广渠道名字")
    @ColumnWidth(20)
    @ExcelProperty("推广渠道名字")
    private String channelName;


    /**
     * 扣量渠道等级
     */
    @Schema(description = "推广渠道名字")
    @ColumnWidth(20)
    @ExcelProperty("推广渠道名字")
    private Integer deductionChannelLevel;

    /**
     * 扣量渠道ID（根据扣量计算结果确定）
     */
    @Schema(description = "扣量渠道ID")
    @ColumnWidth(20)
    @ExcelProperty("扣量渠道ID")
    private Long deductionChannelId;

    @Schema(description = "扣量渠道名字")
    @ColumnWidth(20)
    @ExcelProperty("扣量渠道ID")
    private String deductionChannelName;

    @Schema(description = "广告渠道id")
    @ColumnWidth(20)
    @ExcelProperty("广告渠道id")
    private Long adChannelId;

    @Schema(description = "广告渠道名字")
    @ColumnWidth(20)
    @ExcelProperty("广告渠道名字")
    private String adChannelName;

    @Schema(description = "广告扩展参数")
    @ColumnWidth(20)
    @ExcelProperty("广告扩展参数")
    private String adExt;

    @Schema(description = "订购页url")
    @ColumnWidth(20)
    @ExcelProperty("订购页url")
    private String pageUrl;

    @Schema(description = "广告代理平台")
    @ColumnWidth(20)
    @ExcelProperty("广告代理平台")
    private Integer adAgentPlatform;

    @Schema(description = "身份证名字")
    @ColumnWidth(20)
    @ExcelProperty("身份证名字")
    private String idCardName;

    @Schema(description = "身份证号码")
    @ColumnWidth(20)
    @ExcelProperty("身份证号码")
    private String idCard;

    @Schema(description = "sp产品编码")
    @ColumnWidth(20)
    @ExcelProperty("sp产品编码")
    private String spGoodsId;

    @Schema(description = "收货地址省份编码")
    @ColumnWidth(20)
    @ExcelProperty("收货地址省份编码")
    private String postProvinceCode;

    @Schema(description = "收货地址省份")
    @ColumnWidth(20)
    @ExcelProperty("收货地址省份")
    private String postProvince;

    @Schema(description = "收货城市编码")
    @ColumnWidth(20)
    @ExcelProperty("收货城市编码")
    private String postCityCode;

    @Schema(description = "收货城市")
    @ColumnWidth(20)
    @ExcelProperty("收货城市")
    private String postCity;

    @Schema(description = "收货区县代码")
    @ColumnWidth(20)
    @ExcelProperty("收货区县代码")
    private String postDistrictCode;

    @Schema(description = "收货区县")
    @ColumnWidth(20)
    @ExcelProperty("收货区县")
    private String postDistrict;

    @Schema(description = "详细收货地址")
    @ColumnWidth(20)
    @ExcelProperty("详细收货地址")
    private String address;

    @Schema(description = "收货联系号码")
    @ColumnWidth(20)
    @ExcelProperty("收货联系号码")
    private String contactPhone;

    @Schema(description = "选择的号码")
    @ColumnWidth(20)
    @ExcelProperty("选择的号码")
    private String phone;

    @Schema(description = "号码归属地编码-省")
    @ColumnWidth(20)
    @ExcelProperty("号码归属地编码-省")
    private String provinceCode;

    @Schema(description = "号码归属地-省")
    @ColumnWidth(20)
    @ExcelProperty("号码归属地-省")
    private String province;

    @Schema(description = "号码归属地编码-市")
    @ColumnWidth(20)
    @ExcelProperty("号码归属地编码-市")
    private String cityCode;

    @Schema(description = "号码归属地-市")
    @ColumnWidth(20)
    @ExcelProperty("号码归属地-市")
    private String city;

    @Schema(description = "选号类型")
    @ColumnWidth(20)
    @ExcelProperty(value = "选号类型", converter = PhoneNumSelectTypeConverter.class)
    private Integer selectType;

    @Schema(description = "请求领卡时间")
    @ColumnWidth(20)
    @ExcelProperty(value = "请求领卡时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime reqCardTime;

    @Schema(description = "订单状态：0-提交失败 1-审核中 2-领卡失败 3-领卡成功(运营商受理成功) 4-退单")
    @ColumnWidth(20)
    @ExcelProperty(value = "订单状态", converter = EccGdOrderStatusConverter.class)
    private Integer orderStatus;

    @Schema(description = "sim卡状态：0-待激活 1-激活 2-停机 3-销户")
    @ColumnWidth(20)
    @ExcelProperty(value = "sim卡状态", converter = EccCardStatusConverter.class)
    private Integer cardStatus;

    @Schema(description = "物流状态：0-待发货 1-发货 2-签收 3-拒收")
    @ColumnWidth(20)
    @ExcelProperty(value = "物流状态", converter = ExpressStatusConverter.class)
    private Integer expressStatus;

    @Schema(description = "物流公司")
    @ColumnWidth(20)
    @ExcelProperty("物流公司")
    private String expressCompany;

    @Schema(description = "物流订单号")
    @ColumnWidth(20)
    @ExcelProperty("物流订单号")
    private String expressNo;

    @Schema(description = "发货时间")
    @ColumnWidth(20)
    @ExcelProperty(value = "发货时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime deliverTime;

    @Schema(description = "激活时间")
    @ColumnWidth(20)
    @ExcelProperty(value = "激活时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime activateTime;

    @Schema(description = "停机时间")
    @ColumnWidth(20)
    @ExcelProperty(value = "停机时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime stopTime;

    @Schema(description = "销户时间")
    @ColumnWidth(20)
    @ExcelProperty(value = "销户时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime closeTime;

    @Schema(description = "首次充值金额")
    @ColumnWidth(20)
    @ExcelProperty("首次充值金额")
    private String firstChargeAmount;

    @Schema(description = "首冲充值时间")
    @ColumnWidth(20)
    @ExcelProperty(value = "首冲充值时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime firstChargeTime;

    @Schema(description = "领卡失败原因")
    @ColumnWidth(20)
    @ExcelProperty("领卡失败原因")
    private String failReason;

    @Schema(description = "备注")
    @ColumnWidth(20)
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "插入数据库时间")
    @ColumnWidth(20)
    @ExcelProperty(value = "插入数据库时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @ColumnWidth(20)
    @ExcelProperty(value = "更新时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime updateTime;

    public static EccNcOrderResp buildResp(EccNcOrderDO orderDO) {
        if (orderDO == null) {
            return null;
        }
        EccNcOrderResp resp = new EccNcOrderResp();
        resp.setOrderId(orderDO.getOrderId());
        resp.setOrderNo(orderDO.getOrderNo());
        resp.setChannelOrderNo(orderDO.getChannelOrderNo());
        resp.setSpOrderNo(orderDO.getSpOrderNo());
        resp.setOriginalOrderNo(orderDO.getOriginalOrderNo());
        resp.setOperator(orderDO.getOperator());
        resp.setProdId(orderDO.getProdId());
        resp.setProdName(orderDO.getProdName());
        resp.setChannelId(orderDO.getChannelId());
        resp.setChannelType(orderDO.getChannelType());
        resp.setChannelName(orderDO.getChannelName());
        resp.setDeductionChannelId(orderDO.getDeductionChannelId());
        resp.setDeductionChannelName(orderDO.getDeductionChannelName());
        resp.setDeductionChannelLevel(orderDO.getDeductionChannelLevel());
        resp.setAdChannelId(orderDO.getAdChannelId());
        resp.setAdChannelName(orderDO.getAdChannelName());
        resp.setAdExt(orderDO.getAdExt());
        resp.setPageUrl(orderDO.getPageUrl());
        resp.setAdAgentPlatform(orderDO.getAdAgentPlatform());
        resp.setIdCardName(orderDO.getIdCardName());
        resp.setIdCard(orderDO.getIdCard());
        resp.setSpGoodsId(orderDO.getSpGoodsId());
        resp.setPostProvinceCode(orderDO.getPostProvinceCode());
        resp.setPostProvince(orderDO.getPostProvince());
        resp.setPostCityCode(orderDO.getPostCityCode());
        resp.setPostCity(orderDO.getPostCity());
        resp.setPostDistrictCode(orderDO.getPostDistrictCode());
        resp.setPostDistrict(orderDO.getPostDistrict());
        resp.setAddress(orderDO.getAddress());
        resp.setContactPhone(orderDO.getContactPhone());
        resp.setPhone(orderDO.getPhone());
        resp.setProvinceCode(orderDO.getProvinceCode());
        resp.setProvince(orderDO.getProvince());
        resp.setCityCode(orderDO.getCityCode());
        resp.setCity(orderDO.getCity());
        resp.setSelectType(orderDO.getSelectType());
        resp.setReqCardTime(orderDO.getReqCardTime());
        resp.setOrderStatus(orderDO.getOrderStatus());
        resp.setCardStatus(orderDO.getCardStatus());
        resp.setExpressStatus(orderDO.getExpressStatus());
        resp.setExpressCompany(orderDO.getExpressCompany());
        resp.setExpressNo(orderDO.getExpressNo());
        resp.setDeliverTime(orderDO.getDeliverTime());
        resp.setActivateTime(orderDO.getActivateTime());
        resp.setStopTime(orderDO.getStopTime());
        resp.setCloseTime(orderDO.getCloseTime());
        if (Objects.nonNull(orderDO.getFirstChargeAmount())) {
            // 分转成2为小数的圆显示hutool
            Number yuan = 100;
            resp.setFirstChargeAmount(NumberUtil.div(orderDO.getFirstChargeAmount(), yuan, 2).toString());
        }
        resp.setFirstChargeTime(orderDO.getFirstChargeTime());
        resp.setFailReason(orderDO.getFailReason());
        resp.setRemark(orderDO.getRemark());
        resp.setCreateTime(orderDO.getCreateTime());
        resp.setUpdateTime(orderDO.getUpdateTime());
        return resp;

    }
}