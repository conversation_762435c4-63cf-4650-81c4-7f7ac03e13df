package com.yuelan.hermes.quanyi.common.listener;

import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.commons.enums.BizNoPrefixEnum;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderItemDOService;
import com.yuelan.hermes.quanyi.common.enums.RytOrderNotifyEnum;
import com.yuelan.hermes.quanyi.common.enums.TzOrderStateTypeEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.event.*;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderItemDO;
import com.yuelan.hermes.quanyi.controller.request.RytOrderNotifyReq;
import com.yuelan.hermes.quanyi.controller.request.TeZhenNotifyReq;
import com.yuelan.hermes.quanyi.remote.jinxi.request.JinXiCallBackReq;
import com.yuelan.hermes.quanyi.remote.response.RytRsp;
import com.yuelan.hermes.quanyi.remote.softgame.request.SoftGameNotifyReq;
import com.yuelan.result.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> 2024/4/12 下午4:43
 * 权益订单通知监听
 */
@Slf4j
@Component
@AllArgsConstructor
public class BenefitOrderNotifyListener {

    private final BenefitOrderItemDOService benefitOrderItemDOService;
    private final ApplicationEventPublisher applicationEventPublisher;

    @Order(2)
    @EventListener(SoftGameOrderNotifyEvent.class)
    public void defaultUserBenefitPayListener(SoftGameOrderNotifyEvent event) {
        if (event.getIsHandled()) {
            return;
        }
        SoftGameNotifyReq req = event.getRequest();
        String itemNo = req.getUserOrderId();
        if (!isBenefitOrder(itemNo)) {
            return;
        }
        try {
            BenefitOrderItemDO dbOrderItem = benefitOrderItemDOService.getOneByItemNo(itemNo);
            if (Objects.isNull(dbOrderItem)) {
                throw BizException.create(BizErrorCodeEnum.ORDER_NOT_EXIST, "订单不存在");
            }
            if (!Objects.equals(OrderStatusEnum.PROCESSING.getCode(), dbOrderItem.getOrderStatus())) {
                log.error("权益订单已处理,忽略,订单号{}", itemNo);
                setFail(event);
                return;
            }
            OrderStatusEnum itemOrderStatus = null;
            if (req.isSuccess()) {
                itemOrderStatus = OrderStatusEnum.SUCCESS;
            } else if (req.isFailed()) {
                itemOrderStatus = OrderStatusEnum.ABNORMAL;
            } else {
                log.error("权益订单状态不能识别,订单号{}", itemNo);
                setFail(event);
                return;
            }
            updateItemOrder(JSONObject.toJSONString(req), dbOrderItem.getItemId(), itemOrderStatus);
            setSuccess(event);
            publishItemOrderEndEvent(dbOrderItem.getOrderNo());
        } finally {
            event.setIsHandled(Boolean.TRUE);
        }

    }

    @Order(2)
    @EventListener(JinXiOrderNotifyEvent.class)
    public void defaultUserBenefitPayListener(JinXiOrderNotifyEvent event) {
        if (event.getIsHandled()) {
            return;
        }
        JinXiCallBackReq req = event.getRequest();
        String itemNo = req.getCustomOrderId();
        if (!isBenefitOrder(itemNo)) {
            return;
        }
        log.info("权益订单处理中{}", itemNo);
        try {
            BenefitOrderItemDO dbOrderItem = benefitOrderItemDOService.getOneByItemNo(itemNo);
            if (Objects.isNull(dbOrderItem)) {
                throw BizException.create(BizErrorCodeEnum.ORDER_NOT_EXIST, "订单不存在");
            }
            if (!Objects.equals(OrderStatusEnum.PROCESSING.getCode(), dbOrderItem.getOrderStatus())) {
                log.error("权益订单已处理,忽略,订单号{}", itemNo);
                setFail(event);
                return;
            }
            OrderStatusEnum itemOrderStatus = null;
            if (req.isSuccess()) {
                itemOrderStatus = OrderStatusEnum.SUCCESS;
            } else if (req.isFailed()) {
                itemOrderStatus = OrderStatusEnum.ABNORMAL;
            } else {
                log.error("权益订单状态不能识别,订单号{}", itemNo);
                setFail(event);
                return;
            }
            updateItemOrder(JSONObject.toJSONString(req), dbOrderItem.getItemId(), itemOrderStatus);
            setSuccess(event);
            publishItemOrderEndEvent(dbOrderItem.getOrderNo());
        } catch (Exception e) {
            setFail(event);
            log.error("权益订单处理失败,订单号{}", itemNo, e);
        } finally {
            event.setIsHandled(Boolean.TRUE);
        }
    }

    @EventListener(RtyOrderNotifyEvent.class)
    public void defaultUserBenefitPayListener(RtyOrderNotifyEvent event) {
        if (event.getIsHandled()) {
            return;
        }
        RytOrderNotifyReq req = event.getReq();
        String itemNo = req.getOrderId();
        if (!isBenefitOrder(itemNo)) {
            return;
        }
        OrderStatusEnum itemOrderStatus = RytOrderNotifyEnum.toVirtualOrderStatusEnum(req.getCode());
        log.info("权益订单处理中{}", itemNo);
        try {
            BenefitOrderItemDO dbOrderItem = benefitOrderItemDOService.getOneByItemNo(itemNo);
            if (Objects.isNull(dbOrderItem)) {
                throw BizException.create(BizErrorCodeEnum.ORDER_NOT_EXIST, "订单不存在");
            }
            if (!Objects.equals(OrderStatusEnum.PROCESSING.getCode(), dbOrderItem.getOrderStatus())) {
                log.error("权益订单已处理,忽略,订单号{}", itemNo);
                setFail(event);
                return;
            }
            updateItemOrder(JSONObject.toJSONString(req), dbOrderItem.getItemId(), itemOrderStatus);
            setSuccess(event);
            publishItemOrderEndEvent(dbOrderItem.getOrderNo());
        } catch (Exception e) {
            setFail(event);
            log.error("权益订单处理失败,订单号{}", itemNo, e);
        } finally {
            event.setIsHandled(Boolean.TRUE);
        }

    }

    @EventListener(TeZhenOrderNotifyEvent.class)
    public void defaultUserBenefitPayListener(TeZhenOrderNotifyEvent event) {
        if (event.getIsHandled()) {
            return;
        }
        TeZhenNotifyReq req = event.getReq();
        String itemNo = req.getLinkId();
        if (!isBenefitOrder(itemNo)) {
            return;
        }
        OrderStatusEnum itemOrderStatus = TzOrderStateTypeEnum.toOrderStatusEnum(req.getCode());
        log.info("权益订单处理中{}", itemNo);
        try {
            BenefitOrderItemDO dbOrderItem = benefitOrderItemDOService.getOneByItemNo(itemNo);
            if (Objects.isNull(dbOrderItem)) {
                throw BizException.create(BizErrorCodeEnum.ORDER_NOT_EXIST, "订单不存在");
            }
            if (!Objects.equals(OrderStatusEnum.PROCESSING.getCode(), dbOrderItem.getOrderStatus())) {
                log.error("权益订单已处理,忽略,订单号{}", itemNo);
                setFail(event);
                return;
            }
            updateItemOrder(JSONObject.toJSONString(req), dbOrderItem.getItemId(), itemOrderStatus);
            setSuccess(event);
            publishItemOrderEndEvent(dbOrderItem.getOrderNo());
        } catch (Exception e) {
            setFail(event);
            log.error("权益订单处理失败,订单号{}", itemNo, e);
        } finally {
            event.setIsHandled(Boolean.TRUE);
        }

    }

    private void updateItemOrder(String reqBody, Long itemId, OrderStatusEnum orderStatus) {
        BenefitOrderItemDO itemUpdate = new BenefitOrderItemDO();
        itemUpdate.setItemId(itemId);
        itemUpdate.setOrderStatus(orderStatus.getCode());
        itemUpdate.setCallBackContent(reqBody);
        itemUpdate.setCallBackTime(LocalDateTime.now());
        benefitOrderItemDOService.updateById(itemUpdate);
    }

    private void publishItemOrderEndEvent(String orderNo) {
        BenefitOrderItemEndEvent endEvent = new BenefitOrderItemEndEvent(orderNo);
        applicationEventPublisher.publishEvent(endEvent);
    }


    /**
     * 是否是权益订单
     *
     * @param itemNo 子订单订单号
     * @return 是否是权益订单
     */
    private boolean isBenefitOrder(String itemNo) {
        return Objects.nonNull(itemNo)
                && itemNo.startsWith(BizNoPrefixEnum.ORDER_BENEFIT_ITEM.getPrefix());
    }

    private void setSuccess(RtyOrderNotifyEvent event) {
        event.setResp(RytRsp.success());
    }

    private void setFail(RtyOrderNotifyEvent event) {
        event.setResp(RytRsp.error(500, "充值回调处理失败"));
    }

    private void setSuccess(SoftGameOrderNotifyEvent event) {
        event.setSuccessDeal(true);
    }

    private void setFail(SoftGameOrderNotifyEvent event) {
        event.setSuccessDeal(false);
    }

    private void setSuccess(JinXiOrderNotifyEvent event) {
        event.setSuccessDeal(true);
    }

    private void setFail(JinXiOrderNotifyEvent event) {
        event.setSuccessDeal(false);
    }

    private void setSuccess(TeZhenOrderNotifyEvent event) {
        event.setSuccess(Boolean.TRUE);
    }

    private void setFail(TeZhenOrderNotifyEvent event) {
        event.setSuccess(Boolean.FALSE);
    }


}
