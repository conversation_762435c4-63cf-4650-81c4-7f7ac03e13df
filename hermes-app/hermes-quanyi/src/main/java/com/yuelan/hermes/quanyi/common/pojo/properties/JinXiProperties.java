package com.yuelan.hermes.quanyi.common.pojo.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 今溪配置
 *
 * <AUTHOR> 2025/8/2
 * @since 2025/8/2
 */
@Data
@Component
@ConfigurationProperties(prefix = "jinxi")
public class JinXiProperties {

    /**
     * 商户Key，平台分配给商户的app_key
     */
    private String appKey = "dce68029-4fa8-4ec8-90a6-cb895082560c";

    /**
     * 签名密钥
     */
    private String signKey = "052888ac6328442ca2dfffaf9a5a874b";

    /**
     * 订单查询接口地址
     */
    private String orderQueryUrl = "http://*************:5001/api/order/get";

    /**
     * 订购商品鉴权接口
     */
    private String productAuthUrl = "http://*************:5001/api/order/auth";

    /**
     * 订单充值接口地址
     */
    private String orderRechargeUrl = "http://*************:5001/api/order/recharge";

    /**
     * 接口版本，固定为2.0
     */
    private String version = "2.0";

    /**
     * 签名加密类型，固定为md5
     */
    private String signType = "md5";

    /**
     * 是否启用沙箱环境
     */
    private boolean sandbox = false;


}
