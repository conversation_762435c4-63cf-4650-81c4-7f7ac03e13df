package com.yuelan.hermes.quanyi.biz.handler.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.commons.enums.SpEnum;
import com.yuelan.hermes.quanyi.biz.handler.NumberCardServer;
import com.yuelan.hermes.quanyi.biz.manager.NumberCardManager;
import com.yuelan.hermes.quanyi.biz.service.EccAreaService;
import com.yuelan.hermes.quanyi.biz.service.EccNcOrderService;
import com.yuelan.hermes.quanyi.common.enums.EccSpEnum;
import com.yuelan.hermes.quanyi.common.enums.NcOrderStatusEnum;
import com.yuelan.hermes.quanyi.common.enums.SimCardStatusEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.PhoneLocationBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.PhonePrettyTagBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccAreaDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccNcOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import com.yuelan.hermes.quanyi.controller.request.NcPhoneKeySearchReq;
import com.yuelan.hermes.quanyi.remote.KaSaiManager;
import com.yuelan.hermes.quanyi.remote.kassai.request.BroadnetSelectNumberReq;
import com.yuelan.hermes.quanyi.remote.kassai.request.BroadnetTradeOrderReq;
import com.yuelan.hermes.quanyi.remote.kassai.request.KaSaiMessagePushReq;
import com.yuelan.hermes.quanyi.remote.kassai.response.BroadnetOrderDto;
import com.yuelan.hermes.quanyi.remote.kassai.response.BroadnetSelectDto;
import com.yuelan.hermes.quanyi.remote.kassai.response.KsBaseResponse;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2025/7/10
 * @since 2025/7/10
 * <p>
 * 卡赛广电卡服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KaSaiNumCardServerImpl implements NumberCardServer {

    private final KaSaiManager kaSaiManager;
    private final EccAreaService eccAreaService;
    private final EccNcOrderService eccNcOrderService;

    @Autowired
    @Lazy
    private NumberCardManager numberCardManager;

    @Override
    public EccSpEnum getSpEnum() {
        return EccSpEnum.KA_SAI_CBN;
    }

    @Override
    public PageData<PhonePrettyTagBO> selectPhoneNumPool(NcPhoneKeySearchReq req, EccProductDO productDO) {
        String provinceCode = req.getProvinceCode();
        String cityCode = req.getCityCode();
        if (Objects.isNull(provinceCode) || Objects.isNull(cityCode)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "请选择归属地");
        }

        // 获取地区信息
        EccAreaDO location = eccAreaService.getOneAreaByNumCode(SpEnum.CBN, provinceCode, cityCode);
        if (location == null) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "归属地错误不存在");
        }

        // 构建卡赛选号请求
        BroadnetSelectNumberReq selectReq = new BroadnetSelectNumberReq();
        selectReq.setProvinceCode(provinceCode);
        selectReq.setCityCode(cityCode);
        selectReq.setNumberKeyword(req.getPhoneKey());
        selectReq.setLinkNum(productDO.getSpGoodsId());

        try {
            // 调用卡赛选号接口
            KsBaseResponse<List<BroadnetSelectDto>> selectResp = kaSaiManager.selectNumber(selectReq);

            if (selectResp == null || !Objects.equals(200, selectResp.getCode())) {
                log.error("卡赛选号失败: {}", selectResp != null ? selectResp.getMessage() : "响应为空");
                return PageData.create(new ArrayList<>(), 0L, 1L, 0);
            }

            List<BroadnetSelectDto> selectData = selectResp.getData();
            if (selectData == null || selectData.isEmpty()) {
                return PageData.create(new ArrayList<>(), 0L, 1L, 0);
            }

            // 转换为系统内部格式
            List<PhoneLocationBO> phoneLocationList = selectData.stream()
                    .map(dto -> {
                        PhoneLocationBO phoneLocationBO = new PhoneLocationBO();
                        phoneLocationBO.setPhone(dto.getNumber());
                        phoneLocationBO.setProvince(location.getAreaName());
                        phoneLocationBO.setCity(location.getSubAreaList().get(0).getAreaName());
                        phoneLocationBO.setProvinceCode(provinceCode);
                        phoneLocationBO.setCityCode(cityCode);
                        return phoneLocationBO;
                    })
                    .collect(Collectors.toList());

            PageData<PhoneLocationBO> locationList = PageData.create(phoneLocationList, (long) phoneLocationList.size(), 1L, phoneLocationList.size());
            List<PhonePrettyTagBO> results = locationList.getList().stream()
                    .map(itemBO -> PhonePrettyTagBO.buildBO(itemBO, req.getPhoneKey()))
                    .collect(Collectors.toList());

            return PageData.create(results, locationList.getTotal(), locationList.getPage(), locationList.getSize());

        } catch (Exception e) {
            log.error("卡赛选号异常", e);
            throw BizException.create(BaseErrorCodeEnum.SYS_ERROR, "选号失败");
        }
    }

    @Override
    public void getCard(EccNcOrderDO orderDO) {
        if (!AppConstants.isReal()) {
            log.info("测试环境模拟领卡成功");
            orderDO.setSpOrderNo(IdUtil.fastSimpleUUID());
            orderDO.setOrderStatus(NcOrderStatusEnum.GET_CARD_SUCCESS.getCode());
            return;
        }

        try {
            // 构建卡赛下单请求
            BroadnetTradeOrderReq tradeReq = buildTradeOrderReq(orderDO);

            // 调用卡赛下单接口
            KsBaseResponse<BroadnetOrderDto> orderResp = kaSaiManager.tradeOrder(tradeReq);

            // 处理下单响应
            processGetCardResponse(orderDO, orderResp);

        } catch (Exception e) {
            log.error("卡赛领卡失败", e);
            orderDO.setOrderStatus(NcOrderStatusEnum.GET_CARD_FAIL.getCode());
            orderDO.setFailReason("领卡失败系统异常");
            throw BizException.create(BaseErrorCodeEnum.SYS_ERROR, "领卡失败系统异常");
        }
    }


    /**
     * 处理领卡响应
     *
     * @param orderDO   订单信息
     * @param orderResp 卡赛响应
     */
    private void processGetCardResponse(EccNcOrderDO orderDO,  KsBaseResponse<BroadnetOrderDto> orderResp) {
        if (orderResp == null) {
            log.error("卡赛下单响应为空，订单号: {}", orderDO.getOrderNo());
            setOrderFailed(orderDO, "卡赛接口请求失败");
            return;
        }

        Integer code = orderResp.getCode();
        String message = orderResp.getMessage();

        log.info("卡赛下单响应，订单号: {}, code: {}, message: {}", orderDO.getOrderNo(), code, message);

        if (Objects.equals(200, code)) {
            // 成功响应，处理下单结果
            processTradeOrderResp(orderDO, orderResp);
            log.info("卡赛下单成功，订单号: {}", orderDO.getOrderNo());

        } else if (Objects.equals(119, code)) {
            // 异步处理，需要等待回调
            handleAsyncResponse(orderDO, orderResp);
            log.info("卡赛下单异步处理中，订单号: {}, 等待异步回调", orderDO.getOrderNo());

        } else {
            // 其他错误码，直接失败
            log.error("卡赛下单失败，订单号: {}, code: {}, message: {}", orderDO.getOrderNo(), code, message);
            setOrderFailed(orderDO, "下单失败: " + message);
        }
    }

    /**
     * 处理异步响应 (code = 119)
     *
     * @param orderDO   订单信息
     * @param orderResp 卡赛响应
     */
    private void handleAsyncResponse(EccNcOrderDO orderDO, KsBaseResponse<BroadnetOrderDto> orderResp) {
        BroadnetOrderDto data = orderResp.getData();
        // 设置第三方订单号（如果有）
        if (CharSequenceUtil.isNotBlank(data.getCommonOrderId())
                && !"1".equals(data.getCommonOrderId())) {
            orderDO.setSpOrderNo(data.getCommonOrderId());
        }

        // 设置订单状态为处理中，等待异步回调
        orderDO.setOrderStatus(NcOrderStatusEnum.AUDITING.getCode());

        log.info("卡赛订单进入异步处理状态，订单号: {}, 第三方订单号: {}",
                orderDO.getOrderNo(), data.getCommonOrderId());
    }

    /**
     * 设置订单失败状态
     *
     * @param orderDO    订单信息
     * @param failReason 失败原因
     */
    private void setOrderFailed(EccNcOrderDO orderDO, String failReason) {
        orderDO.setOrderStatus(NcOrderStatusEnum.GET_CARD_FAIL.getCode());
        orderDO.setFailReason(failReason);
    }

    /**
     * 构建卡赛下单请求
     */
    private BroadnetTradeOrderReq buildTradeOrderReq(EccNcOrderDO orderDO) {
        BroadnetTradeOrderReq req = new BroadnetTradeOrderReq();

        // 基本信息
        req.setOrderMobile(orderDO.getPhone());
        req.setCertificateName(orderDO.getIdCardName());
        req.setCertificateNumber(orderDO.getIdCard());
        req.setMobilePhone(orderDO.getContactPhone());

        // 地址信息
        req.setSendDistrictCode(orderDO.getPostDistrictCode());
        req.setGoodsCityCode(orderDO.getCityCode());
        req.setAddress(orderDO.getAddress());

        req.setLinkNum(orderDO.getSpGoodsId());
        // 渠道订单号
        req.setChannelSeqId(orderDO.getOrderNo());
        return req;
    }

    /**
     * 处理下单响应
     */
    private void processTradeOrderResp(EccNcOrderDO orderDO, KsBaseResponse<BroadnetOrderDto> orderResp) {
        BroadnetOrderDto data = orderResp.getData();
        // 设置第三方订单号
        if (CharSequenceUtil.isNotBlank(data.getCommonOrderId())
                && !"1".equals(data.getCommonOrderId())) {
            orderDO.setSpOrderNo(data.getCommonOrderId());
        }

        // 根据响应结果设置订单状态
        if (Objects.equals("1", data.getCommonOrderId())) {
            // 收单场景，等待后续通知
            orderDO.setOrderStatus(NcOrderStatusEnum.AUDITING.getCode());
            log.info("卡赛下单成功，等待后续通知，订单号: {}", orderDO.getOrderNo());
        } else {
            // 其他情况暂时设置为成功，具体根据业务需要调整
            orderDO.setOrderStatus(NcOrderStatusEnum.GET_CARD_SUCCESS.getCode());
            log.info("卡赛下单成功，订单号: {}", orderDO.getOrderNo());
        }
    }

    /**
     * 处理卡赛回调消息
     */
    public KsBaseResponse<Void> dealCallback(KaSaiMessagePushReq req) {
        log.info("处理卡赛回调消息: {}", req);

        try {
            // 根据渠道订单号查询订单
            String orderNum = req.getOrderNum();
            if (CharSequenceUtil.isBlank(orderNum)) {
                log.error("卡赛回调缺少订单号");
                return createErrorResponse("订单号不能为空");
            }

            EccNcOrderDO dbOrder = eccNcOrderService.getByOrderNo(orderNum);
            if (dbOrder == null) {
                log.error("卡赛回调找不到订单: {}", orderNum);
                return createErrorResponse("订单不存在");
            }

            // 保存回调前的订单状态
            EccNcOrderDO beforeOrder = new EccNcOrderDO();
            beforeOrder.setOrderId(dbOrder.getOrderId());
            beforeOrder.setOrderStatus(dbOrder.getOrderStatus());
            beforeOrder.setCardStatus(dbOrder.getCardStatus());

            // 创建更新对象，只设置需要更新的字段
            EccNcOrderDO updateOrder = new EccNcOrderDO();
            updateOrder.setOrderId(dbOrder.getOrderId());

            // 处理订单状态变更，返回是否有字段变更
            boolean hasChanges = processOrderStatusChangeForUpdate(updateOrder, req);

            // 只有当有字段变更时才执行更新
            if (hasChanges) {
                eccNcOrderService.updateById(updateOrder);
            }

            // 发送事件通知
            EccNcOrderDO updatedOrder = eccNcOrderService.getByOrderNo(orderNum);
            numberCardManager.sendEvent(beforeOrder, updatedOrder);

            return createSuccessResponse();

        } catch (Exception e) {
            log.error("处理卡赛回调异常", e);
            return createErrorResponse("处理异常");
        }
    }

    /**
     * 处理订单状态变更，只设置需要更新的字段
     * @param updateOrder 用于更新的订单对象，只设置变更的字段
     * @param req 回调请求
     * @return 是否有字段变更
     */
    private boolean processOrderStatusChangeForUpdate(EccNcOrderDO updateOrder, KaSaiMessagePushReq req) {
        Integer status = req.getStatus();
        String statusDesc = req.getStatusDesc();
        boolean hasChanges = false;

        log.info("卡赛订单状态变更: 订单号={}, 状态={}, 描述={}", req.getOrderNum(), status, statusDesc);

        // 根据卡赛的状态码处理订单状态
        switch (status) {
            case 302: // 订购成功
            case 1025: // 订购完成
                updateOrder.setOrderStatus(NcOrderStatusEnum.GET_CARD_SUCCESS.getCode());
                hasChanges = true;
                break;
            case 303: // 订购失败
            case 1030: // 订购失败
            case 1010: // 预处理失败
                updateOrder.setOrderStatus(NcOrderStatusEnum.GET_CARD_FAIL.getCode());
                updateOrder.setFailReason(statusDesc);
                hasChanges = true;
                break;
            case 1020: // 已发货
                // 设置物流信息
                if (CharSequenceUtil.isNotBlank(req.getShipmentCompanyName())) {
                    updateOrder.setExpressCompany(req.getShipmentCompanyName());
                    hasChanges = true;
                }
                if (CharSequenceUtil.isNotBlank(req.getShipmentNumber())) {
                    updateOrder.setExpressNo(req.getShipmentNumber());
                    hasChanges = true;
                }
                break;
            case 1035: // 已激活
                updateOrder.setCardStatus(SimCardStatusEnum.ACTIVE.getCode());
                hasChanges = true;
                if (CharSequenceUtil.isNotBlank(req.getActivateTime())) {
                    try {
                        long timestamp = Long.parseLong(req.getActivateTime());
                        updateOrder.setActivateTime(LocalDateTime.ofEpochSecond(timestamp / 1000, 0, java.time.ZoneOffset.ofHours(8)));
                    } catch (NumberFormatException e) {
                        log.error("解析激活时间失败: {}", req.getActivateTime(), e);
                    }
                }
                break;
            case 10656: // 首充
                if (Objects.equals(1, req.getOaoRechargeStatus())) {
                    // oao充值档位 0 无 1  50元挡 2 100元及以上
                    Integer oaoRechargeGear = req.getOaoRechargeGear();
                    switch (oaoRechargeGear) {
                        case 1:
                            updateOrder.setFirstChargeAmount(50 * 100);
                            hasChanges = true;
                            break;
                        case 2:
                            updateOrder.setFirstChargeAmount(100 * 100);
                            hasChanges = true;
                            break;
                        default:
                            break;
                    }
                    if (updateOrder.getFirstChargeAmount() != null) {
                        updateOrder.setFirstChargeTime(LocalDateTime.now());
                    }
                }
                break;
            default:
                log.info("卡赛订单状态暂不处理: {}", status);
                break;
        }

        // 更新第三方订单号
        if (CharSequenceUtil.isNotBlank(req.getOrderId())) {
            updateOrder.setSpOrderNo(req.getOrderId());
            hasChanges = true;
        }

        return hasChanges;
    }

    /**
     * 创建成功响应
     */
    private KsBaseResponse<Void> createSuccessResponse() {
        KsBaseResponse<Void> response = new KsBaseResponse<>();
        response.setCode(200);
        response.setMessage("success");
        return response;
    }

    /**
     * 创建错误响应
     */
    private KsBaseResponse<Void> createErrorResponse(String message) {
        KsBaseResponse<Void> response = new KsBaseResponse<>();
        response.setCode(500);
        response.setMessage(message);
        return response;
    }
}
