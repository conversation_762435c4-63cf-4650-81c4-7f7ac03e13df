package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccImeiBindDO;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 2025/7/2
 * @since 2025/7/2
 */
@Data
@AutoMapper(target = EccImeiBindDO.class)
public class EccImeiBindResp {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long bindId;

    /**
     * imei
     */
    @Schema(description = "imei")
    private String imei;

    /**
     * icc_id
     */
    @Schema(description = "iccId")
    private String iccId;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String phone;

    /**
     * 号卡订单 id
     */
    @Schema(description = "号卡订单 id")
    private Long orderId;

    /**
     * 运营商订单号
     */
    @Schema(description = "运营商订单号")
    private String operatorOrderNo;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间(绑定时间)")
    private LocalDateTime createTime;

}
