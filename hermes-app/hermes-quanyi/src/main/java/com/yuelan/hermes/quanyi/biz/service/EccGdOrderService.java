package com.yuelan.hermes.quanyi.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccGdOrderDO;
import com.yuelan.hermes.quanyi.controller.request.EccGdOrderListReq;
import com.yuelan.hermes.quanyi.controller.response.EccGdOrderResp;
import com.yuelan.hermes.quanyi.controller.response.FileExportTaskCreateResp;
import com.yuelan.hermes.quanyi.remote.response.WaNumOrderDetailResp;
import com.yuelan.result.entity.PageData;

/**
 * <AUTHOR> 2024/11/20
 * @since 2024/11/20
 */
public interface EccGdOrderService extends IService<EccGdOrderDO> {

    /**
     * 根据我方订单号查询订单
     *
     * @param orderNo 我方订单号
     * @return EccGdOrderDO
     */
    EccGdOrderDO getByOrderNo(String orderNo);

    /**
     * 分页查询
     *
     * @param req     查询条件
     * @param adminId 管理员id
     * @return PageData<EccZopOrderResp>
     */
    PageData<EccGdOrderResp> pageList(EccGdOrderListReq req, long adminId);

    /**
     * 导出
     *
     * @param req     查询条件
     * @param adminId 管理员id
     * @return FileExportTaskCreateResp
     */
    FileExportTaskCreateResp export(EccGdOrderListReq req, long adminId);

    /**
     * 订单在三方或运营商的操作记录
     *
     * @param gdOrderId 我方订单id
     * @return WaNumOrderDetailResp.OrderDetail
     */
    WaNumOrderDetailResp.OrderDetail orderOperatorRecord(Long gdOrderId);

    /**
     * 更新订单状态
     *
     * @param gdOrderId 我方订单id
     * @param code      状态码
     */
    void updateOrderStatus(Long gdOrderId, Integer code);
}
