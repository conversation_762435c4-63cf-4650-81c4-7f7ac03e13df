package com.yuelan.hermes.quanyi.common.pojo.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 2025/3/21
 * @since 2025/3/21
 * HTTP异步请求任务
 */
@Schema(description = "HTTP异步请求任务")
@Data
@TableName(value = "http_async_task")
@Builder
public class HttpAsyncTask implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 请求唯一标识
     */
    @TableField(value = "request_id")
    @Schema(description = "请求唯一标识")
    private String requestId;

    /**
     * HTTP方法(GET/POST/PUT/DELETE等)
     */
    @TableField(value = "`method`")
    @Schema(description = "HTTP方法(GET/POST/PUT/DELETE等)")
    private String method;

    /**
     * 请求URL
     */
    @TableField(value = "url")
    @Schema(description = "请求URL")
    private String url;

    /**
     * HTTP请求头(JSON格式)
     */
    @TableField(value = "headers")
    @Schema(description = "HTTP请求头(JSON格式)")
    private String headers;

    /**
     * 请求体
     */
    @TableField(value = "body")
    @Schema(description = "请求体")
    private String body;

    /**
     * 内容类型
     */
    @TableField(value = "content_type")
    @Schema(description = "内容类型")
    private String contentType;


    /**
     * 来源系统
     */
    @TableField(value = "source_system")
    @Schema(description = "来源系统")
    private String sourceSystem;

    /**
     * 业务类型
     */
    @TableField(value = "business_type")
    @Schema(description = "业务类型")
    private String businessType;

    /**
     * 业务ID
     */
    @TableField(value = "business_id")
    @Schema(description = "业务ID")
    private String businessId;

    /**
     * 已重试次数
     */
    @TableField(value = "retry_count")
    @Schema(description = "已重试次数")
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @TableField(value = "max_retry_count")
    @Schema(description = "最大重试次数")
    private Integer maxRetryCount;

    /**
     * 下次重试时间
     */
    @TableField(value = "next_retry_time")
    @Schema(description = "下次重试时间")
    private LocalDateTime nextRetryTime;

    /**
     * 基础重试间隔(秒)
     */
    @TableField(value = "retry_interval")
    @Schema(description = "基础重试间隔(秒)")
    private Integer retryInterval;


    /**
     * 连接超时(毫秒)
     */
    @TableField(value = "connect_timeout")
    @Schema(description = "连接超时(毫秒)")
    private Integer connectTimeout;

    /**
     * 响应超时(毫秒)
     */
    @TableField(value = "read_timeout")
    @Schema(description = "响应超时(毫秒)")
    private Integer readTimeout;


    /**
     * 状态(0:待处理,1:处理中,2:处理成功,3:处理失败（会自动重试）,4:最终失败)
     */
    @TableField(value = "`status`")
    @Schema(description = "状态(0:待处理,1:处理中,2:处理成功,3:处理失败（会自动重试）,4:最终失败)")
    private Integer status;

    /**
     * 最后一次响应内容
     */
    @TableField(value = "last_response")
    @Schema(description = "最后一次响应内容")
    private String lastResponse;

    /**
     * 最后一次响应状态码
     */
    @TableField(value = "last_response_code")
    @Schema(description = "最后一次响应状态码")
    private Integer lastResponseCode;

    /**
     * 成功状态判断策略
     */
    @TableField(value = "success_strategy")
    @Schema(description = "成功状态判断策略")
    private String successStrategy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 扩展字段
     */
    @TableField(value = "ext_data")
    @Schema(description = "扩展字段")
    private String extData;


    /**
     * 备注
     */
    @TableField(value = "remark")
    @Schema(description = "备注")
    private String remark;


}