package com.yuelan.hermes.quanyi.job;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.yuelan.hermes.quanyi.biz.manager.SuiXiaoCaManager;
import com.yuelan.hermes.quanyi.biz.service.EccImeiBindService;
import com.yuelan.hermes.quanyi.biz.service.EccNcOrderService;
import com.yuelan.hermes.quanyi.biz.service.EccProductDOService;
import com.yuelan.hermes.quanyi.common.constant.CommonConstants;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.enums.HuNanOtherStatus;
import com.yuelan.hermes.quanyi.common.enums.NcOrderStatusEnum;
import com.yuelan.hermes.quanyi.common.enums.SimCardStatusEnum;
import com.yuelan.hermes.quanyi.common.enums.SpProdEnum;
import com.yuelan.hermes.quanyi.common.event.EccSxcActiveEvent;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccNcOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import com.yuelan.hermes.quanyi.remote.HuNanDxManager;
import com.yuelan.plugins.redisson.util.RedisUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.annotation.PowerJobHandler;
import tech.powerjob.worker.core.processor.TaskContext;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 随销卡定时任务处理器
 * <p>
 * 主要功能：
 * 1. 定时查询随销卡订单状态
 * 2. 更新首充金额和激活状态
 * 3. 处理订单状态变更和事件发布
 *
 * <AUTHOR> 2025/7/1
 * @since 2025/7/1
 */
@Slf4j
@Component
@AllArgsConstructor
public class SxcJob {

    private static final String LOG_PREFIX = "[随销卡定时任务]";
    private static final int QUERY_DAYS = 15; // 查询天数
    private static final int RECHARGE_UNIT_CONVERSION = 100; // 充值金额单位转换（元转分）

    private final EccProductDOService productDOService;
    private final EccNcOrderService ncOrderService;
    private final HuNanDxManager huNanDxManager;
    private final EccImeiBindService imeiBindService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final SuiXiaoCaManager suiXiaoCaManager;

    /**
     * 更新首充任务
     *
     * @param context 任务上下文
     * @return 执行结果
     */
    @PowerJobHandler(name = "sxcUpdateFirstChargeJob")
    public String sxcUpdateFirstChargeJob(TaskContext context) {
        String traceId = IdUtil.simpleUUID();
        MDC.put(CommonConstants.TRACE_ID, traceId);

        try {
            log.info("{}开始执行，任务ID：{}", LOG_PREFIX, traceId);

            // 1. 获取随销卡产品列表
            List<Long> productIdList = getProductIdList();
            if (productIdList.isEmpty()) {
                log.info("{}没有随销卡产品需要处理，任务结束", LOG_PREFIX);
                return buildSuccessResult("没有产品需要处理");
            }

            // 2. 查询待处理订单
            List<EccNcOrderDO> orders = getPendingOrders(productIdList);
            if (orders.isEmpty()) {
                log.info("{}没有待处理的订单，任务结束", LOG_PREFIX);
                return buildSuccessResult("没有订单需要处理");
            }

            log.info("{}查询到 {} 条待处理的随销卡订单", LOG_PREFIX, orders.size());

            // 3. 处理订单
            JobExecutionResult result = processOrders(orders);

            log.info("{}任务执行完成，处理订单数：{}，成功：{}，失败：{}，跳过：{}",
                    LOG_PREFIX, orders.size(), result.getSuccessCount(), result.getFailureCount(), result.getSkipCount());

            return buildSuccessResult(String.format("处理完成，成功：%d，失败：%d，跳过：%d",
                    result.getSuccessCount(), result.getFailureCount(), result.getSkipCount()));

        } catch (Exception e) {
            log.error("{}任务执行异常", LOG_PREFIX, e);
            return buildErrorResult("任务执行异常：" + e.getMessage());
        } finally {
            MDC.clear();
        }
    }

    /**
     * 获取随销卡产品ID列表
     */
    private List<Long> getProductIdList() {
        try {
            List<EccProductDO> productList = productDOService.getBySpProdId(SpProdEnum.HN_DX_SXC.getProdId());
            return productList.stream()
                    .map(EccProductDO::getProdId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("{}获取产品列表失败", LOG_PREFIX, e);
            throw new RuntimeException("获取产品列表失败", e);
        }
    }

    /**
     * 获取待处理订单列表
     */
    private List<EccNcOrderDO> getPendingOrders(List<Long> productIdList) {
        try {
            LocalDateTime startTime = LocalDateTime.now().minusDays(QUERY_DAYS);
            LocalDateTime endTime = LocalDateTime.now();
            return ncOrderService.getSuccessOrderAndNoChargeByProductId(productIdList, startTime, endTime);
        } catch (Exception e) {
            log.error("{}获取待处理订单失败", LOG_PREFIX, e);
            throw new RuntimeException("获取待处理订单失败", e);
        }
    }

    /**
     * 处理订单列表
     */
    private JobExecutionResult processOrders(List<EccNcOrderDO> orders) {
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        AtomicInteger skipCount = new AtomicInteger(0);

        for (EccNcOrderDO order : orders) {
            try {
                OrderProcessResult result = processOrder(order);
                switch (result) {
                    case SUCCESS:
                        successCount.incrementAndGet();
                        break;
                    case SKIP:
                        skipCount.incrementAndGet();
                        break;
                    case FAILURE:
                        failureCount.incrementAndGet();
                        break;
                }
            } catch (Exception e) {
                log.error("{}处理订单异常，订单号：{}", LOG_PREFIX, order.getOrderNo(), e);
                failureCount.incrementAndGet();
            }
        }

        return new JobExecutionResult(successCount.get(), failureCount.get(), skipCount.get());
    }

    /**
     * 处理单个订单
     */
    private OrderProcessResult processOrder(EccNcOrderDO order) {
        // 跳过激活错误的订单
        if (SimCardStatusEnum.ACTIVE_ERROR.getCode().equals(order.getCardStatus())) {
            log.debug("{}跳过激活错误订单，订单号：{}", LOG_PREFIX, order.getOrderNo());
            return OrderProcessResult.SKIP;
        }

        // 查询订单信息
        OrderQueryResult queryResult = queryOrderInfo(order);
        if (!queryResult.isSuccess()) {
            return OrderProcessResult.FAILURE;
        }

        // 处理订单取消
        if (queryResult.isCancelled()) {
            handleOrderCancellation(order, queryResult.getCancelReason());
            return OrderProcessResult.SUCCESS;
        }

        // 更新订单信息
        return updateOrderInfo(order, queryResult);
    }

    /**
     * 查询订单信息
     */
    private OrderQueryResult queryOrderInfo(EccNcOrderDO order) {
        try {
            HuNanDxManager.ApiWrapper<HuNanDxManager.BpsOrderInfoResponse> apiWrapper =
                    huNanDxManager.orderQuery(order.getSpOrderNo());

            if (!apiWrapper.isSystemSuccess()) {
                log.error("{}系统调用失败，订单号：{}", LOG_PREFIX, order.getOrderNo());
                return OrderQueryResult.failure();
            }

            HuNanDxManager.BpsOrderInfoResponse response = apiWrapper.getResult();
            if (!response.isApiSuccess()) {
                log.error("{}API调用失败，订单号：{}", LOG_PREFIX, order.getOrderNo());
                return OrderQueryResult.failure();
            }

            return parseOrderResponse(response);

        } catch (Exception e) {
            log.error("{}查询订单信息异常，订单号：{}", LOG_PREFIX, order.getOrderNo(), e);
            return OrderQueryResult.failure();
        }
    }

    /**
     * 解析订单响应
     */
    private OrderQueryResult parseOrderResponse(HuNanDxManager.BpsOrderInfoResponse response) {
        HuNanDxManager.BpsOrderInfoResponse.ServiceOrder serviceOrder =
                Optional.ofNullable(response.getData())
                        .map(HuNanDxManager.BpsOrderInfoResponse.RespData::getServiceOrder)
                        .orElse(null);

        if (serviceOrder == null) {
            return OrderQueryResult.failure();
        }

        OrderQueryResult result = new OrderQueryResult();
        result.setSuccess(true);
        result.setServiceOrderState(serviceOrder.getServiceOrderState());
        result.setCancelReason(serviceOrder.getServiceOrderReason());
        result.setRechargeLevel(serviceOrder.getRechargeLevel());
        result.setPhone(serviceOrder.getServiceOrderCusAccPhone());
        result.setActivationStatus(serviceOrder.getServiceOrderActivationStatus());
        result.setActivateTime(serviceOrder.getServiceOrderActivationTime());

        return result;
    }

    /**
     * 处理订单取消
     */
    private void handleOrderCancellation(EccNcOrderDO order, String cancelReason) {
        try {
            EccNcOrderDO updateOrder = new EccNcOrderDO();
            updateOrder.setOrderId(order.getOrderId());
            updateOrder.setOrderStatus(NcOrderStatusEnum.GET_CARD_FAIL.getCode());
            updateOrder.setFailReason(cancelReason);

            ncOrderService.updateById(updateOrder);
            log.info("{}订单已取消，订单号：{}，原因：{}", LOG_PREFIX, order.getOrderNo(), cancelReason);
        } catch (Exception e) {
            log.error("{}处理订单取消失败，订单号：{}", LOG_PREFIX, order.getOrderNo(), e);
            throw e;
        }
    }

    /**
     * 更新订单信息
     */
    private OrderProcessResult updateOrderInfo(EccNcOrderDO order, OrderQueryResult queryResult) {
        try {
            EccNcOrderDO updateOrder = new EccNcOrderDO();
            updateOrder.setOrderId(order.getOrderId());
            boolean hasUpdate = false;

            // 更新充值金额
            hasUpdate |= updateRechargeAmount(updateOrder, queryResult.getRechargeLevel());

            // 更新手机号
            hasUpdate |= updatePhoneNumber(order, updateOrder, queryResult.getPhone());

            // 更新激活状态
            hasUpdate |= updateActivationStatus(order, updateOrder, queryResult);

            // 检测权益补发
            checkBenefitDispatch(order);
            if (hasUpdate) {
                ncOrderService.updateById(updateOrder);
                log.info("{}订单信息已更新，订单号：{}", LOG_PREFIX, order.getOrderNo());
                return OrderProcessResult.SUCCESS;
            } else {
                return OrderProcessResult.SKIP;
            }

        } catch (Exception e) {
            log.error("{}更新订单信息失败，订单号：{}", LOG_PREFIX, order.getOrderNo(), e);
            return OrderProcessResult.FAILURE;
        }
    }

    /**
     * 权益补发检测
     */
    private void checkBenefitDispatch(EccNcOrderDO order) {
        try {
            if (order.getPhone() == null) {
                return;
            }
            String cacheKey = RedisKeys.getSxkBenefit(order.getOrderId());
            if (!RedisUtils.hasKey(cacheKey)) {
                return;
            }
            // 下发完成会自动删除 cacheKey 的
            suiXiaoCaManager.dispatchBenefit(order);
        } catch (Exception e) {
            log.error("{}权益补发检测失败，订单号：{}", LOG_PREFIX, order.getOrderNo(), e);
            // 不要影响主流程
        }
    }

    /**
     * 更新充值金额
     */
    private boolean updateRechargeAmount(EccNcOrderDO updateOrder, String rechargeLevel) {
        if (CharSequenceUtil.isNotBlank(rechargeLevel)) {
            try {
                Integer rechargeAmount = Integer.parseInt(rechargeLevel) * RECHARGE_UNIT_CONVERSION;
                updateOrder.setFirstChargeAmount(rechargeAmount);
                updateOrder.setFirstChargeTime(LocalDateTime.now());
                return true;
            } catch (NumberFormatException e) {
                log.error("{}解析充值金额失败：{}", LOG_PREFIX, rechargeLevel, e);
            }
        }
        return false;
    }

    /**
     * 更新手机号
     */
    private boolean updatePhoneNumber(EccNcOrderDO order, EccNcOrderDO updateOrder, String phone) {
        if (CharSequenceUtil.isNotBlank(phone) && CharSequenceUtil.isBlank(order.getPhone())) {
            updateOrder.setPhone(phone);
            order.setPhone(phone); // 更新原订单对象，用于后续处理

            try {
                imeiBindService.updatePhoneBySpOrderNo(order.getSpOrderNo(), phone);
                return true;
            } catch (Exception e) {
                log.error("{}更新IMEI绑定手机号失败，订单号：{}", LOG_PREFIX, order.getOrderNo(), e);
                // 不影响主流程，继续执行
                return true;
            }
        }
        return false;
    }

    /**
     * 更新激活状态
     */
    private boolean updateActivationStatus(EccNcOrderDO order, EccNcOrderDO updateOrder, OrderQueryResult queryResult) {
        String activationStatus = queryResult.getActivationStatus();

        if (HuNanOtherStatus.ACTIVE_SUCCESS.getCode().equals(activationStatus)
                && !SimCardStatusEnum.ACTIVE.getCode().equals(order.getCardStatus())) {

            updateOrder.setCardStatus(SimCardStatusEnum.ACTIVE.getCode());

            // 设置激活时间
            LocalDateTime activateDateTime = Optional.ofNullable(queryResult.getActivateTime())
                    .map(LocalDateTimeUtil::of)
                    .orElse(LocalDateTime.now());
            updateOrder.setActivateTime(activateDateTime);

            // 发布激活事件
            publishActivationEvent(order);

            return true;
        }
        return false;
    }

    /**
     * 发布激活事件
     */
    private void publishActivationEvent(EccNcOrderDO order) {
        try {
            EccSxcActiveEvent activeEvent = new EccSxcActiveEvent();
            activeEvent.setActiveOrder(order);
            applicationEventPublisher.publishEvent(activeEvent);
            log.info("{}发布激活事件成功，订单号：{}", LOG_PREFIX, order.getOrderNo());
        } catch (Exception e) {
            log.error("{}发布激活事件失败，订单号：{}", LOG_PREFIX, order.getOrderNo(), e);
            // 不影响主流程
        }
    }

    /**
     * 构建成功结果
     */
    private String buildSuccessResult(String message) {
        return String.format("SUCCESS: %s", message);
    }

    /**
     * 构建错误结果
     */
    private String buildErrorResult(String message) {
        return String.format("ERROR: %s", message);
    }

    /**
     * 订单处理结果枚举
     */
    private enum OrderProcessResult {
        SUCCESS, FAILURE, SKIP
    }

    /**
     * 任务执行结果
     */
    @Data
    @AllArgsConstructor
    private static class JobExecutionResult {
        private final int successCount;
        private final int failureCount;
        private final int skipCount;

    }

    /**
     * 订单查询结果
     */
    @Data
    private static class OrderQueryResult {
        private boolean success;
        private String serviceOrderState;
        private String cancelReason;
        private String rechargeLevel;
        private String phone;
        private String activationStatus;
        private Long activateTime;

        public static OrderQueryResult failure() {
            OrderQueryResult result = new OrderQueryResult();
            result.success = false;
            return result;
        }

        public boolean isCancelled() {
            return "ORDER_CANCEL".equals(serviceOrderState);
        }

    }

}
