package com.yuelan.hermes.quanyi.biz.service;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.CircleCaptcha;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.yuelan.core.captcha.CaptchaGenerator;
import com.yuelan.hermes.commons.enums.SmsCodeType;
import com.yuelan.hermes.commons.util.PasswordUtil;
import com.yuelan.hermes.quanyi.biz.handler.CacheHandler;
import com.yuelan.hermes.quanyi.biz.manager.AdminManager;
import com.yuelan.hermes.quanyi.common.constant.CommonConstants;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.enums.CacheEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.converter.AdminConverter;
import com.yuelan.hermes.quanyi.common.pojo.domain.AdminDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.AdminOperationLogDO;
import com.yuelan.hermes.quanyi.config.satoken.StpAdminUtil;
import com.yuelan.hermes.quanyi.config.satoken.context.AdminContext;
import com.yuelan.hermes.quanyi.controller.request.AdminLoginReq;
import com.yuelan.hermes.quanyi.controller.request.AdminLoginSmsCodeReq;
import com.yuelan.hermes.quanyi.controller.response.AdminUserInfoRsp;
import com.yuelan.hermes.quanyi.controller.response.PicCaptchaRsp;
import com.yuelan.hermes.quanyi.mapper.AdminOperationLogMapper;
import com.yuelan.plugins.log.enums.OperationStatus;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.plugins.log.utils.LogUtil;
import com.yuelan.plugins.redisson.util.RedisUtils;
import com.yuelan.plugins.satoken.utils.SaUtils;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

@Slf4j
@Service
public class LoginService {
    @Autowired
    private CacheHandler cacheHandler;
    @Autowired
    private AdminManager adminManager;
    @Autowired
    private AdminOperationLogMapper adminOperationLogMapper;
    @Autowired
    private HttpServletRequest httpServletRequest;
    @Autowired
    private SmsCodeService smsCodeService;

    /**
     * 获取登录随机码
     */
    public String generateKey() {
        String uuid = PasswordUtil.generateKey();
        RBucket<String> rBucket = cacheHandler.getAdminRandomCodeCache(uuid);
        rBucket.set(uuid, CacheEnum.ADMIN_RANDOM_CODE.getExpire(), CacheEnum.ADMIN_RANDOM_CODE.getUnit());
        return uuid;
    }

    /**
     * 获取图片验证码
     */
    public PicCaptchaRsp getPicCaptcha(String username) {
        // 校验用户
        AdminDO adminDO = adminManager.findByUsername(username);
        if (Objects.isNull(adminDO)) {
            throw BizException.create(BizErrorCodeEnum.ADMIN_NOT_EXISTS);
        }
        // 生成验证码，随机4位字符
        CircleCaptcha lineCaptcha = CaptchaUtil.createCircleCaptcha(100, 40, new CaptchaGenerator(), 10);
        lineCaptcha.setGenerator(new CaptchaGenerator());
        lineCaptcha.createCode();
        // 获取验证码的值
        String captcha = lineCaptcha.getCode();
        // 获取验证码的base64
        String captchaBase64 = lineCaptcha.getImageBase64Data();
        // 生成请求号
        String captchaReqNo = IdUtil.getSnowflakeNextIdStr();
        // 将请求号作为key，验证码的值作为value放到redis，用于校验，5分钟有效
        RMap<String, String> rMap = cacheHandler.getAdminCaptchaCache(username);
        rMap.put(captchaReqNo, captcha);
        rMap.expire(Duration.of(CacheEnum.ADMIN_CAPTCHA.getExpire(), ChronoUnit.MINUTES));
        return PicCaptchaRsp.builder().captchaPic(captchaBase64).captchaReqNo(captchaReqNo).build();
    }

    /**
     * 校验验证码
     * 不论是否校验成功都会删除验证码 防止对一个验证码暴力破解
     **/
    private void checkCaptcha(String username, String captcha, String captchaReqNo) {
        // 依据请求号，取出缓存中的验证码进行校验
        RMap<String, String> rMap = cacheHandler.getAdminCaptchaCache(username);
        String code = rMap.get(captchaReqNo);
        if (Objects.nonNull(code)) {
            rMap.remove(captchaReqNo);
        }
        if (StrUtil.equalsIgnoreCase(code, captcha)) {
            rMap.remove(captchaReqNo);
        } else {
            throw BizException.create(BizErrorCodeEnum.ADMIN_PIC_CAPTCHA_ERROR);
        }
    }

    /**
     * 管理员登录
     */
    public SaTokenInfo login(AdminLoginReq loginReq) {
        String cacheKey = RedisKeys.getAdminLoginCountLimitKey(loginReq.getUsername());
        long errorCount = RedisUtils.getAtomicValue(cacheKey);
        if (errorCount > 5) {
            throw BizException.create(BizErrorCodeEnum.ADMIN_STATUS_LOCK);
        }
        AdminDO adminDO = null;
        try {
            adminDO = getLoginAdmin(loginReq.getUsername(), loginReq.getPassword());
            RedisUtils.deleteObject(cacheKey);
        } catch (Exception e) {
            RedisUtils.incrAtomicValue(cacheKey, Duration.ofMinutes(30));
            throw e;
        }
        // 短信验证码验证
        smsCodeService.verifyCaptcha(adminDO.getPhone(), loginReq.getSmsCode(), SmsCodeType.ADMIN_LOGIN);
        // 创建登录上下文
        AdminContext context = new AdminContext();
        context.setAdminId(adminDO.getId());
        context.setNickname(adminDO.getNickname());
        //创建token
        StpAdminUtil.login(context.getAdminId());
        SaSession saSession = StpAdminUtil.getSession();
        SaUtils.setUserInfo(saSession, context);
        SaTokenInfo tokenInfo = StpAdminUtil.getTokenInfo();
        // 记录登录日志
        saveLoginLog(loginReq, adminDO, tokenInfo);
        return tokenInfo;
    }

    private void saveLoginLog(AdminLoginReq loginReq, AdminDO adminDO, SaTokenInfo tokenInfo) {
        AdminOperationLogDO logDO = new AdminOperationLogDO();
        logDO.init();
        logDO.setAdminId(adminDO.getId());
        logDO.setTitle(OperationType.LOGIN.getDesc());
        logDO.setTypeValue(OperationType.LOGIN.getCode());
        logDO.setTypeName(OperationType.LOGIN.getDesc());
        logDO.setUrl(LogUtil.getRequestURI(httpServletRequest));
        logDO.setIp(LogUtil.getIpAddr(httpServletRequest));
        logDO.setRequestData(JSON.toJSONString(loginReq));
        logDO.setResponseData(JSON.toJSONString(tokenInfo));
        logDO.setStatus(OperationStatus.SUCCESS.getCode());
        logDO.setErrorMsg("");
        adminOperationLogMapper.insert(logDO);
    }

    private AdminDO getLoginAdmin(String username, String password) {
        String decryptPassword = "";
        try {
            decryptPassword = PasswordUtil.decrypt(password, CommonConstants.PASSWORD_PRIVATE_KEY);
        } catch (Exception e) {
            throw BizException.create(BizErrorCodeEnum.INCORRECT_PASSWORD);
        }
        //校验随机码
        String key = PasswordUtil.getKey(decryptPassword);
        RBucket<String> rBucket = cacheHandler.getAdminRandomCodeCache(key);
        String code = rBucket.get();
        if (Objects.isNull(code)) {
            log.error("管理员登录，随机码不正确 username:{},key:{}", username, key);
            throw BizException.create(BizErrorCodeEnum.CODE_EXPIRED);
        }
        //通过验证立刻删除
        rBucket.delete();
        //校验用户
        AdminDO adminDO = adminManager.findByUsername(username);
        //校验密码
        boolean verify = PasswordUtil.verify(decryptPassword, adminDO.getPassword(), key);
        if (!verify) {
            throw BizException.create(BizErrorCodeEnum.INCORRECT_PASSWORD);
        }
        return adminDO;
    }

    /**
     * 获取登录用户信息
     */
    public AdminUserInfoRsp getUserInfo(AdminContext context) {
        AdminDO adminDO = adminManager.findById(context.getAdminId());
        if (Objects.isNull(adminDO)) {
            throw BizException.create(BizErrorCodeEnum.ADMIN_NOT_EXISTS);
        }
        return AdminConverter.toAdminUserInfoRsp(adminDO);
    }

    /**
     * 用户登出
     */
    public boolean logout() {
        StpAdminUtil.logout();
        return true;
    }

    /**
     * 管理员登录短信验证码
     */
    public void reqSmsCode(AdminLoginSmsCodeReq req) {
        checkCaptcha(req.getUsername(), req.getPicCaptcha(), req.getCaptchaReqNo());
        AdminDO adminDO = adminManager.findByUsername(req.getUsername());
        if (Objects.isNull(adminDO)) {
            throw BizException.create(BizErrorCodeEnum.ADMIN_NOT_EXISTS);
        }
        if (StrUtil.isBlank(adminDO.getPhone())) {
            throw BizException.create(BizErrorCodeEnum.ADMIN_PHONE_NOT_HAVE);
        }
        // 发送短信验证码
        smsCodeService.reqCaptcha(adminDO.getPhone(), SmsCodeType.ADMIN_LOGIN);
    }
}
