package com.yuelan.hermes.quanyi.common.pojo.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.yuelan.result.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 权益商品表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "benefit_goods")
public class BenefitGoodsDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "goods_id", type = IdType.AUTO)
    private Long goodsId;

    /**
     * 商品名
     */
    @TableField(value = "goods_name")
    private String goodsName;

    /**
     * 供应商编
     */
    @TableField(value = "supplier_type")
    private Integer supplierType;

    /**
     * 供应商名字
     */
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 供应商产品编号
     */
    @TableField(value = "supplier_goods_no")
    private String supplierGoodsNo;

    /**
     * 充值面值（单位元)
     */
    @TableField(value = "par_value")
    private BigDecimal parValue;

    /**
     * 产品图
     */
    @TableField(value = "goods_img")
    private String goodsImg;

    /**
     * 成本价格
     */
    @TableField(value = "cost_price")
    private BigDecimal costPrice;

    /**
     * 售价
     */
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 上下架状态：0-下架；1-上架
     */
    @TableField(value = "goods_status")
    private Integer goodsStatus;


    @TableLogic
    private Integer deleted;
}