package com.yuelan.hermes.quanyi.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccIccIdDO;
import com.yuelan.hermes.quanyi.controller.request.EccIccIdReq;
import com.yuelan.hermes.quanyi.controller.response.EccIccIdResp;
import com.yuelan.hermes.quanyi.controller.response.ImportResp;
import com.yuelan.result.entity.PageData;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * ICCID服务接口
 *
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
public interface EccIccIdService extends IService<EccIccIdDO> {

    /**
     * 导入ICCID文件
     *
     * @param file 导入文件
     * @throws IOException IO异常
     */
    ImportResp importIccIdFile(MultipartFile file, Integer channelType, Long channelId, Long productId) throws IOException;

    /**
     * 分页查询ICCID
     *
     * @param req 查询请求
     * @return 分页数据
     */
    PageData<EccIccIdResp> pageIccId(EccIccIdReq req);

    /**
     * 下载导入模板
     *
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void downloadTemplate(HttpServletResponse response) throws IOException;

    /**
     * 根据ICCID号码查询
     *
     * @param iccId ICCID号码
     * @return ICCID记录
     */
    EccIccIdDO getByIccId(String iccId);

    /**
     * 验证ICCID格式
     *
     * @param iccId ICCID号码
     * @return 是否有效
     */
    boolean validateIccIdFormat(String iccId);
}
