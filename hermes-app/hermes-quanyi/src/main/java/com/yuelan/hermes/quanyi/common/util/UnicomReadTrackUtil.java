package com.yuelan.hermes.quanyi.common.util;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Base64;


/**
 * 联通沃悦读H5埋点外部渠道
 *
 * <AUTHOR>
 */
public class UnicomReadTrackUtil {

    private static final String IV = "16-Bytes--String";
    private static final String DEFAULT_UA = "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36";
    private static final String DEFAULT_DEPT_ID = "7";
    private static final String DEFAULT_PAGE_URL = "https://h5.hzsydm.com/benefit/landingPage/TLtf3s75";
    private static final String API_URL = "https://m.woread.com.cn/api/union/common/marketingOrderReport";

    /**
     * 创建并配置HTTP请求对象
     *
     * @param obj 包含请求参数的Map对象
     * @return 配置好的HttpRequest对象，失败返回null
     */
    public static HttpRequest request(Map<String, Object> obj) {
        try {
            String source = obj.getOrDefault("source", "").toString();
            String key = obj.getOrDefault("key", "").toString();
            Object eventType = obj.getOrDefault("event_type", "");
            Object type = obj.getOrDefault("type", "");
            Object productId = obj.getOrDefault("productid", "");
            Object userUa = obj.getOrDefault("user_ua", DEFAULT_UA);
            String mobile = obj.getOrDefault("mobile", "").toString();
            Object timestamp = obj.getOrDefault("timestamp", System.currentTimeMillis());
            Object deptId = obj.getOrDefault("dept_id", DEFAULT_DEPT_ID);
            Object prmId = obj.getOrDefault("prm_id", "");
            Object cbId = obj.getOrDefault("cb_id", "");
            Object pageUrl = obj.getOrDefault("page_url", DEFAULT_PAGE_URL);
            Object orderUrl = obj.getOrDefault("order_url", null);
            Object orderId = obj.getOrDefault("order_id", "");
            Object treesid = obj.getOrDefault("treesid", "");
            Object paytype = obj.getOrDefault("paytype", "");
            Object channelid = obj.getOrDefault("channelid", "");

            Map<String, Object> query = new HashMap<>();
            query.put("eventType", eventType);
            query.put("productid", productId);
            query.put("type", type);
            query.put("userUa", userUa);
            query.put("timestamp", timestamp);
            query.put("pageUrl", pageUrl);
            query.put("orderUrl", orderUrl);
            query.put("orderId", orderId);
            query.put("deptId", deptId);
            query.put("treesid", treesid);
            query.put("paytype", paytype);
            query.put("channelid", channelid);

            if (!mobile.isEmpty()) {
                query.put("mobile", getAES(mobile, key));
            }
            query = sortObjectByKeys(query);

            Map<String, Object> sortedQuery = new TreeMap<>(query);
            String queryJson = JSON.toJSONString(sortedQuery);
//            System.out.println(queryJson);
            String signStrAes = getAES(queryJson, key);

            Map<String, Object> params = new HashMap<>();
            params.put("sign", signStrAes);

            String jsonInputString = JSON.toJSONString(params);
            return HttpRequest.post(API_URL)
                    .header("Content-Type", "application/json")
                    .header("api-source", source)
                    .header("Referer", "https://h5.hzsydm.com/")
                    .header("origin", "https://h5.hzsydm.com")
                    .body(jsonInputString)
                    .timeout(10000);
        } catch (Exception e) {
            // 记录日志或抛出更具体的异常
            throw new RuntimeException("创建HTTP请求失败", e);
        }
    }

    /**
     * AES加密方法(CBC模式,PKCS5Padding填充)
     *
     * @param data 要加密的数据
     * @param key  加密密钥
     * @return Base64编码的加密结果，失败返回null
     */
    public static String getAES(String data, String key) {
        try {
            byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
            byte[] ivBytes = IV.getBytes(StandardCharsets.UTF_8);
            AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, keyBytes, ivBytes);
            // 加密
            byte[] encryptedBytes = aes.encrypt(data.getBytes(StandardCharsets.UTF_8));
            String hexEncodedCiphertext = HexUtil.encodeHexStr(encryptedBytes);
            return Base64.getEncoder().encodeToString(hexEncodedCiphertext.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            // 记录日志或抛出更具体的异常
            throw new RuntimeException("AES加密失败", e);
        }
    }

    /**
     * 对Map按key进行排序并过滤空值
     *
     * @param obj 要排序的Map对象
     * @return 排序后的LinkedHashMap对象
     */
    public static Map<String, Object> sortObjectByKeys(Map<String, Object> obj) {
        return obj.entrySet().stream()
                .filter(entry -> entry.getValue() != null && !"".equals(entry.getValue()))
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new
                ));
    }

//    public static void main(String[] args) throws Exception {
//        Map<String, Object> param = new HashMap<>();
//        param.put("source", "102");
//        param.put("key", "LuJr52l@z5276Kc8");
//        param.put("type", "1");
//        param.put("productid", "11084");
//        param.put("event_type", "1");
//        param.put("mobile", "15301230123");
//        param.put("treesid", "ff10bbfb885a4ae0bce68a48e8adccaf");
//        param.put("paytype", "8");
//        param.put("channelid", "15796641");
//
//        HttpRequest request = request(param);
//        System.out.println(request.execute().body());
//
////        System.out.println(System.currentTimeMillis());
//        System.out.println(UUID.randomUUID().toString().replaceAll("-", ""));
////        System.out.println(getAES("1", "LuJr52l@z5276Kc8"));
//    }

}
