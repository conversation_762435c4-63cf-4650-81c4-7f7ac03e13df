package com.yuelan.hermes.quanyi.common.listener;

import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.biz.manager.SuiXiaoCaManager;
import com.yuelan.hermes.quanyi.biz.service.impl.EccImeiBindServiceImpl;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.event.EccSxcActiveEvent;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccImeiBindDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccNcOrderDO;
import com.yuelan.hermes.quanyi.controller.request.SxcOrderCreateReq;
import com.yuelan.plugins.redisson.util.RedisUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR> 2025/7/3
 * @since 2025/7/3
 */
@Slf4j
@Component
@AllArgsConstructor
public class EccSxcListener {
    private final EccImeiBindServiceImpl eccImeiBindService;
    private final SuiXiaoCaManager suiXiaoCaManager;

    @Order(1)
    @EventListener(EccSxcActiveEvent.class)
    public void handleEccSxcActiveEvent(EccSxcActiveEvent event) {
        log.info("激活随销卡订单: {}", event.getActiveOrder().getOrderId());
        EccNcOrderDO activeOrder = event.getActiveOrder();
        String ext2 = activeOrder.getExt2();
        if (Objects.nonNull(ext2)) {
            SxcOrderCreateReq sxcOrderCreateReq = JSON.parseObject(ext2, SxcOrderCreateReq.class);
            EccImeiBindDO imeiBind = new EccImeiBindDO();
            imeiBind.setImei(sxcOrderCreateReq.getImei());
            imeiBind.setIccId(sxcOrderCreateReq.getIccid());
            imeiBind.setOrderId(activeOrder.getOrderId());
            imeiBind.setPhone(activeOrder.getPhone());
            imeiBind.setOperatorOrderNo(activeOrder.getSpOrderNo());
            eccImeiBindService.save(imeiBind);
        }
    }

    /**
     * 随销卡激活后
     *
     * @param event 激活事件
     */
    @Order(2)
    @EventListener(EccSxcActiveEvent.class)
    public void handleSxcBenefitsOrder(EccSxcActiveEvent event) {
        EccNcOrderDO activeOrder = event.getActiveOrder();
        String phone = activeOrder.getPhone();
        if (Objects.isNull(phone)) {
            String cacheKey = RedisKeys.getSxkBenefit(activeOrder.getOrderId());
            log.error("随销卡激活后处理订单: {}，手机号为空，权益发放忽略稍后定时任务处理发放", activeOrder.getOrderId());
            RedisUtils.setCacheObject(cacheKey, "");
            return;
        }
        suiXiaoCaManager.dispatchBenefit(activeOrder);
    }



}
