package com.yuelan.hermes.quanyi.remote.benefit.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 湖北数科获取Token响应对象
 *
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
public class HuBeiShuKeTokenResp {


    @lombok.Data
    static class Data {
        private String token;
    }

    /**
     * 响应码
     */
    private String code;

    /**
     * data数据
     */
    private Data data;


    /**
     * 是否成功
     */
    @JSONField(serialize = false)
    public boolean isSuccess() {
        return "0000".equals(code);
    }

}
