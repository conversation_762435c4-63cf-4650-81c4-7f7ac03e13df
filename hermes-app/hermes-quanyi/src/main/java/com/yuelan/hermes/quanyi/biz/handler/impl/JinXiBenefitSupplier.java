package com.yuelan.hermes.quanyi.biz.handler.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.commons.enums.SupplierEnum;
import com.yuelan.hermes.quanyi.biz.manager.param.ParameterDefinition;
import com.yuelan.hermes.quanyi.common.constant.SupplierGoodsParameters;
import com.yuelan.hermes.quanyi.common.interfaces.BenefitSupplier;
import com.yuelan.hermes.quanyi.common.pojo.bo.DispatchResultDTO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderExtensionDO;
import com.yuelan.hermes.quanyi.remote.JinXiManager;
import com.yuelan.hermes.quanyi.remote.jinxi.request.JinXiOrderRechargeReq;
import com.yuelan.hermes.quanyi.remote.jinxi.response.JinXiBaseResponse;
import com.yuelan.hermes.quanyi.remote.jinxi.response.JinXiOrderRechargeResp;
import com.yuelan.result.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> 2025/4/24
 * @since 2025/4/24
 * <p>
 * 软游通权益供应商
 */
@Slf4j
@Component
@AllArgsConstructor
public class JinXiBenefitSupplier implements BenefitSupplier {

    private final JinXiManager jinXiManager;

    @Override
    public SupplierEnum getSupplier() {
        return SupplierEnum.JIN_XI;
    }

    @Override
    public Set<ParameterDefinition> goodsParams() {
        Set<ParameterDefinition> param = new HashSet<>();
        param.add(SupplierGoodsParameters.GOODS_CODE);
        return param;
    }

    @Override
    public DispatchResultDTO dispatchBenefits(BenefitItemOrderDO orderDO, BenefitItemDO benefitItemDO) {
        BenefitOrderExtensionDO extension = orderDO.getOrderExtension();
        StringBuilder reqBuild = new StringBuilder();
        StringBuilder respBuild = new StringBuilder();
        JinXiBaseResponse<JinXiOrderRechargeResp> jinXiRechargeResp;
        boolean dispatchSuccess = false;
        String errorMessage = null;
        String supplierOrderNo = null;
        boolean realTime = false;
        try {
            String supplierGoodsParam = benefitItemDO.getSupplierGoodsParam();

            if (!AppConstants.isReal()) {
                // 测试环境模拟成功
                return dev(supplierGoodsParam);
            }
            jinXiRechargeResp = submitRechargeOrder(orderDO, benefitItemDO, reqBuild, respBuild);
            if (Objects.nonNull(jinXiRechargeResp) && jinXiRechargeResp.isSuccess()) {
                dispatchSuccess = true;
                realTime = jinXiRechargeResp.getData().isRealTimeSuccess();
            }
        } catch (BizException e) {
            errorMessage = String.format("今溪下单业务失败: %s", e.getMessage());
            log.error("今溪下单失败, 订单号: {}, 失败信息: {}", extension.getChannelOrderNo(), e.getMessage());
        } catch (Exception e) {
            errorMessage = "今溪下单未知错误";
            log.error("今溪下单未知错误, 订单号: {}", extension.getChannelOrderNo(), e);
        }
        // 软游通不是实时到账 要等待回调
        return new DispatchResultDTO(dispatchSuccess, realTime)
                .setSupplierRequest(reqBuild.length() > 0 ? reqBuild.toString() : null)
                .setSupplierResponse(respBuild.length() > 0 ? respBuild.toString() : null)
                .setSupplierOrderNo(supplierOrderNo)
                .setErrorMessage(errorMessage);
    }


    public JinXiBaseResponse<JinXiOrderRechargeResp> submitRechargeOrder(BenefitItemOrderDO orderDO, BenefitItemDO benefitItemDO, StringBuilder reqBuild, StringBuilder respBuild) {
        JinXiOrderRechargeReq req = new JinXiOrderRechargeReq();
        JSONObject param = JSONObject.parseObject(benefitItemDO.getSupplierGoodsParam());
        String productId = param.getString(SupplierGoodsParameters.GOODS_CODE.getKey());
        req.setBuyNumber(1);
        req.setCustomOrderId(orderDO.getSupplierRequestOrder());
        req.setExternal(null);
        req.setProductId(productId);
        req.setRechargeAccount(String.valueOf(orderDO.getOrderExtension().getMobile()));
        req.setRechargeTemplate(null);
        req.setVerifityCode(null);

        return jinXiManager.orderRecharge(req, reqBuild, respBuild);
    }


    private DispatchResultDTO dev(String supplierGoodsParam) {
        if (supplierGoodsParam.contains("yuelan_success")) {
            return new DispatchResultDTO(true, true)
                    .setSupplierRequest("【模拟】今溪 request")
                    .setSupplierResponse("【模拟】今溪-成功并且实时到账测试 response")
                    .setSupplierOrderNo("jinxi" + IdUtil.fastSimpleUUID())
                    .setErrorMessage(null);
        } else {
            return new DispatchResultDTO(false, false)
                    .setSupplierRequest("【模拟】今溪 request")
                    .setSupplierResponse("【模拟】今溪-失败测试 response")
                    .setSupplierOrderNo("jinxi" + IdUtil.fastSimpleUUID())
                    .setErrorMessage("jinxi err");
        }

    }
}

