package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccChannelDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2024/5/4 上午1:07
 */
public interface EccChannelDOMapper extends BaseMapper<EccChannelDO> {
    default EccChannelDO selectByChannelName(String channelName) {
        return selectOne(Wrappers.lambdaQuery(EccChannelDO.class)
                .eq(EccChannelDO::getChannelName, channelName));
    }

    /**
     * 使用递归CTE一次性查询渠道的所有下级渠道
     *
     * @param parentChannelId 父渠道ID
     * @return 所有下级渠道列表
     */
    List<EccChannelDO> selectAllDescendantChannels(@Param("parentChannelId") Long parentChannelId);
}