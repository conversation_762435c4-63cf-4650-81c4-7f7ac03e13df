package com.yuelan.hermes.quanyi.biz.service;

import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.biz.handler.CacheHandler;
import com.yuelan.hermes.quanyi.common.event.SoftGameOrderNotifyEvent;
import com.yuelan.hermes.quanyi.common.pojo.properties.SoftGameProperties;
import com.yuelan.hermes.quanyi.common.util.SoftGameUtil;
import com.yuelan.hermes.quanyi.remote.softgame.request.SoftGameNotifyReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 软游通v2服务类
 * 按照现有RytService的模式实现
 *
 * <AUTHOR> 2025/7/31
 * @since 2025/7/31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SoftGameService {

    private final SoftGameProperties softGameProperties;
    private final CacheHandler cacheHandler;
    private final ApplicationEventPublisher applicationEventPublisher;

    /**
     * 处理异步通知
     *
     * @param request 通知请求
     * @return 处理结果
     */
    public boolean handleNotify(SoftGameNotifyReq request) {
        String orderNo = request.getUserOrderId();
        RLock lock = cacheHandler.getCommOrderLock(orderNo);
        try {
            if (Objects.isNull(lock)) {
                log.error("软游通v2异步通知获取锁失败,orderId:{}", orderNo);
                // 订单正在处理请稍后回调
                return false;
            }
            // 1. 验证签名
            if (!verifyNotifySign(request)) {
                log.error("软游通v2异步通知签名验证失败: {}", JSON.toJSONString(request));
                return false;
            }

            SoftGameOrderNotifyEvent event = new SoftGameOrderNotifyEvent();
            event.setRequest(request);
            applicationEventPublisher.publishEvent(event);
            if (!event.getIsHandled()) {
                log.error("软游通v2异步通知未被处理: {}", JSON.toJSONString(request));
                return false;
            } else {
                return event.getSuccessDeal();
            }
        } catch (Exception e) {
            log.error("软游通v2异步通知处理异常: 订单号={}", request.getUserOrderId(), e);
            return false;
        } finally {
            cacheHandler.releaseLock(lock);
        }
    }

    /**
     * 验证异步通知签名
     *
     * @param request 通知请求
     * @return 验证结果
     */
    private boolean verifyNotifySign(SoftGameNotifyReq request) {
        String expectedSign = SoftGameUtil.generateNotifySign(
                request.getBusinessId(),
                request.getUserOrderId(),
                request.getStatus(),
                softGameProperties.getCurrentKey()
        );

        boolean isValid = SoftGameUtil.verifySign(expectedSign, request.getSign());
        if (!isValid) {
            log.error("软游通v2异步通知签名验证失败: 期望={}, 实际={}", expectedSign, request.getSign());
        }
        return isValid;
    }


}
