package com.yuelan.hermes.quanyi.biz.handler.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.yuelan.hermes.commons.enums.SpEnum;
import com.yuelan.hermes.quanyi.biz.handler.NumberCardServer;
import com.yuelan.hermes.quanyi.biz.service.EccAreaService;
import com.yuelan.hermes.quanyi.biz.service.ThreeFactorAuthService;
import com.yuelan.hermes.quanyi.common.enums.EccSpEnum;
import com.yuelan.hermes.quanyi.common.enums.NcOrderStatusEnum;
import com.yuelan.hermes.quanyi.common.enums.PhoneNumSelectType;
import com.yuelan.hermes.quanyi.common.pojo.bo.PhoneLocationBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.PhonePrettyTagBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccAreaDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccNcOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import com.yuelan.hermes.quanyi.controller.request.NcPhoneKeySearchReq;
import com.yuelan.hermes.quanyi.remote.XieYunManager;
import com.yuelan.hermes.quanyi.remote.request.XieYunGdSearchPhoneReq;
import com.yuelan.hermes.quanyi.remote.request.XieYunSubmitOrderReq;
import com.yuelan.hermes.quanyi.remote.request.XieYunSubmitPreCheckReq;
import com.yuelan.hermes.quanyi.remote.response.ThreeFactorAuthResp;
import com.yuelan.hermes.quanyi.remote.response.XieYunSubmitResult;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2025/3/19
 * @since 2025/3/19
 * <p>
 * 携云广电卡服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CbnServerByXieYunImpl implements NumberCardServer {

    private final XieYunManager xieYunManager;
    private final EccAreaService eccAreaService;
    private final ThreeFactorAuthService threeFactorAuthService;

    @Override
    public EccSpEnum getSpEnum() {
        return EccSpEnum.GD_XY;
    }

    @Override
    public PageData<PhonePrettyTagBO> selectPhoneNumPool(NcPhoneKeySearchReq req, EccProductDO productDO) {
        if (Objects.isNull(req.getProvinceCode()) || Objects.isNull(req.getCityCode())) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "请选择省市");
        }
        EccAreaDO phoneArea = eccAreaService.getOneAreaByNumCode(SpEnum.CBN, req.getProvinceCode(), req.getCityCode());
        if (Objects.isNull(phoneArea)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "省市信息错误");
        }
        String provinceName = phoneArea.getAreaName();
        String cityName = phoneArea.getSubAreaList().get(0).getAreaName();

        String provinceCode = phoneArea.getNumCode();
        String cityCode = phoneArea.getSubAreaList().get(0).getNumCode();

        List<String> highlightKeywordsList = Lists.newArrayList();

        List<String> phones = searchPhone(req, highlightKeywordsList);
        List<PhoneLocationBO> phoneLocationList = new ArrayList<>();
        for (String phone : phones) {
            PhoneLocationBO phoneLocationBO = new PhoneLocationBO();
            phoneLocationBO.setProvince(provinceName);
            phoneLocationBO.setCity(cityName);
            phoneLocationBO.setPhone(phone);
            phoneLocationBO.setProvinceCode(provinceCode);
            phoneLocationBO.setCityCode(cityCode);
            phoneLocationList.add(phoneLocationBO);
        }
        PageData<PhoneLocationBO> locationList = PageData.create(phoneLocationList, (long) phoneLocationList.size(), 1L, phoneLocationList.size());
        List<PhonePrettyTagBO> results = locationList.getList().stream()
                .map(itemBO -> PhonePrettyTagBO.buildBO(itemBO, highlightKeywordsList))
                .collect(Collectors.toList());
        return PageData.create(results, locationList.getTotal(), locationList.getPage(), locationList.getSize());
    }

    @Override
    public void getCard(EccNcOrderDO orderDo) {
        try {
            genUserId(orderDo);
            // 随机选号
            if (PhoneNumSelectType.RANDOM_SELECT.getCode().equals(orderDo.getSelectType())) {
                if (!randomChosePhone(orderDo)) {
                    return;
                }
            }
            // 地址检测 和 三要素检测
            if (!addressAndThreeFactorCheck(orderDo)) {
                orderDo.setOrderStatus(NcOrderStatusEnum.GET_CARD_FAIL.getCode());
                return;
            }
            // 占号
            if (!occupyPhone(orderDo)) {
                orderDo.setOrderStatus(NcOrderStatusEnum.GET_CARD_FAIL.getCode());
                return;
            }
            // 预校验
            if (!preCheck(orderDo)) {
                orderDo.setOrderStatus(NcOrderStatusEnum.GET_CARD_FAIL.getCode());
                return;
            }
            // 订单提交
            XieYunSubmitResult submitResult = submitOrder(orderDo);
            if (submitResult == null) {
                orderDo.setOrderStatus(NcOrderStatusEnum.GET_CARD_FAIL.getCode());
                return;
            }
            orderDo.setSpOrderNo(submitResult.getOrderId());
            orderDo.setPhone(submitResult.getPhone());
            orderDo.setOrderStatus(NcOrderStatusEnum.AUDITING.getCode());
        } catch (Exception e) {
            log.error("携云广电卡下单未知异常", e);
            orderDo.setOrderStatus(NcOrderStatusEnum.GET_CARD_FAIL.getCode());
            orderDo.setFailReason("系统内部错误");
        }
    }


    private void genUserId(EccNcOrderDO orderDO) {
        // 用户ID = 身份证后 6 位+年月日+6位随机数
        orderDO.setExt(StrUtil.subSufByLength(orderDO.getIdCard(), 6) + RandomUtil.randomNumbers(6));
    }

    /**
     * 随机选号
     * <p>
     * 携云广电卡不支持随机选号 自己实现随机选号
     *
     * @param orderDo 订单信息
     */
    private boolean randomChosePhone(EccNcOrderDO orderDo) {
        // 查询出收货地址是哪里 号码归属地也设置成这里
        EccAreaDO location = eccAreaService.getOneAreaByCode(SpEnum.CBN, orderDo.getPostProvinceCode(), orderDo.getPostCityCode(), orderDo.getPostDistrictCode());
        String provinceCode = location.getNumCode();
        EccAreaDO cityArea = location.getSubAreaList().get(0);
        orderDo.setProvince(location.getAreaName());
        orderDo.setProvinceCode(provinceCode);
        orderDo.setCity(cityArea.getAreaName());
        orderDo.setCityCode(cityArea.getNumCode());

        // 优先选同归属地的
        try {
            // 随机选号
            NcPhoneKeySearchReq req = new NcPhoneKeySearchReq();
            req.setCityCode(orderDo.getCityCode());
            List<String> highlightKeywordsList = Lists.newArrayList();
            String randomPhone = searchPhone(req, highlightKeywordsList).get(0);

            orderDo.setPhone(randomPhone);
            if (Objects.isNull(orderDo.getPhone())) {
                orderDo.setOrderStatus(NcOrderStatusEnum.GET_CARD_FAIL.getCode());
                orderDo.setFailReason("随机选号失败");
            } else {
                return true;
            }
        } catch (Exception e) {
            orderDo.setOrderStatus(NcOrderStatusEnum.GET_CARD_FAIL.getCode());
            orderDo.setFailReason("随机选号异常");
            log.error("随机选号异常", e);
        }
        return false;

    }


    public boolean addressAndThreeFactorCheck(EccNcOrderDO orderDO) {

        try {
            boolean addressPreCheck = xieYunManager.addressPreCheck(orderDO.getAddress());
            if (!addressPreCheck) {
                orderDO.setFailReason("根据规定，[学校/快递点/商业场所]类地址暂不支持配送。请提供常住地址或写字楼地址（如：XX小区X单元X室）");
                return false;
            }
        } catch (Exception e) {
            orderDO.setFailReason("地址校验异常，请稍后再试");
            log.error("携云广电卡地址校验异常", e);
            return false;
        }
        try {
            ThreeFactorAuthResp threeFactorAuthResp = threeFactorAuthService.threeFactorAuth(orderDO.getIdCardName(), orderDO.getIdCard(), orderDO.getContactPhone());
            if (!threeFactorAuthResp.isSuccess()) {
                orderDO.setFailReason(threeFactorAuthResp.getMsg());
                return false;
            }
            return true;
        } catch (Exception e) {
            handleException(e, orderDO, "身份信息校验失败：系统异常");
            return false;
        }
    }

    /**
     * 占号
     *
     * @param orderDO 订单信息
     */
    private boolean occupyPhone(EccNcOrderDO orderDO) {
        try {
            orderDO.setSelectType(PhoneNumSelectType.USER_SELECT.getCode());
            xieYunManager.occupyPhone(orderDO.getCityCode(), orderDO.getPhone(), orderDO.getIdCard());
            return true;
        } catch (BizException e) {
            handleException(e, orderDO, "选占号码失败：" + e.getMessage());
        } catch (Exception e) {
            handleException(e, orderDO, "选占号码失败：系统异常");
        }
        return false;
    }

    /**
     * 预校验
     *
     * @param orderDO 订单信息
     */
    private boolean preCheck(EccNcOrderDO orderDO) {
        try {
            xieYunManager.submitPreCheck(buildPreCheckReq(orderDO));
            return true;
        } catch (BizException e) {
            handleException(e, orderDO, "预校验失败：" + e.getMessage());
        } catch (Exception e) {
            handleException(e, orderDO, "预校验失败：系统异常");
        }
        return false;
    }

    /**
     * 订单提交
     *
     * @param orderDO 订单信息
     */
    private XieYunSubmitResult submitOrder(EccNcOrderDO orderDO) {
        try {
            return xieYunManager.submitOrder(buildSubmitOrderReq(orderDO));
        } catch (BizException e) {
            String message = e.getMessage();
            if (Objects.nonNull(message) && message.startsWith("订单提交失败")) {
                handleException(e, orderDO, e.getMessage());
            } else {
                handleException(e, orderDO, "订单提交失败：" + e.getMessage());
            }
        } catch (Exception e) {
            handleException(e, orderDO, "订单提交失败：系统异常");
        }
        return null;
    }

    private void handleException(Exception e, EccNcOrderDO eccGdOrderDO, String failReason) {
        eccGdOrderDO.setFailReason(failReason);
        log.error(failReason, e);
    }

    private XieYunSubmitOrderReq buildSubmitOrderReq(EccNcOrderDO eccGdOrderDO) {
        XieYunSubmitOrderReq req = new XieYunSubmitOrderReq();
        XieYunSubmitPreCheckReq preCheckReq = buildPreCheckReq(eccGdOrderDO);
        // 复制字段
        req.setPhone(preCheckReq.getPhone());
        // 号码-省份
        req.setProvinceCode(preCheckReq.getProvinceCode());
        // 号码-市
        req.setAreaCode(preCheckReq.getAreaCode());
        req.setUserIdType(preCheckReq.getUserIdType());
        req.setOrderRemark(preCheckReq.getOrderRemark());
        req.setOfferId(preCheckReq.getOfferId());
        req.setUserId(preCheckReq.getUserId());
        req.setOrderRecvAddr(preCheckReq.getOrderRecvAddr());
        req.setCustInfo(preCheckReq.getCustInfo());

        // 设置 XieYunSubmitOrderReq 特有的字段
        req.setAreaName(eccGdOrderDO.getPostCity());
        // 快递-区县
        req.setRegionCode(eccGdOrderDO.getPostDistrictCode());
        // 号码-市
        req.setCityCode(eccGdOrderDO.getCityCode());
        req.setCityName(eccGdOrderDO.getCity());
        req.setOneCardCheck(null);
        req.setSocialSecurity(null);
        return req;
    }

    private XieYunSubmitPreCheckReq buildPreCheckReq(EccNcOrderDO eccGdOrderDO) {
        XieYunSubmitPreCheckReq req = new XieYunSubmitPreCheckReq();
        req.setPhone(eccGdOrderDO.getPhone());
        req.setProvinceCode(eccGdOrderDO.getProvinceCode());
        req.setAreaCode(eccGdOrderDO.getPostCityCode());
        req.setUserIdType("1");
        req.setOrderRemark(null);
        req.setOfferId(eccGdOrderDO.getSpGoodsId());
        // 用户ID = 身份证后 6 位+年月日+6位随机数
        req.setUserId(eccGdOrderDO.getExt());

        XieYunSubmitPreCheckReq.OrderRecvAddr orderRecvAddr = new XieYunSubmitPreCheckReq.OrderRecvAddr();
        orderRecvAddr.setReceiveName(eccGdOrderDO.getIdCardName());
        orderRecvAddr.setReceiveCountry("中国");
        orderRecvAddr.setReceiveProvince(eccGdOrderDO.getPostProvinceCode());
        orderRecvAddr.setReceiveProvinceName(eccGdOrderDO.getPostProvince());
        orderRecvAddr.setReceiveCity(eccGdOrderDO.getPostCityCode());
        orderRecvAddr.setReceiveCityName(eccGdOrderDO.getPostCity());
        // 区县
        orderRecvAddr.setReceiveCounty(eccGdOrderDO.getPostDistrictCode());
        orderRecvAddr.setReceiveCountyName(eccGdOrderDO.getPostDistrict());

        orderRecvAddr.setReceiveAddress(eccGdOrderDO.getAddress());
        orderRecvAddr.setReceiveMobile(eccGdOrderDO.getContactPhone());

        XieYunSubmitPreCheckReq.CustInfo custInfo = new XieYunSubmitPreCheckReq.CustInfo();
        custInfo.setCustName(eccGdOrderDO.getIdCardName());
        custInfo.setIdenType("1");
        custInfo.setIdenNr(eccGdOrderDO.getIdCard());
        custInfo.setContNumber(eccGdOrderDO.getContactPhone());

        req.setOrderRecvAddr(orderRecvAddr);
        req.setCustInfo(custInfo);
        return req;
    }


    /**
     * 搜索号码
     */
    public List<String> searchPhone(NcPhoneKeySearchReq req, List<String> highlightKeywordsList) {
        List<String> resPhones = new ArrayList<>();
        try {
            XieYunGdSearchPhoneReq searchPhoneReq = new XieYunGdSearchPhoneReq(req.getCityCode());
            if (StrUtil.isNotBlank(req.getPhoneKey())) {
                searchPhoneReq.setNumFuzzy(req.getPhoneKey());
                highlightKeywordsList.add(req.getPhoneKey());
            }
            searchPhoneReq.setPageSize("200");
            long startTime = System.currentTimeMillis();
            int timeoutSeconds = 7; // 设置超时时间为7秒
            boolean timeout = false;
            if (StrUtil.isBlank(req.getPhoneKey())) {
                Set<String> phoneSet = new LinkedHashSet<>();
                int searchCount = 0;
                // 如果没有传入号码关键字，随机查询一些优质号码
                List<String> ruleList = Lists.newArrayList();
                ruleList.add("123");
                ruleList.add("234");
                ruleList.add("345");
                ruleList.add("456");
                ruleList.add("567");
                ruleList.add("678");
                ruleList.add("789");
                ruleList.add("987");
                ruleList.add("876");
                ruleList.add("765");
                ruleList.add("765");
                ruleList.add("654");
                ruleList.add("543");
                ruleList.add("432");
                ruleList.add("321");
                List<String> tagList = Lists.newArrayList();
                tagList.add("0");
                tagList.add("1");
                tagList.add("2");
                // 查询 3 次
                Set<String> useRuleSet = new LinkedHashSet<>();
                while (phoneSet.size() < 10 && searchCount < 5 && !timeout) {
                    searchCount++;
                    // 随机选择规则
                    String rule = ruleList.get(RandomUtil.randomInt(ruleList.size()));
                    while (useRuleSet.contains(rule)) {
                        rule = ruleList.get(RandomUtil.randomInt(ruleList.size()));
                    }
                    useRuleSet.add(rule);
                    // 随机选择模糊查询类型
                    String tag = tagList.get(RandomUtil.randomInt(tagList.size()));
                    searchPhoneReq.setFuzzyTag(tag);
                    searchPhoneReq.setFuzzyKey(rule);
                    List<String> phoneList = xieYunManager.selectPhonePool(searchPhoneReq);
                    if (phoneList.isEmpty()) {
                        continue;
                    }
                    highlightKeywordsList.add(rule);
                    phoneSet.addAll(phoneList);
                    long elapsedTime = System.currentTimeMillis() - startTime;
                    if (elapsedTime > timeoutSeconds * 1000) {
                        timeout = true; // 超时
                    }
                }
                resPhones.addAll(phoneSet);
            }
            if (resPhones.size() < 10) {
                searchPhoneReq.setFuzzyTag(null);
                searchPhoneReq.setFuzzyKey(null);
                resPhones.addAll(xieYunManager.selectPhonePool(searchPhoneReq));
            }
            return resPhones;
        } catch (BizException e) {
            log.error("携云广电卡号码搜索异常", e);
        }
        return new ArrayList<>();
    }

}
