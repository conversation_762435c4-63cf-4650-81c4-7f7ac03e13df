package com.yuelan.hermes.quanyi.biz.manager;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.boot.utils.HttpServletRequestUtil;
import com.yuelan.hermes.quanyi.biz.service.EccImeiBindService;
import com.yuelan.hermes.quanyi.biz.service.EccImeiService;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccImeiBindDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccImeiDO;
import com.yuelan.hermes.quanyi.controller.request.ImeiActivationStatusQueryReq;
import com.yuelan.hermes.quanyi.controller.response.ImeiActivationStatusResp;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;
import java.util.Random;

/**
 * <AUTHOR> 2025/6/13
 * @since 2025/6/13
 */
@Component
@AllArgsConstructor
public class ImeiManager {

    private final ApiChannelManager apiChannelManager;
    private final EccImeiService eccImeiService;
    private final EccImeiBindService eccImeiBindService;

    private static final Random random = new Random();

    public static LocalDate generateDeliveryDate() {
        // 基准时间是30天前
        LocalDate startTime = LocalDate.now().plusDays(-30);

        // 目标：生成一个1到30之间的天数偏移量
        // 高峰期：过去3-7天。在我们的计算中，这对应偏移量的 23-27
        // (今天-30) + 23 = 今天-7
        // (今天-30) + 27 = 今天-3
        // 所以我们让均值落在25
        double mean = 25.0;

        // 增大标准差，让分布更广泛
        double stdDev = 6.0; // 原先是 2.0

        int daysOffset;
        do {
            // 生成一个符合高斯分布的随机数
            daysOffset = (int) Math.round(random.nextGaussian() * stdDev + mean);
        } while (daysOffset < 1 || daysOffset >= 30); // 确保偏移量在1-30天范围内

        return startTime.plusDays(daysOffset);
    }

    public static LocalDateTime generateDeliveryDateTime() {
        // 1. 先获取一个随机日期
        LocalDate date = generateDeliveryDate();

        // 2. 定义时间范围
        LocalTime startTime = LocalTime.of(9, 0);
        LocalTime endTime = LocalTime.of(18, 0);

        // 3. 计算范围内的总秒数
        long totalSecondsInRange = Duration.between(startTime, endTime).getSeconds();

        // 4. 生成一个随机秒数 (nextInt的bound是互斥的，所以+1使其包含18:00:00)
        long randomSecondsToAdd = random.nextInt((int) totalSecondsInRange + 1);

        // 5. 计算出最终的随机时间
        LocalTime randomTime = startTime.plusSeconds(randomSecondsToAdd);

        // 6. 合并日期和时间
        return date.atTime(randomTime);
    }

    public ImeiActivationStatusResp activationStatus(ImeiActivationStatusQueryReq req) {
        String ip = ServletUtil.getClientIP(HttpServletRequestUtil.getCurrentRequest());
        String sign = req.getSign();
        req.setSign(null);
        String imei = req.getImei();
        apiChannelManager.verifySignAndIp(ip, req.getApiKey(), sign, JSONObject.from(req));
        // 数据库中没有该IMEI
        ImeiActivationStatusResp resp = new ImeiActivationStatusResp();
        EccImeiDO dbImei = eccImeiService.getByImei(imei);
        if (dbImei == null) {
            resp.setImei(imei);
            resp.setValid(Boolean.FALSE);
            return resp;
        }
        LocalDateTime deliveryTime = dbImei.getDeliveryTime();
        if (Objects.isNull(deliveryTime)) {
            deliveryTime = generateDeliveryDateTime();
            eccImeiService.updateDeliveryTimeByImei(imei, deliveryTime);
        }
        resp.setImei(imei);
        resp.setValid(Boolean.TRUE);
        resp.setDeliveryTime(deliveryTime);
        EccImeiBindDO lastBindRecord = eccImeiBindService.findLastBindRecordByImei(imei);
        if (lastBindRecord != null) {
            // 已经激活
            resp.setActivated(Boolean.TRUE);
            resp.setActivationTime(lastBindRecord.getCreateTime());
        } else {
            // 未激活
            resp.setActivated(Boolean.FALSE);
        }
        return resp;
    }



}
