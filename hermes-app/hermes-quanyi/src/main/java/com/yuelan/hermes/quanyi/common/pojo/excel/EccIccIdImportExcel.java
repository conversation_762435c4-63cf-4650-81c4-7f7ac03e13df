package com.yuelan.hermes.quanyi.common.pojo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * ICCID导入Excel模板
 *
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Data
@ContentRowHeight(25)
@HeadRowHeight(35)
public class EccIccIdImportExcel {

    @ColumnWidth(25)
    @ExcelProperty(value = "ICCID编码")
    @NumberFormat("@")  // @ 表示文本格式，避免长数字变成科学计数法
    @Schema(description = "ICCID编码")
    private String iccId;


    @ColumnWidth(15)
    @ExcelProperty(value = "最晚启用日期")
    @DateTimeFormat("yyyy-MM-dd")
    @Schema(description = "最晚启用日期(YYYY-MM-DD格式)")
    private String validityPeriod;
}
