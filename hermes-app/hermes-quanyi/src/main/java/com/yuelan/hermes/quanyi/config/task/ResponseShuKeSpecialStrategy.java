package com.yuelan.hermes.quanyi.config.task;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.common.enums.SuccessStrategyEnum;
import com.yuelan.hermes.quanyi.common.interfaces.SuccessStrategy;
import com.yuelan.hermes.quanyi.common.pojo.domain.HttpAsyncTask;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2025/3/21
 * @since 2025/3/21
 */
@Component
public class ResponseShuKeSpecialStrategy implements SuccessStrategy {

    @Override
    public boolean isSuccess(HttpAsyncTask task) {
        String lastResponse = task.getLastResponse();
        String remark = task.getRemark();
        if (lastResponse == null) {
            return false;
        }

        try {
            JSONObject jsonObject = JSON.parseObject(lastResponse);
            String status = jsonObject.getString("status");
            String message = jsonObject.getString("message");
            if ("error".equals(status) || "无效订单号".equals(message)) {
                return false;
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public String getType() {
        return SuccessStrategyEnum.RESPONSE_SHU_KE_SPECIAL.getCode();
    }
}
