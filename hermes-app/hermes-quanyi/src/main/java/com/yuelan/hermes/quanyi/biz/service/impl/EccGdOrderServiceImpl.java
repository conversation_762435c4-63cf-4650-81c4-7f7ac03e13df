package com.yuelan.hermes.quanyi.biz.service.impl;

import akka.protobufv3.internal.ServiceException;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuelan.hermes.quanyi.biz.handler.FileExportTaskHandler;
import com.yuelan.hermes.quanyi.biz.service.EccChannelDOService;
import com.yuelan.hermes.quanyi.biz.service.EccGdOrderService;
import com.yuelan.hermes.quanyi.biz.service.EccOuterChannelDOService;
import com.yuelan.hermes.quanyi.biz.service.FileExportTaskDOService;
import com.yuelan.hermes.quanyi.common.enums.FileExportTaskCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccGdOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.FileExportTaskDO;
import com.yuelan.hermes.quanyi.common.util.MpPageUtil;
import com.yuelan.hermes.quanyi.controller.request.EccGdOrderListReq;
import com.yuelan.hermes.quanyi.controller.response.EccGdOrderResp;
import com.yuelan.hermes.quanyi.controller.response.FileExportTaskCreateResp;
import com.yuelan.hermes.quanyi.mapper.EccGdOrderMapper;
import com.yuelan.hermes.quanyi.remote.WoAiNumManager;
import com.yuelan.hermes.quanyi.remote.response.WaNumOrderDetailResp;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/11/20
 * @since 2024/11/20
 */
@Service
@AllArgsConstructor
public class EccGdOrderServiceImpl extends ServiceImpl<EccGdOrderMapper, EccGdOrderDO> implements EccGdOrderService, FileExportTaskHandler {

    private final EccGdOrderMapper gdOrderMapper;
    private final EccOuterChannelDOService outerChannelDOService;
    private final EccChannelDOService eccChannelDOService;
    private final FileExportTaskDOService fileExportTaskDOService;
    private final WoAiNumManager woAiNumManager;


    @Override
    public EccGdOrderDO getByOrderNo(String orderNo) {
        return getOne(Wrappers.<EccGdOrderDO>lambdaQuery().eq(EccGdOrderDO::getOrderNo, orderNo));
    }

    @Override
    public PageData<EccGdOrderResp> pageList(EccGdOrderListReq req, long adminId) {
        IPage<EccGdOrderDO> page = MpPageUtil.convertPageRequest(req);
        List<Long> outChannelLimit = outerChannelDOService.selectLimitChannelIds(adminId);
        List<Long> innerChannelLimit = eccChannelDOService.selectLimitChannelIds(adminId);
        page = gdOrderMapper.selectPage(page, req.buildQueryWrapper(outChannelLimit, innerChannelLimit));
        List<EccGdOrderResp> respList = page.getRecords().stream()
                .map(EccGdOrderResp::buildResp)
                .collect(Collectors.toList());
        return PageData.create(respList, page.getTotal(), req.getPage(), req.getSize());
    }

    @Override
    public FileExportTaskCreateResp export(EccGdOrderListReq req, long adminId) {
        if (req.getOrderTimeEnd() == null) {
            // 防止数据一直在变化
            req.setOrderTimeEnd(LocalDateTime.now());
        } else {
            // 如果结束时间大于当前时间
            if (req.getOrderTimeEnd().isAfter(LocalDateTime.now())) {
                req.setOrderTimeEnd(LocalDateTime.now());
            }
        }
        String param = JSONObject.toJSONString(req);
        JSONObject paramJson = JSONObject.parseObject(param);
        paramJson.put("adminId", adminId);
        FileExportTaskDO taskDO = fileExportTaskDOService.createDefaultTask(paramJson.toString(), getTaskCode(), "xlsx");
        return new FileExportTaskCreateResp(taskDO.getTaskId(), taskDO.getFileName());
    }

    @Override
    public WaNumOrderDetailResp.OrderDetail orderOperatorRecord(Long gdOrderId) {
        EccGdOrderDO gdOrderDO = gdOrderMapper.selectById(gdOrderId);
        if (Objects.isNull(gdOrderDO)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "非数字格式");
        }
        try {
            WaNumOrderDetailResp response = woAiNumManager.queryOrder(gdOrderDO.getOrderNo());
            if (response.isSuccessful()) {
                return response.getData();
            } else {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "查询失败");
            }
        } catch (ServiceException e) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "系统异常");
        }
    }

    @Override
    public void updateOrderStatus(Long gdOrderId, Integer code) {
        update(Wrappers.<EccGdOrderDO>lambdaUpdate().eq(EccGdOrderDO::getGdOrderId, gdOrderId)
                .set(EccGdOrderDO::getOrderStatus, code));
    }

    @Override
    public FileExportTaskCodeEnum getTaskCode() {
        return FileExportTaskCodeEnum.ECC_GD_ORDER_EXPORT;
    }

    @Override
    public void generateExportFile(FileExportTaskDO taskDO) {
        String evalParam = taskDO.getEvalParam();
        JSONObject paramJson = JSONObject.parseObject(evalParam);
        Long adminId = paramJson.getLong("adminId");
        paramJson.remove("adminId");
        EccGdOrderListReq req = JSONObject.parseObject(paramJson.toJSONString(), EccGdOrderListReq.class);
        boolean hasNextPage;

        IPage<EccGdOrderDO> page = new PageDTO<>(1, 1000);
        List<Long> outChannelLimit = outerChannelDOService.selectLimitChannelIds(adminId);
        List<Long> innerChannelLimit = eccChannelDOService.selectLimitChannelIds(adminId);
        ExcelWriter excelWriter = EasyExcel.write(taskDO.getFilePath(), EccGdOrderResp.class).build();
        try {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            do {
                page = gdOrderMapper.selectPage(page, req.buildQueryWrapper(outChannelLimit, innerChannelLimit));
                List<EccGdOrderResp> respList = page.getRecords().stream()
                        .map(EccGdOrderResp::buildResp).collect(Collectors.toList());
                excelWriter.write(respList, writeSheet);
                hasNextPage = page.getCurrent() < page.getPages();
                page.setCurrent(page.getCurrent() + 1);
            } while (hasNextPage);
        } finally {
            excelWriter.finish();
        }
    }
}
