package com.yuelan.hermes.quanyi.biz.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuelan.hermes.commons.enums.SupplierEnum;
import com.yuelan.hermes.commons.enums.UpOffStatusEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitGoodsDO;
import com.yuelan.hermes.quanyi.common.util.MpPageUtil;
import com.yuelan.hermes.quanyi.controller.request.BenefitGoodsListReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitGoodsSaveReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitGoodsUpdateStatusReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitGoodsResp;
import com.yuelan.hermes.quanyi.controller.response.SupplierResp;
import com.yuelan.hermes.quanyi.mapper.BenefitGoodsMapper;
import com.yuelan.hermes.quanyi.mapper.BenefitProductItemMapper;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/4/2 16:16
 */
@Service
@RequiredArgsConstructor
public class BenefitGoodsService extends ServiceImpl<BenefitGoodsMapper, BenefitGoodsDO> {
    private static final List<SupplierEnum> SUPPLIER_ENUM_LIST = Arrays.asList(SupplierEnum.RYT, SupplierEnum.TE_ZHEN,SupplierEnum.BAO_MI_HUA);
    private final BenefitGoodsMapper benefitGoodsMapper;
    private final BenefitProductItemMapper benefitProductItemMapper;
    private final BenefitItemService benefitItemService;

    /**
     * 权益商品更新
     *
     * @param req 权益商品更新数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateById(BenefitGoodsSaveReq req) {
        BenefitGoodsDO dbGoods = getById(req.getGoodsId());
        if (Objects.isNull(dbGoods)) {
            throw BizException.create(BizErrorCodeEnum.GOODS_NOT_EXIST);
        }
        // 上架状态不允许编辑 核心参数
        if (UpOffStatusEnum.UP.getCode().equals(dbGoods.getGoodsStatus())) {
            if (!Objects.equals(dbGoods.getSupplierType(), req.getSupplierType())) {
                throw BizException.create(BizErrorCodeEnum.GOODS_STATUS_ERROR, "上架状态不允许编辑供应商类型");
            }
            if (!Objects.equals(dbGoods.getSupplierGoodsNo(), req.getSupplierGoodsNo())) {
                throw BizException.create(BizErrorCodeEnum.GOODS_STATUS_ERROR, "上架状态不允许编辑供应商商品编号");
            }
        }
        checkSupplier(req.getSupplierType());
        BenefitGoodsDO goodsDO = BenefitGoodsSaveReq.convert(req);
        updateById(goodsDO);

        // 同步更新到新权益商品库
        BenefitGoodsDO syncGoods = getById(goodsDO.getGoodsId());
        benefitItemService.syncBenefitGoods(syncGoods);
    }

    /**
     * 权益商品保存
     *
     * @param req 权益商品保存数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BenefitGoodsSaveReq req) {
        checkSupplier(req.getSupplierType());
        req.setGoodsId(null);
        BenefitGoodsDO goodsDO = BenefitGoodsSaveReq.convert(req);
        goodsDO.setGoodsStatus(UpOffStatusEnum.UP.getCode());
        save(goodsDO);

        // 同步更新到新权益商品库
        BenefitGoodsDO syncGoods = getById(goodsDO.getGoodsId());
        benefitItemService.syncBenefitGoods(syncGoods);
    }

    /**
     * 权益商品分页列表
     *
     * @param req 分页和查询参数
     * @return 分页数据
     */
    public PageData<BenefitGoodsResp> list(BenefitGoodsListReq req) {
        IPage<BenefitGoodsDO> page = MpPageUtil.convertPageRequest(req);
        page = benefitGoodsMapper.selectPage(page, BenefitGoodsListReq.buildQueryWrapper(req));
        List<BenefitGoodsResp> respList = page.getRecords().stream()
                .map(BenefitGoodsResp::buildResp).collect(Collectors.toList());
        return PageData.create(respList, page.getTotal(), req.getPage(), req.getSize());
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateStatusById(BenefitGoodsUpdateStatusReq req) {
        if (Objects.equals(req.getGoodsStatus(), UpOffStatusEnum.OFF.getCode())) {
            Integer goodsIdUseCount = benefitProductItemMapper.selectCountByGoodsId(req.getGoodsId());
            if (goodsIdUseCount != 0) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "商品正在权益包中使用，不允许下架");
            }
        }
        Wrapper<BenefitGoodsDO> wrapper = BenefitGoodsUpdateStatusReq.buildUpdateWrapper(req);
        update(wrapper);

        // 同步更新到新权益商品库
        BenefitGoodsDO syncGoods = getById(req.getGoodsId());
        benefitItemService.syncBenefitGoods(syncGoods);
    }

    /**
     * 返回权益支持的2个供应商
     */
    public List<SupplierResp> getSuppliers() {
        List<SupplierResp> rests = new ArrayList<>();
        for (SupplierEnum supplierEnum : SUPPLIER_ENUM_LIST) {
            SupplierResp resp = SupplierResp.buildResp(supplierEnum);
            rests.add(resp);
        }
        return rests;
    }

    /**
     * 校验是否支持供应商类型
     *
     * @param supplierType 供应商类型
     */
    private void checkSupplier(Integer supplierType) {
        for (SupplierEnum supplierEnum : SUPPLIER_ENUM_LIST) {
            if (supplierEnum.getCode().equals(supplierType)) {
                return;
            }
        }
        throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "权益商品不支持该供应商");
    }
}
