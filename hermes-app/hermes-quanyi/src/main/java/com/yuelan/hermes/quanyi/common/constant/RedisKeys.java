package com.yuelan.hermes.quanyi.common.constant;

import cn.hutool.crypto.SecureUtil;
import com.yuelan.hermes.commons.enums.SmsCodeType;
import com.yuelan.hermes.quanyi.common.enums.BenefitPayChannelEnum;

/**
 * <AUTHOR> 2024/3/21 19:18
 */
public class RedisKeys {
    /**
     * 账号登录限制
     */
    private static final String ADMIN_LOGIN_COUNT_LIMIT = "ADMIN_LOGIN_COUNT_LIMIT:%s";
    /**
     * 雪花算法workId
     */
    public static final String SNOWFLAKE_WORK_ID = "SNOWFLAKE_WORK_ID:";
    /**
     * 咪咕回调处理中
     */
    private static final String MIGU_NOTIFY_PROCESSING = "MIGU_NOTIFY_PROCESSING:";
    /**
     * 权益下单
     */
    private static final String QUANYI_ORDER = "QUANYI_ORDER";
    /**
     * [/ecc/listProvince]省份数据缓存
     */
    public static final String ECC_PROVINCE_CODE = "ECC_PROVINCE_CODE";

    public static final String ECC_NUM_CODE = "ECC_NUM_CODE";
    public static final String ECC_NUM_CODE_NAME = "ECC_NUM_CODE_NAME";

    /**
     * 渠道请求省市区 请求限制
     */
    public static final String ECC_CHANNEL_REQUEST_LIMIT = "ECC_CHANNEL_REQUEST_LIMIT";

    /**
     * 今日发送短信次数
     */
    private static final String SMS_CODE_SEND_COUNT_DAY_KEY = "SMS_CODE_SEND_COUNT_DAY:%s:%s:%s";
    /**
     * 验证码验证次数
     */
    private static final String SMS_CODE_VERIFY_COUNT_KEY = "SMS_CODE_VERIFY_COUNT:%s:%s";
    /**
     * ip发送短信次数
     */
    private static final String SMS_CODE_SEND_COUNT_IP_KEY = "SMS_CODE_SEND_COUNT_IP:%s:%s";
    /**
     * 短信发送频繁  已达限制 (一分钟内)
     */
    private static final String SMS_CODE_SEND_MINUTE_LIMIT_KEY = "SMS_CODE_SEND_MINUTE_LIMIT_KEY:%s:%s";
    /**
     * 短信发送频繁  已达限制 (今日)
     */
    private static final String SMS_CODE_SEND_DAY_LIMIT_KEY = "SMS_CODE_SEND_DAY_LIMIT_KEY:%s:%s:%s";
    /**
     * ip获取频繁 限制24小内不获取才能恢复获取
     */
    private static final String SMS_CODE_IP_GET_LIMIT_KEY = "SMS_CODE_IP_GET_LIMIT_KEY:%s:%s";
    /**
     * 短信验证码缓存key
     */
    private static final String SMS_CODE_KEY = "SMS_CODE:%s:%s";

    /**
     * GamingOrderDistributeFailJob
     * 最后处理到的id
     */
    public static final String GODFJ_LAST_ID = "GODFJ_LAST_ID";

    /**
     * 索游 联通阅读通道请求的token
     */
    private static final String SY_UNICOM_TOKEN = "SY_UNICOM_TOKEN:";

    /**
     * 内蒙古移动通道请求的token
     */
    public static final String NMG_YD_TOKEN = "NMG_YD_TOKEN:";

    /**
     * 内蒙古移动随机码
     */
    private static final String NMG_YD_RANDOM_CODE = "NMG_YD_RANDOM_CODE:%s";

    /**
     * 江苏联通用户推荐商品
     */
    private static final String JIANG_SU_UNICOM_PRODUCT_KEY = "JIANG_SU_UNICOM:PRODUCT_KEY:%s";


    /**
     * 用户领取兑换成功回调咪咕
     */
    private static final String MG_ORDER_ITEM_OBTAINED_ORDER = "MG_ORDER_ITEM_OBTAINED_ORDER:%s";

    /**
     * 携云token缓存
     */
    public static final String XY_TOKEN = "XY_TOKEN";

    /**
     * 江西联通获取token
     */
    private static final String JIANG_XI_UNICOM_TOKEN_KEY = "JIANG_XI_UNICOM:TOKEN_KEY:%s";

    /**
     * 河北移动token缓存
     */
    private static final String HB_CMCC_TOKEN = "HB_CMCC_TOKEN:%S";

    /**
     * 河北移动 客户归属地 缓存
     */
    private static final String HB_CMCC_USER_KEY = "HB_CMCC_USER_KEY:%S";

    /**
     * 权益日志发送短信日志保存
     * BENEFIT_ORDER_LOG:payChannelId:phone
     */
    public static final String BENEFIT_ORDER_LOG = "BENEFIT_ORDER_LOG:%s:%s";

    /**
     * 海南电信手机号码对应订单号缓存
     */
    private static final String HAINAN_ORDER_NO = "HAINAN_ORDER_NO:%s";

    /**
     * 海南通用缓存
     */
    public static final String HAINAN_CACHE = "HAINAN_CACHE:%s";

    /**
     * 咪咕 relatePropsId 缓存
     */
    public static final String MIGU_FUN_CACHE = "MIGU_FUN_CACHE:%s";

    /**
     * 江西联通流水号缓存
     */
    public static final String JIANG_XI_UNICOM_FLOW = "JIANG_XI_UNICOM_FLOW:%s";

    /**
     * 权益通用号码缓存
     */
    public static final String BENEFIT_NUMBER = "BENEFIT_NUMBER:%s:%s";

    /**
     * ip 归属地
     */
    public static final String IP_LOCATION = "IP_LOCATION:%s";

    /**
     * 三要素认证结果
     */
    public static final String THREE_FACTOR_AUTH_RESULT = "THREE_FACTOR_AUTH_RESULT:%s";


    public static String getAdminLoginCountLimitKey(String adminAccount) {
        return String.format(ADMIN_LOGIN_COUNT_LIMIT, adminAccount);
    }

    public static String getSmsCodeSendCountDayKey(String phone, SmsCodeType type, String day) {
        return String.format(SMS_CODE_SEND_COUNT_DAY_KEY, phone, type.getType(), day);
    }

    public static String getSmsCodeVerifyCountKey(String phone, SmsCodeType smsCodeType) {
        return String.format(SMS_CODE_VERIFY_COUNT_KEY, phone, smsCodeType.getType());
    }

    public static String getSmsCodeSendCountIpKey(String ip, SmsCodeType smsCodeType) {
        return String.format(SMS_CODE_SEND_COUNT_IP_KEY, ip, smsCodeType.getType());
    }

    public static String getSmsCodeSendMinuteLimitKey(String phone, SmsCodeType smsCodeType) {
        return String.format(SMS_CODE_SEND_MINUTE_LIMIT_KEY, phone, smsCodeType.getType());
    }

    public static String getSmsCodeSendDayLimitKey(String phone, SmsCodeType smsCodeType, String day) {
        return String.format(SMS_CODE_SEND_DAY_LIMIT_KEY, phone, smsCodeType.getType(), day);
    }

    public static String getSmsCodeIpGetLimitKey(String ip, SmsCodeType smsCodeType) {
        return String.format(SMS_CODE_IP_GET_LIMIT_KEY, ip, smsCodeType.getType());
    }

    public static String getSmsCodeKey(String phone, SmsCodeType smsCodeType) {
        return String.format(SMS_CODE_KEY, phone, smsCodeType.getType());
    }

    public static String getMiguNotifyProcessing(Long orderId) {
        return MIGU_NOTIFY_PROCESSING + orderId;
    }

    public static String getQuanyiOrder(Long id) {
        return QUANYI_ORDER + ":" + id;
    }

    public static String getEccAreaKey(Integer operator, String postProvinceCode) {
        return ECC_PROVINCE_CODE + ":" + operator + ":" + postProvinceCode;
    }

    public static String getEccChannelRequestLimitKey(Long outerChannelId, String reqMd5) {
        return ECC_CHANNEL_REQUEST_LIMIT + ":" + outerChannelId + ":" + reqMd5;
    }

    public static String getJiangSuUnicomProductKey(String phone) {
        return String.format(JIANG_SU_UNICOM_PRODUCT_KEY, phone);
    }

    public static String getNmgYdRandomCodeKey(String phone) {
        return String.format(NMG_YD_RANDOM_CODE, phone);
    }

    public static String getSyUnicomToken(String clientId) {
        return SY_UNICOM_TOKEN + clientId;
    }

    public static String getNmCmccToken(String phone) {
        return NMG_YD_TOKEN + phone;
    }

    public static String getMgOrderItemObtainedOrder(String orderNo) {
        return String.format(MG_ORDER_ITEM_OBTAINED_ORDER, orderNo);
    }

    public static String getJiangXiUnicomTokenKey(String clientId) {
        return String.format(JIANG_XI_UNICOM_TOKEN_KEY, clientId);
    }

    public static String getHbCmccToken(String clientId) {
        return String.format(HB_CMCC_TOKEN, clientId);
    }

    public static String getHbCmccUserInfo(String phone) {
        return String.format(HB_CMCC_USER_KEY, phone);
    }

    public static String getHainanOrderNo(String phone) {
        return String.format(HAINAN_ORDER_NO, phone);
    }

    public static String getHainanCache(String key) {
        return String.format(HAINAN_CACHE, key);
    }

    public static String getMiguFunCache(String phone) {
        return String.format(MIGU_FUN_CACHE, phone);
    }

    public static String getJiangXiUnicomFlow(String phone) {
        return String.format(JIANG_XI_UNICOM_FLOW, phone);
    }

    public static String getBenefitNumber(BenefitPayChannelEnum channelEnum, String phone) {
        return String.format(BENEFIT_NUMBER, phone, channelEnum.getCode());
    }

    public static String ip2Region(String ip) {
        return String.format(IP_LOCATION, ip);
    }

    public static String getThreeFactorAuthResult(String name, String idCard, String phone) {
        String key = SecureUtil.md5(name + idCard + phone);
        return String.format(THREE_FACTOR_AUTH_RESULT, key);
    }

}
