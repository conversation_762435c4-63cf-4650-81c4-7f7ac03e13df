package com.yuelan.hermes.quanyi.common.proxy;

import cn.hutool.http.HttpRequest;
import com.yuelan.hermes.quanyi.remote.kuaiProxy.KuaiProxyManager;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 快代理HTTP请求设置器实现
 *
 * <AUTHOR> 2025/8/13
 * @since 2025/8/13
 */
@Slf4j
@RequiredArgsConstructor
public class KuaiProxyHttpRequestSetter implements ProxyHttpRequestSetter {

    private final KuaiProxyManager kuaiProxyManager;
    /**
     * -- GETTER --
     * 获取代理IP
     *
     * @return 代理IP
     */
    @Getter
    private final String proxyIp;
    /**
     * -- GETTER --
     * 获取代理端口
     *
     * @return 代理端口
     */
    @Getter
    private final int proxyPort;

    /**
     * 创建快代理设置器
     *
     * @param kuaiProxyManager 快代理管理器
     * @param proxyIp          代理IP
     * @param proxyPort        代理端口
     * @return 快代理设置器
     */
    public static KuaiProxyHttpRequestSetter create(KuaiProxyManager kuaiProxyManager, String proxyIp, int proxyPort) {
        return new KuaiProxyHttpRequestSetter(kuaiProxyManager, proxyIp, proxyPort);
    }

    @Override
    public HttpRequest setProxy(HttpRequest httpRequest) {
        log.info("使用快代理设置代理: {}:{}", proxyIp, proxyPort);
        return kuaiProxyManager.setProxyForHttpRequest(proxyIp, proxyPort, httpRequest);
    }
}
