package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.*;
import com.yuelan.result.entity.PageRequest;
import com.yuelan.result.enums.YesOrNoEnum;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> 2025/6/19
 * @since 2025/6/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AutoMapper(target = EccImeiDO.class)
public class EccImeiReq extends PageRequest {
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long imeiId;

    /**
     * imei
     */
    @Schema(description = "imei")
    private String imei;

    /**
     * 导入的批次 id
     */
    @Schema(description = "导入的批次 id")
    private Long batchId;

    /**
     * 设备信息
     */
    @Schema(description = "设备信息")
    private String device;

    /**
     * 渠道类型:1-内部渠道,2-外部渠道
     */
    @Schema(description = "渠道类型:1-内部渠道,2-外部渠道")
    private Integer channelType;

    /**
     * 渠道id
     */
    @Schema(description = "渠道id")
    private Long channelId;

    /**
     * 产品Id
     */
    @Schema(description = "产品Id")
    private Long productId;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "iccId")
    private String iccId;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "运营商订单号")
    private String operatorOrderNo;

    @Schema(description = "绑定状态:0-未绑定 1-已绑定")
    private Integer boundStatus;

    public Wrapper<EccImeiDO> buildQueryWrapper(List<String> imeiList) {
        return new MPJLambdaWrapper<EccImeiDO>()
                .selectAll(EccImeiDO.class)
                .select(EccProductDO::getProdName)
                .selectAs(EccOuterChannelDO::getChannelName, EccImeiDO::getOuterChannelName)
                .selectAs(EccChannelDO::getChannelName, EccImeiDO::getInnerChannelName)
                .selectCollection(EccImeiBindDO.class, EccImeiDO::getImeiBindList)
                .leftJoin(EccProductDO.class, EccProductDO::getProdId, EccImeiDO::getProductId)
                .leftJoin(EccOuterChannelDO.class, EccOuterChannelDO::getOuterChannelId, EccImeiDO::getChannelId)
                .leftJoin(EccChannelDO.class, EccChannelDO::getChannelId, EccImeiDO::getChannelId)
                .leftJoin(EccImeiBindDO.class, EccImeiBindDO::getImei, EccImeiDO::getImei)
                .eq(getImeiId() != null, EccImeiDO::getImeiId, imeiId)
                .like(StringUtils.isNotBlank(getImei()), EccImeiDO::getImei, imei)
                .eq(getBatchId() != null, EccImeiDO::getBatchId, batchId)
                .like(StringUtils.isNotBlank(getDevice()), EccImeiDO::getDevice, device)
                .eq(getChannelId() != null, EccImeiDO::getChannelId, channelId)
                .eq(getChannelType() != null, EccImeiDO::getChannelType, channelType)
                .eq(getProductId() != null, EccImeiDO::getProductId, productId)
                .ge(getCreateTimeStart() != null, EccImeiDO::getCreateTime, createTimeStart)
                .le(getCreateTimeEnd() != null, EccImeiDO::getCreateTime, createTimeEnd)
                .in(CollUtil.isNotEmpty(imeiList), EccImeiDO::getImei, imeiList)
                .isNotNull(YesOrNoEnum.YES.getCode().equals(boundStatus), EccImeiBindDO::getBindId)
                .isNull(YesOrNoEnum.NO.getCode().equals(boundStatus), EccImeiBindDO::getBindId)
                .orderByDesc(EccImeiDO::getCreateTime);
    }
}
