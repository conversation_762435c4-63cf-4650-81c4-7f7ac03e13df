package com.yuelan.hermes.quanyi.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuelan.core.util.MapstructUtils;
import com.yuelan.hermes.commons.util.EasyExcelUtil;
import com.yuelan.hermes.commons.util.ImeiUtil;
import com.yuelan.hermes.quanyi.biz.service.*;
import com.yuelan.hermes.quanyi.common.enums.EccChannelTypeEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccImeiBindDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccImeiDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import com.yuelan.hermes.quanyi.common.pojo.excel.EccImeiImportExcel;
import com.yuelan.hermes.quanyi.common.util.MpPageUtil;
import com.yuelan.hermes.quanyi.controller.EccImeiResp;
import com.yuelan.hermes.quanyi.controller.request.EccImeiReq;
import com.yuelan.hermes.quanyi.mapper.EccImeiMapper;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2025/6/17
 * @since 2025/6/17
 */
@Service
@RequiredArgsConstructor
public class EccImeiServiceImpl extends ServiceImpl<EccImeiMapper, EccImeiDO> implements EccImeiService {

    private final EccOuterChannelDOService eccOuterChannelDOService;
    private final EccChannelDOService eccChannelDOService;
    private final EccProductDOService eccProductDOService;
    private final EccImeiBindService eccImeiBindService;

    @Override
    @Lock4j(name = "ecc_imei_import")
    @Transactional(rollbackFor = Exception.class)
    public void importEccImei(MultipartFile file, Integer channelType, Long channelId, Long productId, String deviceInfo) throws IOException {
        List<EccImeiImportExcel> excelRows = EasyExcelFactory.read(file.getInputStream())
                .head(EccImeiImportExcel.class)
                .sheet()
                .doReadSync();
        if (excelRows.isEmpty()) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "未找到需要导入的数据");
        }
        EccChannelTypeEnum eccChannelTypeEnum = EccChannelTypeEnum.of(channelType);
        boolean existsChannel = false;
        if (EccChannelTypeEnum.INNER == eccChannelTypeEnum) {
            existsChannel = eccChannelDOService.getById(channelId) != null;
        } else if (EccChannelTypeEnum.OUTER == eccChannelTypeEnum) {
            existsChannel = eccOuterChannelDOService.existsById(channelId);
        } else {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "无效渠道类型");
        }
        BizException.assertCheck(existsChannel, BaseErrorCodeEnum.PARAMS_ERROR, "无效渠道 Id");

        boolean existsProduct = eccProductDOService.exists(Wrappers.<EccProductDO>lambdaQuery()
                .eq(EccProductDO::getProdId, productId));
        BizException.assertCheck(existsProduct, BaseErrorCodeEnum.PARAMS_ERROR, "无效产品 Id");


        List<String> imeiList = new ArrayList<>();

        for (EccImeiImportExcel excelRow : excelRows) {
            String imei = excelRow.getImei();
            if (imei == null || StringUtils.isBlank(imei)) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "放弃导入，发现IMEI为空的行");
            }
            boolean valid = ImeiUtil.verifyImei(imei);
            if (!valid) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "放弃导入，发现IMEI不合法：" + imei);
            }
            if (imeiList.contains(imei)) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "放弃导入，发现重复IMEI：" + imei);
            }
            imeiList.add(imei);
        }

        // 查询已经存在数据库的 imei
        // 先按 500条一组
        int batchSize = 500;
        List<List<String>> splitList = CollUtil.split(imeiList, batchSize);
        for (List<String> itemList : splitList) {
            List<EccImeiDO> existingImeiList = baseMapper.selectBatchByImei(itemList);
            if (!existingImeiList.isEmpty()) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "放弃导入，已经存在数据库的 imei：" + existingImeiList.get(0).getImei());
            }
        }
        // 随机生成一个批次 id
        Long batchId = System.currentTimeMillis();
        List<EccImeiDO> batchInsertList = new ArrayList<>();
        for (String imei : imeiList) {
            EccImeiDO imeiDO = new EccImeiDO();
            imeiDO.setImei(imei);
            imeiDO.setBatchId(batchId);
            imeiDO.setChannelType(channelType);
            imeiDO.setChannelId(channelId);
            imeiDO.setProductId(productId);
            imeiDO.setDevice(deviceInfo);
            batchInsertList.add(imeiDO);
        }
        saveBatch(batchInsertList, 1000);
    }

    @Override
    public PageData<EccImeiResp> pageEccImei(EccImeiReq req) {
        IPage<EccImeiDO> page = MpPageUtil.convertPageRequest(req);
        List<String> imeiList = inImeiList(req);
        page = baseMapper.selectPage(page, req.buildQueryWrapper(imeiList));
        List<EccImeiResp> respList = new ArrayList<>();
        for (EccImeiDO imeiItem : page.getRecords()) {
            EccImeiResp eccImeiResp = MapstructUtils.convertNotNull(imeiItem, EccImeiResp.class);
            // 补充渠道名称
            if (EccChannelTypeEnum.INNER.getType().equals(imeiItem.getChannelType())) {
                eccImeiResp.setChannelName(imeiItem.getInnerChannelName());
            } else if (EccChannelTypeEnum.OUTER.getType().equals(imeiItem.getChannelType())) {
                eccImeiResp.setChannelName(imeiItem.getOuterChannelName());
            }
            respList.add(eccImeiResp);
        }
        return PageData.create(
                respList,
                page.getTotal(),
                req.getPage(),
                req.getSize()
        );
    }

    public List<String> inImeiList(EccImeiReq req) {
        if (req.getIccId() != null || req.getPhone() != null || req.getOperatorOrderNo() != null) {
            return eccImeiBindService.list(Wrappers.<EccImeiBindDO>lambdaQuery()
                            .eq(req.getIccId() != null, EccImeiBindDO::getIccId, req.getIccId())
                            .eq(StringUtils.isNotBlank(req.getPhone()), EccImeiBindDO::getPhone, req.getPhone())
                            .eq(StringUtils.isNotBlank(req.getOperatorOrderNo()), EccImeiBindDO::getOperatorOrderNo, req.getOperatorOrderNo())
                    ).stream()
                    .map(EccImeiBindDO::getImei)
                    .distinct()
                    .collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        EasyExcelUtil.download(response, "imei 导入模板", EccImeiImportExcel.class, Collections.emptyList());
    }

    @Override
    public EccImeiDO getByImei(String imei) {
        return baseMapper.selectOne(Wrappers.<EccImeiDO>lambdaQuery()
                .eq(EccImeiDO::getImei, imei));

    }

    @Override
    public void updateDeliveryTimeByImei(String imei, LocalDateTime deliveryTime) {
        new LambdaUpdateChainWrapper<>(baseMapper)
                .eq(EccImeiDO::getImei, imei)
                .set(EccImeiDO::getDeliveryTime, deliveryTime)
                .update();
    }


}
