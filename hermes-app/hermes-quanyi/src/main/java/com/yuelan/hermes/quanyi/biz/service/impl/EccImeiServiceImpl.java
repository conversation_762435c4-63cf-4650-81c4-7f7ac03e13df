package com.yuelan.hermes.quanyi.biz.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuelan.core.util.MapstructUtils;
import com.yuelan.hermes.commons.util.EasyExcelUtil;
import com.yuelan.hermes.commons.util.ImeiUtil;
import com.yuelan.hermes.quanyi.biz.service.*;
import com.yuelan.hermes.quanyi.common.enums.EccChannelTypeEnum;
import com.yuelan.hermes.quanyi.common.enums.SpProdEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccImeiBindDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccImeiDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import com.yuelan.hermes.quanyi.common.pojo.excel.EccImeiImportExcel;
import com.yuelan.hermes.quanyi.common.util.MpPageUtil;
import com.yuelan.hermes.quanyi.controller.request.EccImeiReq;
import com.yuelan.hermes.quanyi.controller.response.EccImeiResp;
import com.yuelan.hermes.quanyi.controller.response.ImportResp;
import com.yuelan.hermes.quanyi.mapper.EccImeiMapper;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2025/6/17
 * @since 2025/6/17
 */
@Service
@RequiredArgsConstructor
public class EccImeiServiceImpl extends ServiceImpl<EccImeiMapper, EccImeiDO> implements EccImeiService {

    private final EccOuterChannelDOService eccOuterChannelDOService;
    private final EccChannelDOService eccChannelDOService;
    private final EccProductDOService eccProductDOService;
    private final EccImeiBindService eccImeiBindService;

    @Override
    @Lock4j(name = "ecc_imei_import")
    public ImportResp importEccImei(MultipartFile file, Integer channelType, Long channelId, Long productId, String deviceInfo) throws IOException {
        List<EccImeiImportExcel> excelRows = EasyExcelFactory.read(file.getInputStream())
                .head(EccImeiImportExcel.class)
                .sheet()
                .doReadSync();
        if (excelRows.isEmpty()) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "未找到需要导入的数据");
        }
        EccChannelTypeEnum eccChannelTypeEnum = EccChannelTypeEnum.of(channelType);
        boolean existsChannel = false;
        if (EccChannelTypeEnum.INNER == eccChannelTypeEnum) {
            existsChannel = eccChannelDOService.getById(channelId) != null;
        } else if (EccChannelTypeEnum.OUTER == eccChannelTypeEnum) {
            existsChannel = eccOuterChannelDOService.existsById(channelId);
        } else {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "无效渠道类型");
        }
        BizException.assertCheck(existsChannel, BaseErrorCodeEnum.PARAMS_ERROR, "无效渠道 Id");
        // 产品校验
        prodCheck(productId);

        LinkedHashSet<String> imeiList = new LinkedHashSet<>();
        for (EccImeiImportExcel excelRow : excelRows) {
            String imei = excelRow.getImei();
            if (imei == null || StringUtils.isBlank(imei)) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "放弃导入，发现IMEI为空的行");
            }
            boolean valid = ImeiUtil.verifyImei(imei);
            if (!valid) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "放弃导入，发现IMEI不合法：" + imei);
            }
            imeiList.add(imei);
        }
        // 随机生成一个批次 id
        Long batchId = System.currentTimeMillis();
        LinkedHashSet<EccImeiDO> batchInsertList = new LinkedHashSet<>();
        for (String imei : imeiList) {
            EccImeiDO imeiDO = new EccImeiDO();
            imeiDO.setImei(imei);
            imeiDO.setBatchId(batchId);
            imeiDO.setChannelType(channelType);
            imeiDO.setChannelId(channelId);
            imeiDO.setProductId(productId);
            imeiDO.setDevice(deviceInfo);
            batchInsertList.add(imeiDO);
        }
        ImportResp resp = new ImportResp();
        resp.setBatchId(batchId);

        saveBatchIgnore(batchInsertList, 1000);

        Long count = baseMapper.countByBatchId(batchId);
        resp.setSuccessCount(count.intValue());
        resp.setIgnoreCount(excelRows.size() - count.intValue());
        return resp;
    }

    private void prodCheck(Long prodId) {
        EccProductDO productDO = eccProductDOService.getById(prodId);
        BizException.assertCheck(productDO != null, BaseErrorCodeEnum.PARAMS_ERROR, "无效产品 Id");
        SpProdEnum hnDxSxc = SpProdEnum.HN_DX_SXC;
        BizException.assertCheck(hnDxSxc.getProdId().equals(productDO.getSpProdId()), BaseErrorCodeEnum.PARAMS_ERROR, "仅支持导入湖南南电信随销卡");
    }

    private void saveBatchIgnore(LinkedHashSet<EccImeiDO> dataSet, int i) {
        List<EccImeiDO> saveList = new ArrayList<>();
        int index = 0;
        for (EccImeiDO imeiDO : dataSet) {
            index++;
            if (index % i == 0) {
                baseMapper.saveBatchIgnore(saveList);
                saveList.clear();
            } else {
                saveList.add(imeiDO);
            }
        }
        if (!saveList.isEmpty()) {
            baseMapper.saveBatchIgnore(saveList);
        }
    }

    @Override
    public PageData<EccImeiResp> pageEccImei(EccImeiReq req) {
        IPage<EccImeiDO> page = MpPageUtil.convertPageRequest(req);
        List<String> imeiList = inImeiList(req);
        page = baseMapper.selectPage(page, req.buildQueryWrapper(imeiList));
        List<EccImeiResp> respList = new ArrayList<>();
        for (EccImeiDO imeiItem : page.getRecords()) {
            EccImeiResp eccImeiResp = MapstructUtils.convertNotNull(imeiItem, EccImeiResp.class);
            // 补充渠道名称
            if (EccChannelTypeEnum.INNER.getType().equals(imeiItem.getChannelType())) {
                eccImeiResp.setChannelName(imeiItem.getInnerChannelName());
            } else if (EccChannelTypeEnum.OUTER.getType().equals(imeiItem.getChannelType())) {
                eccImeiResp.setChannelName(imeiItem.getOuterChannelName());
            }
            respList.add(eccImeiResp);
        }
        return PageData.create(
                respList,
                page.getTotal(),
                req.getPage(),
                req.getSize()
        );
    }

    public List<String> inImeiList(EccImeiReq req) {
        if (req.getIccId() != null || req.getPhone() != null || req.getOperatorOrderNo() != null) {
            return eccImeiBindService.list(Wrappers.<EccImeiBindDO>lambdaQuery()
                            .eq(req.getIccId() != null, EccImeiBindDO::getIccId, req.getIccId())
                            .eq(StringUtils.isNotBlank(req.getPhone()), EccImeiBindDO::getPhone, req.getPhone())
                            .eq(StringUtils.isNotBlank(req.getOperatorOrderNo()), EccImeiBindDO::getOperatorOrderNo, req.getOperatorOrderNo())
                    ).stream()
                    .map(EccImeiBindDO::getImei)
                    .distinct()
                    .collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        EasyExcelUtil.download(response, "imei 导入模板", EccImeiImportExcel.class, Collections.emptyList());
    }

    @Override
    public EccImeiDO getByImei(String imei) {
        return baseMapper.selectOne(Wrappers.<EccImeiDO>lambdaQuery()
                .eq(EccImeiDO::getImei, imei));

    }

    @Override
    public void updateDeliveryTimeByImei(String imei, LocalDateTime deliveryTime) {
        new LambdaUpdateChainWrapper<>(baseMapper)
                .eq(EccImeiDO::getImei, imei)
                .set(EccImeiDO::getDeliveryTime, deliveryTime)
                .update();
    }


}
