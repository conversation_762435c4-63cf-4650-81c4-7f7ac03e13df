package com.yuelan.hermes.quanyi.common.listener;

import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.commons.enums.BizNoPrefixEnum;
import com.yuelan.hermes.quanyi.biz.manager.BenefitDispatchManager;
import com.yuelan.hermes.quanyi.biz.service.BenefitItemOrderService;
import com.yuelan.hermes.quanyi.common.enums.BenefitItemOrderProcessStateEnum;
import com.yuelan.hermes.quanyi.common.enums.BenefitItemOrderStatus;
import com.yuelan.hermes.quanyi.common.enums.RytOrderNotifyEnum;
import com.yuelan.hermes.quanyi.common.enums.TzOrderStateTypeEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.event.BenefitItemOrderStatusChangeEvent;
import com.yuelan.hermes.quanyi.common.event.RtyOrderNotifyEvent;
import com.yuelan.hermes.quanyi.common.event.TeZhenOrderNotifyEvent;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemOrderDO;
import com.yuelan.hermes.quanyi.controller.request.RytOrderNotifyReq;
import com.yuelan.hermes.quanyi.controller.request.TeZhenNotifyReq;
import com.yuelan.hermes.quanyi.remote.response.RytRsp;
import com.yuelan.result.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> 2024/4/12 下午4:43
 * 权益融合版本
 */
@Slf4j
@Component
@AllArgsConstructor
public class NewBenefitOrderNotifyListener {

    private final BenefitItemOrderService benefitItemOrderService;
    private final BenefitDispatchManager benefitDispatchManager;

    @EventListener(RtyOrderNotifyEvent.class)
    public void defaultUserBenefitPayListener(RtyOrderNotifyEvent event) {
        if (event.getIsHandled()) {
            return;
        }
        RytOrderNotifyReq req = event.getReq();
        String itemNo = req.getOrderId();
        if (!isNewBenefitOrder(itemNo)) {
            return;
        }
        RytOrderNotifyEnum orderNotifyEnum = RytOrderNotifyEnum.of(req.getCode());
        log.info("权益融合版本，权益订单处理中{}", itemNo);
        try {
            BenefitItemOrderDO orderDO = benefitItemOrderService.getBySupplierRequestOrder(itemNo);
            if (Objects.isNull(orderDO)) {
                throw BizException.create(BizErrorCodeEnum.ORDER_NOT_EXIST, "订单不存在");
            }
            if (!Objects.equals(BenefitItemOrderStatus.DISPATCHING.getCode(), orderDO.getOrderStatus())) {
                log.error("权益订单已被处理,忽略,订单号{}", itemNo);
                setFail(event);
                return;
            }
            BenefitItemOrderStatus newOrderStatus;
            BenefitItemOrderProcessStateEnum newProcessState;
            if (RytOrderNotifyEnum.ING.equals(orderNotifyEnum)) {
                log.error("充值中通知不做处理,已忽略,订单号{}", itemNo);
                setSuccess(event);
                return;
            } else if (RytOrderNotifyEnum.SUCCESS.equals(orderNotifyEnum)) {
                newOrderStatus = BenefitItemOrderStatus.SUCCESS;
                newProcessState = BenefitItemOrderProcessStateEnum.DISPATCH_SUCCESS;
            } else if (RytOrderNotifyEnum.FAIL.equals(orderNotifyEnum)) {
                newOrderStatus = BenefitItemOrderStatus.FAIL;
                newProcessState = BenefitItemOrderProcessStateEnum.DISPATCH_FAIL;
            } else {
                log.error("权益订单状态不能识别,订单号{}", itemNo);
                setFail(event);
                return;
            }
            BenefitItemOrderProcessStateEnum oldProcessStateEnum = BenefitItemOrderProcessStateEnum.getByCode(orderDO.getProcessState());
            if (oldProcessStateEnum != null && oldProcessStateEnum.equals(newProcessState)) {
                log.error("已经处理过的订单,忽略,订单号{}", itemNo);
                setSuccess(event);
                return;
            }
            updateItemOrder(JSONObject.toJSONString(req), orderDO.getItemOrderId(), newOrderStatus, newProcessState);
            setSuccess(event);
            publishStatusChangeEvent(orderDO, newOrderStatus.getCode());
        } catch (Exception e) {
            setFail(event);
            log.error("权益订单处理失败,订单号{}", itemNo, e);
        } finally {
            event.setIsHandled(Boolean.TRUE);
        }

    }

    @EventListener(TeZhenOrderNotifyEvent.class)
    public void defaultUserBenefitPayListener(TeZhenOrderNotifyEvent event) {
        if (event.getIsHandled()) {
            return;
        }
        TeZhenNotifyReq req = event.getReq();
        String itemNo = req.getLinkId();
        if (!isNewBenefitOrder(itemNo)) {
            return;
        }
        TzOrderStateTypeEnum orderNotifyEnum = TzOrderStateTypeEnum.of(req.getCode());
        log.info("权益融合版本，权益订单处理中{}", itemNo);
        try {
            BenefitItemOrderDO orderDO = benefitItemOrderService.getBySupplierRequestOrder(itemNo);
            if (Objects.isNull(orderDO)) {
                throw BizException.create(BizErrorCodeEnum.ORDER_NOT_EXIST, "订单不存在");
            }
            if (!Objects.equals(BenefitItemOrderStatus.DISPATCHING.getCode(), orderDO.getOrderStatus())) {
                log.error("权益订单已被处理,忽略,订单号{}", itemNo);
                setFail(event);
                return;
            }
            BenefitItemOrderStatus newOrderStatus;
            BenefitItemOrderProcessStateEnum newProcessState;
            if (TzOrderStateTypeEnum.ING.equals(orderNotifyEnum)) {
                log.error("充值中通知不做处理,已忽略,订单号{}", itemNo);
                setSuccess(event);
                return;
            } else if (TzOrderStateTypeEnum.SUCCESS.equals(orderNotifyEnum)) {
                newOrderStatus = BenefitItemOrderStatus.SUCCESS;
                newProcessState = BenefitItemOrderProcessStateEnum.DISPATCH_SUCCESS;
            } else if (TzOrderStateTypeEnum.FAIL.equals(orderNotifyEnum)) {
                newOrderStatus = BenefitItemOrderStatus.FAIL;
                newProcessState = BenefitItemOrderProcessStateEnum.DISPATCH_FAIL;
            } else {
                log.error("权益订单状态不能识别,订单号{}", itemNo);
                setFail(event);
                return;
            }
            BenefitItemOrderProcessStateEnum oldProcessStateEnum = BenefitItemOrderProcessStateEnum.getByCode(orderDO.getProcessState());
            if (oldProcessStateEnum != null && oldProcessStateEnum.equals(newProcessState)) {
                log.error("已经处理过的订单,忽略,订单号{}", itemNo);
                setSuccess(event);
                return;
            }
            updateItemOrder(JSONObject.toJSONString(req), orderDO.getItemOrderId(), newOrderStatus, newProcessState);
            setSuccess(event);
            publishStatusChangeEvent(orderDO, newOrderStatus.getCode());
        } catch (Exception e) {
            setFail(event);
            log.error("权益订单处理失败,订单号{}", itemNo, e);
        } finally {
            event.setIsHandled(Boolean.TRUE);
        }

    }

    private void updateItemOrder(String reqBody, Long itemOrderId, BenefitItemOrderStatus orderStatus, BenefitItemOrderProcessStateEnum processState) {
        BenefitItemOrderDO itemUpdate = new BenefitItemOrderDO();
        itemUpdate.setItemOrderId(itemOrderId);
        itemUpdate.setProcessState(processState.getCode());
        itemUpdate.setOrderStatus(orderStatus.getCode());
        itemUpdate.setCallbackReq(reqBody);
        itemUpdate.setCallbackTime(LocalDateTime.now());
        benefitItemOrderService.updateById(itemUpdate);
    }

    private void publishStatusChangeEvent(BenefitItemOrderDO orderDO, int newOrderStatus) {
        BenefitItemOrderStatusChangeEvent event = new BenefitItemOrderStatusChangeEvent();
        event.setItemOrderId(orderDO.getItemOrderId());
        event.setExtensionId(orderDO.getExtensionId());
        event.setDispatchTiming(orderDO.getDispatchTiming());
        event.setOldOrderStatus(orderDO.getOrderStatus());
        event.setNewOrderStatus(newOrderStatus);
        benefitDispatchManager.itemOrderStatusChangeEvent(event);
    }


    /**
     * 是否是权益订单
     *
     * @param itemNo 子订单订单号
     * @return 是否是权益订单
     */
    private boolean isNewBenefitOrder(String itemNo) {
        return Objects.nonNull(itemNo)
                && itemNo.startsWith(BizNoPrefixEnum.BENEFIT_ITEM_ORDER.getPrefix());
    }

    private void setSuccess(RtyOrderNotifyEvent event) {
        event.setResp(RytRsp.success());
    }

    private void setFail(RtyOrderNotifyEvent event) {
        event.setResp(RytRsp.error(500, "充值回调处理失败"));
    }

    private void setSuccess(TeZhenOrderNotifyEvent event) {
        event.setSuccess(Boolean.TRUE);
    }

    private void setFail(TeZhenOrderNotifyEvent event) {
        event.setSuccess(Boolean.FALSE);
    }


}
