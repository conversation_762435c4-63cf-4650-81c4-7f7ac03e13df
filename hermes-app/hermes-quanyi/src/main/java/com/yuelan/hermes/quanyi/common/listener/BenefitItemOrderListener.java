package com.yuelan.hermes.quanyi.common.listener;

import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.biz.service.BenefitsPackageOrderService;
import com.yuelan.hermes.quanyi.common.enums.BenefitDispatchTimingEnum;
import com.yuelan.hermes.quanyi.common.enums.BenefitItemOrderStatus;
import com.yuelan.hermes.quanyi.common.event.BenefitItemOrderStatusChangeEvent;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageOrderDO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2025/4/30
 * @since 2025/4/30
 */
@Slf4j
@Component
@AllArgsConstructor
public class BenefitItemOrderListener {

    private final BenefitsPackageOrderService benefitsPackageOrderService;

    /**
     * 更新权益包订单的权益发放信息
     */
    @EventListener(BenefitItemOrderStatusChangeEvent.class)
    public void defaultListener(BenefitItemOrderStatusChangeEvent event) {
        log.info("权益订单状态变更事件：{}", JSONObject.toJSONString(event));
        // 检查是否是权益包订单
        BenefitsPackageOrderDO packageOrderDO = benefitsPackageOrderService.getByExtensionId(event.getExtensionId());
        if (packageOrderDO == null) {
            return;
        }
        BenefitItemOrderStatus oldOrderStatus = BenefitItemOrderStatus.getByCode(event.getOldOrderStatus());
        BenefitItemOrderStatus newOrderStatus = BenefitItemOrderStatus.getByCode(event.getNewOrderStatus());
        if (oldOrderStatus == newOrderStatus) {
            return;
        }
        BenefitDispatchTimingEnum dispatchTimingEnum = BenefitDispatchTimingEnum.getByCode(event.getDispatchTiming());
        int dispatchErrorCountIncrement = 0;
        int instantDispatchCountIncrement = 0;
        int optionalDispatchCountIncrement = 0;
        if (BenefitItemOrderStatus.FAIL.equals(oldOrderStatus)) {
            dispatchErrorCountIncrement = -1;
        }
        if (BenefitItemOrderStatus.SUCCESS.equals(newOrderStatus)) {
            if (BenefitDispatchTimingEnum.INSTANT.equals(dispatchTimingEnum)) {
                instantDispatchCountIncrement = 1;
            } else if (BenefitDispatchTimingEnum.USER_SELECT.equals(dispatchTimingEnum)) {
                optionalDispatchCountIncrement = 1;
            }
        }
        // 更新权益包订单状态
        benefitsPackageOrderService.updateDispatchInfo(packageOrderDO.getPackageOrderId(),
                dispatchErrorCountIncrement, instantDispatchCountIncrement, optionalDispatchCountIncrement);
    }

}
