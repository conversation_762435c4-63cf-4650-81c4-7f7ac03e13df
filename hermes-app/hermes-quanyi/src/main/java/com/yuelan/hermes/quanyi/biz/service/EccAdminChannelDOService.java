package com.yuelan.hermes.quanyi.biz.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccAdminChannelDO;
import com.yuelan.hermes.quanyi.config.satoken.StpAdminUtil;
import com.yuelan.hermes.quanyi.config.satoken.context.AdminContext;
import com.yuelan.hermes.quanyi.mapper.EccAdminChannelDOMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/6/7 上午10:57
 */
@Service
public class EccAdminChannelDOService extends ServiceImpl<EccAdminChannelDOMapper, EccAdminChannelDO> {
    @Lazy
    @Autowired
    private EccOuterChannelDOService eccOuterChannelDOService;

    @Lazy
    @Autowired
    private EccChannelDOService eccChannelDOService;

    /**
     * 查询该账号允许查询的渠道
     * @param adminId cms后台账号id
     * @return 返回null 表示不存在限制  其他为限制
     */
    @Deprecated
    public List<Long> listChannelIdsByAdminId(Long adminId) {
        EccAdminChannelDO adminChannelDo = baseMapper.getByAdminId(adminId);
        if (Objects.isNull(adminChannelDo)) {
            return null;
        } else {
            return adminChannelDo.getChannelIds();
        }
    }

    public EccAdminChannelDO getByAdminId(Long adminId) {
        return baseMapper.getByAdminId(adminId);
    }


    public List<Long> listAllOutChannelsLimit() {
        AdminContext adminContext = StpAdminUtil.getAdminContext();
        List<Long> limit = new ArrayList<>();
        if (adminContext.isSysAdmin()) {
            return null;
        }else if (adminContext.isEccChannel()) {
            if (adminContext.getOutChannelContext() != null) {
                Long outerChannelId = adminContext.getOutChannelContext().getOuterChannelId();
                limit.add(outerChannelId);
                limit.addAll(eccOuterChannelDOService.getAllDescendantChannelIds(outerChannelId));
            }

        }
        return limit;
    }

    public List<Long> listAllInnerChannelsLimit() {
        AdminContext adminContext = StpAdminUtil.getAdminContext();
        List<Long> limit = new ArrayList<>();
        if (adminContext.isSysAdmin()) {
            return null;
        }else if (adminContext.isEccChannel()) {
            if (adminContext.getInnerChannelContext()!=null) {
                Long innerChannelId = adminContext.getInnerChannelContext().getInnerChannelId();
                limit.add(innerChannelId);
                limit.addAll(eccChannelDOService.getAllDescendantChannelIds(innerChannelId));
            }
        }
        return limit;
    }

}
