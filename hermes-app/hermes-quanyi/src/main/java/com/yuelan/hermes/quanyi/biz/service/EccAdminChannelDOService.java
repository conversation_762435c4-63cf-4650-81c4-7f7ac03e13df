package com.yuelan.hermes.quanyi.biz.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccAdminChannelDO;
import com.yuelan.hermes.quanyi.mapper.EccAdminChannelDOMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
/**
 * <AUTHOR> 2024/6/7 上午10:57
 */
@Service
public class EccAdminChannelDOService extends ServiceImpl<EccAdminChannelDOMapper, EccAdminChannelDO> {
    /**
     * 查询该账号允许查询的渠道
     * @param adminId cms后台账号id
     * @return 返回null 表示不存在限制  其他为限制
     */
    public List<Long> listChannelIdsByAdminId(Long adminId) {
        EccAdminChannelDO adminChannelDo = baseMapper.getByAdminId(adminId);
        if (Objects.isNull(adminChannelDo)) {
            return null;
        } else {
            return adminChannelDo.getChannelIds();
        }
    }
}
