package com.yuelan.hermes.quanyi.biz.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuelan.hermes.commons.enums.DeliveryTypeEnum;
import com.yuelan.hermes.commons.enums.SupplierEnum;
import com.yuelan.hermes.quanyi.biz.handler.factory.BenefitSupplierFactory;
import com.yuelan.hermes.quanyi.biz.manager.param.ParameterDefinition;
import com.yuelan.hermes.quanyi.biz.service.BenefitItemService;
import com.yuelan.hermes.quanyi.common.constant.SupplierGoodsParameters;
import com.yuelan.hermes.quanyi.common.enums.BenefitGoodsModuleEnum;
import com.yuelan.hermes.quanyi.common.interfaces.BenefitSupplier;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitGoodsDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemDO;
import com.yuelan.hermes.quanyi.common.util.MpPageUtil;
import com.yuelan.hermes.quanyi.controller.request.BenefitItemListReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitItemResp;
import com.yuelan.hermes.quanyi.mapper.BenefitItemMapper;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.YesOrNoEnum;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2025/4/21
 * @since 2025/4/21
 */
@Service
@RequiredArgsConstructor
public class BenefitItemServiceImpl extends ServiceImpl<BenefitItemMapper, BenefitItemDO> implements BenefitItemService {

    private final BenefitSupplierFactory benefitSupplierFactory;

    @Override
    public PageData<BenefitItemResp> list(BenefitItemListReq req) {
        IPage<BenefitItemDO> page = MpPageUtil.convertPageRequest(req);
        page = baseMapper.selectPage(page, req.buildQueryWrapper());
        List<BenefitItemResp> respList = page.getRecords().stream()
                .map(BenefitItemResp::buildResp).collect(Collectors.toList());
        return PageData.create(respList, page.getTotal(), req.getPage(), req.getSize());
    }

    @Override
    public List<BenefitItemDO> listUpItemsByIds(List<Long> optionalIds) {
        if (optionalIds == null || optionalIds.isEmpty()) {
            return new ArrayList<>();
        }
        List<BenefitItemDO> items = baseMapper.selectBatchIds(optionalIds);
        return items.stream()
                .filter(item -> Objects.equals(item.getStatus(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toList());
    }

    @Override
    public void syncBenefitGoods(BenefitGoodsDO benefitGoodsDO) {
        Long goodsId = benefitGoodsDO.getGoodsId();
        BenefitGoodsModuleEnum sourceEnum = BenefitGoodsModuleEnum.OLD;
        BenefitItemDO itemDO = findByTargetId(goodsId, sourceEnum);

        BenefitItemDO saveOrUpdateItem = new BenefitItemDO();
        boolean save = false;
        if (Objects.nonNull(itemDO)) {
            saveOrUpdateItem.setBenefitItemId(itemDO.getBenefitItemId());
            saveOrUpdateItem.setBenefitCode(itemDO.getBenefitCode());
        } else {
            save = true;
            saveOrUpdateItem.setBenefitCode(generateItemCode());
        }
        saveOrUpdateItem.setModule(sourceEnum.getCode());
        saveOrUpdateItem.setTargetId(goodsId);
        saveOrUpdateItem.setBenefitName(benefitGoodsDO.getGoodsName());
        // 原来的库没有这个字段现在只能写死 可能是错的
        saveOrUpdateItem.setDeliveryType(DeliveryTypeEnum.DIRECT_RECHARGE.getCode());
        saveOrUpdateItem.setItemImg(benefitGoodsDO.getGoodsImg());
        BigDecimal costPrice = benefitGoodsDO.getCostPrice();
        if (Objects.nonNull(costPrice)) {
            // 小数元转换成分
            int costPriceInt = costPrice.multiply(BigDecimal.valueOf(100)).intValue();
            saveOrUpdateItem.setCostPrice(costPriceInt);
        } else {
            saveOrUpdateItem.setCostPrice(0);
        }
        BigDecimal sellingPrice = benefitGoodsDO.getPrice();
        if (Objects.nonNull(sellingPrice)) {
            // 小数元转换成分
            int sellingPriceInt = sellingPrice.multiply(BigDecimal.valueOf(100)).intValue();
            saveOrUpdateItem.setSellingPrice(sellingPriceInt);
        } else {
            saveOrUpdateItem.setSellingPrice(0);
        }
        saveOrUpdateItem.setSupplierId(benefitGoodsDO.getSupplierType());
        saveOrUpdateItem.setSupplierName(benefitGoodsDO.getSupplierName());
        saveOrUpdateItem.setStatus(benefitGoodsDO.getGoodsStatus());
        // 现在都是存的供应商商品编码
        JSONObject param = new JSONObject() {{
            put(SupplierGoodsParameters.GOODS_CODE.getKey(), benefitGoodsDO.getSupplierGoodsNo());
        }};
        if (Objects.nonNull(benefitGoodsDO.getParValue())) {
            param.put(SupplierGoodsParameters.PAR_VALUE.getKey(), benefitGoodsDO.getParValue());
        }
        supplierGoodsParametersCheck(benefitGoodsDO.getSupplierType(), param);
        saveOrUpdateItem.setSupplierGoodsParam(param.toJSONString());

        if (save) {
            this.save(saveOrUpdateItem);
        } else {
            this.updateById(saveOrUpdateItem);
        }

    }

    private void supplierGoodsParametersCheck(Integer supplierType, JSONObject param) {
        SupplierEnum supplierEnum = SupplierEnum.of(supplierType);
        BenefitSupplier supplier = benefitSupplierFactory.getSupplier(supplierEnum);
        if (Objects.nonNull(supplier)) {
            Set<ParameterDefinition> parameterDefinitions = supplier.goodsParams();
            for (ParameterDefinition parameterDefinition : parameterDefinitions) {
                String key = parameterDefinition.getKey();
                if (!param.containsKey(key)) {
                    throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "该供应商需要提供参数" + parameterDefinition.getDescription());
                } else if (key.equals(SupplierGoodsParameters.PAR_VALUE.getKey()) && param.getBigDecimal(key).doubleValue() <= 0) {
                    // 如果有面值需要传 默认需要大于 0
                    throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "该供应商需要提供面值大于0");
                }
            }
        }
    }

    private BenefitItemDO findByTargetId(Long targetId, BenefitGoodsModuleEnum moduleEnum) {
        return baseMapper.selectOne(Wrappers.<BenefitItemDO>lambdaQuery()
                .eq(BenefitItemDO::getTargetId, targetId)
                .eq(BenefitItemDO::getModule, moduleEnum.getCode()));
    }

    /**
     * 生成唯一编码
     * 随机生成 && 不存在数据库内
     * 8-10长度生成10次 连续10次出错 增长一位
     */
    private String generateItemCode() {
        final int MAX_CODE_LENGTH = 15;
        boolean dbExist;
        int count = 0;
        int generateCount = 10;
        int generateLength = 8;
        String code;
        do {
            count++;
            if (count % generateCount == 0) {
                generateLength++;
            }
            if (generateLength > MAX_CODE_LENGTH) {
                throw BizException.create(BaseErrorCodeEnum.SYSTEM_BUSY, "商品码生成失败");
            }
            code = RandomUtil.randomString(generateLength);

            LambdaQueryWrapper<BenefitItemDO> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(BenefitItemDO::getBenefitCode, code);
            dbExist = baseMapper.selectCount(wrapper) > 0L;
        } while (dbExist);
        return code;
    }


}

