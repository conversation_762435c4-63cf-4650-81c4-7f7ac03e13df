package com.yuelan.hermes.quanyi.common.enums;

import com.yuelan.hermes.quanyi.common.interfaces.PayChannelPkgInter;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 湖北数科-爱音乐包 & 教育包
 *
 * <AUTHOR> 2025/7/17
 * @since 2025/7/17
 */
@Getter
@AllArgsConstructor
public enum HuBeiShuKePayPkgEnum implements PayChannelPkgInter {

    /**
     * 爱音乐-视频彩铃经典铃音盒
     */
    RING_BOX_SERVICE(1, PkgType.MUSIC, "视频彩铃经典铃音盒-暂不可用"),

    /**
     * 教育包
     */
    EDU_BY_THUMB(2, PkgType.EDU, "大拇哥精品教育权益包"),

    ;

    /**
     * 渠道包ID
     */
    private final Integer pkgId;

    private final PkgType pkgType;

    /**
     * 渠道包名称
     */
    private final String name;


    public static HuBeiShuKePayPkgEnum of(Integer channelPkgId) {
        for (HuBeiShuKePayPkgEnum value : values()) {
            if (value.pkgId.equals(channelPkgId)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public Integer getChannelPkgId() {
        return pkgId;
    }

    @Override
    public String getChannelPkgName() {
        return name;
    }

    public static List<HuBeiShuKePayPkgEnum> getByPkgType(PkgType pkgType) {
        List<HuBeiShuKePayPkgEnum> result = new ArrayList<>();
        for (HuBeiShuKePayPkgEnum value : values()) {
            if (value.pkgType.equals(pkgType)) {
                result.add(value);
            }
        }
        return result;

    }

    @AllArgsConstructor
    @Getter
    public enum PkgType {
        MUSIC(1, "音乐"),
        EDU(2, "教育"),
        ;
        private final Integer code;
        private final String name;

    }
}
