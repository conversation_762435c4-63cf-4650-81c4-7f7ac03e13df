package com.yuelan.hermes.quanyi.config.satoken;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import com.yuelan.hermes.quanyi.common.enums.SysRoleEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;


/**
 * [Sa-Token 权限认证] 配置类
 *
 * <AUTHOR>
 */
@Configuration
public class SaTokenConfigure implements WebMvcConfigurer {

    // 注册 Sa-Token 拦截器，打开注解式鉴权功能
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，校验规则为 StpUtil.checkLogin() 登录校验。
        registry.addInterceptor(new SaInterceptor(handle -> StpAdminUtil.checkLogin()))
                .addPathPatterns("/a/**");
        // 电商卡外部渠道只能访问特定的url
        registry.addInterceptor(new EccAccessInterceptor())
                .addPathPatterns("/a/**");
        // n选1权益包用户鉴权
        registry.addInterceptor(new SaInterceptor(handle -> StpBenefitUserUtil.checkLogin()))
                .addPathPatterns("/b/**");
        // 电商卡需鉴权接口
        registry.addInterceptor(new SaInterceptor(handle -> StpEccUserUtil.checkLogin()))
                .addPathPatterns("/e/**");
    }

    @Autowired
    public void setSaTokenConfig() {
        StpUtil.stpLogic.setConfig(StpAdminUtil.config);
    }

    @Autowired
    public void setAdminStpLogic() {
        StpAdminUtil.setStpLogic(StpAdminUtil.stpLogic);
    }

    /**
     * 临时解决方案 系统没有权限管理
     * 电商卡外部渠道账号禁止操作指定url以外的接口
     */
    public static class EccAccessInterceptor implements HandlerInterceptor {
        static List<String> accessUrl = new ArrayList<>();

        static {
            accessUrl.add("/a/userinfo");
            accessUrl.add("/a/admin/detail");
            accessUrl.add("/a/admin/password/modify");
            // 电商卡产品详情
            accessUrl.add("/a/ecc/product/detail/**");
            // 电商卡产品列表
            accessUrl.add("/a/ecc/product/selectOptions");
            // 电商卡产品复制分销链接
            accessUrl.add("/a/ecc/product/copyDistributionUrl");
            // 电商卡内部渠道列表
            accessUrl.add("/a/ecc/channel/selectOptions");
            // 电商卡外部渠道所有接口
            accessUrl.add("/a/ecc/outerChannel/**");
            // 广告渠道
            accessUrl.add("/a/adChannel/selectOptions");
            // 联通 zop 订单
            accessUrl.add("/a/ecc/zopOrder/list");
            // 联通的 zop 状态
            accessUrl.add("/a/ecc/zopOrder/unicomStatusOptions");
            // 通用的号卡订单 列表导出等
            accessUrl.add("/a/ecc/ncOrder/**");
        }

        @Override
        public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
            // 获取请求的路径
            String path = request.getRequestURI();
            if (StpAdminUtil.hasRole(SysRoleEnum.ADMIN.getName())) {
                return true;
            }
            if (StpAdminUtil.hasRole(SysRoleEnum.ECC_CHANNEL.getName())
                    || StpAdminUtil.hasRole(SysRoleEnum.ECC_OUTER_END_LEVEL.getName())
            ) {
                for (String s : accessUrl) {
                    if (isPathMatched(path, s)) {
                        return true;
                    }
                }
                throw BizException.create(BizErrorCodeEnum.ADMIN_PERMISSION_DENIED);
            }
            return false;
        }
        private boolean isPathMatched(String path, String pattern) {
            if (pattern.endsWith("/**")) {
                // 处理通配符模式，如 "/a/ecc/outerChannel/**"
                String prefix = pattern.substring(0, pattern.length() - 3); // 去掉 "/**"
                return path.startsWith(prefix);
            } else {
                // 精确匹配
                return path.equals(pattern);
            }
        }
    }
}
