package com.yuelan.hermes.quanyi.config.satoken;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import com.yuelan.hermes.quanyi.common.enums.SysRoleEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;


/**
 * [Sa-Token 权限认证] 配置类
 *
 * <AUTHOR>
 */
@Configuration
public class SaTokenConfigure implements WebMvcConfigurer {

    // 注册 Sa-Token 拦截器，打开注解式鉴权功能
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，校验规则为 StpUtil.checkLogin() 登录校验。
        registry.addInterceptor(new SaInterceptor(handle -> StpAdminUtil.checkLogin()))
                .addPathPatterns("/a/**");
        // 电商卡外部渠道只能访问特定的url
        registry.addInterceptor(new EccAccessInterceptor())
                .addPathPatterns("/a/**");
        // n选1权益包用户鉴权
        registry.addInterceptor(new SaInterceptor(handle -> StpBenefitUserUtil.checkLogin()))
                .addPathPatterns("/b/**");
        // 电商卡需鉴权接口
        registry.addInterceptor(new SaInterceptor(handle -> StpEccUserUtil.checkLogin()))
                .addPathPatterns("/e/**");
    }

    @Autowired
    public void setSaTokenConfig() {
        StpUtil.stpLogic.setConfig(StpAdminUtil.config);
    }

    @Autowired
    public void setAdminStpLogic() {
        StpAdminUtil.setStpLogic(StpAdminUtil.stpLogic);
    }

    /**
     * 临时解决方案 系统没有权限管理
     * 电商卡外部渠道账号禁止操作指定url以外的接口
     */
    public static class EccAccessInterceptor implements HandlerInterceptor {
        static List<String> accessUrl = new ArrayList<>();

        static {
            accessUrl.add("/a/userinfo");
            accessUrl.add("/a/admin/detail");
            accessUrl.add("/a/admin/password/modify");
            accessUrl.add("/a/ecc/product/detail/");
            accessUrl.add("/a/ecc/product/selectOptions");
            accessUrl.add("/a/ecc/product/copyDistributionUrl");
            accessUrl.add("/a/ecc/channel/selectOptions");
            accessUrl.add("/a/ecc/zopOrder/list");
            accessUrl.add("/a/adChannel/selectOptions");
            accessUrl.add("/a/ecc/zopOrder/unicomStatusOptions");
        }

        @Override
        public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
            // 获取请求的路径
            String path = request.getRequestURI();
            if (StpAdminUtil.hasRole(SysRoleEnum.ECC_OUTER.getName())) {
                for (String s : accessUrl) {
                    if (path.startsWith(s)) {
                        return true;
                    }
                }
                throw BizException.create(BizErrorCodeEnum.ADMIN_PERMISSION_DENIED);
            }
            // 如果角色验证通过，则继续执行后续的处理
            return true;
        }
    }
}
