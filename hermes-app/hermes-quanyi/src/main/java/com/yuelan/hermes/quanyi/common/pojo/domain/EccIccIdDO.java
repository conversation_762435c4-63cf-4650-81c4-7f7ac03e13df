package com.yuelan.hermes.quanyi.common.pojo.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * ICCID实体类
 *
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Schema(description = "ICCID卡号库")
@Data
@TableName(value = "ecc_iccid")
public class EccIccIdDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键")
    private Long id;

    /**
     * ICCID号码
     */
    @TableField(value = "iccid")
    @Schema(description = "ICCID号码")
    private String iccId;

    /**
     * 产品代码
     */
    @TableField(value = "product_id")
    @Schema(description = "产品id")
    private Long productId;


    @TableField(value = "channel_type")
    @Schema(description = "渠道类型:1-内部渠道,2-外部渠道")
    private Integer channelType;

    /**
     * 渠道代码
     */
    @TableField(value = "channel_id")
    @Schema(description = "渠道id")
    private Long channelId;

    /**
     * 批次号
     */
    @TableField(value = "batch_id")
    @Schema(description = "批次号")
    private Long batchId;

    /**
     * 有效期
     */
    @TableField(value = "validity_period")
    @Schema(description = "有效期")
    private LocalDate validityPeriod;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;


    @TableField(exist = false)
    @Schema(description = "产品名称")
    private String prodName;

    @TableField(exist = false)
    @Schema(description = "内部渠道名称")
    private String innerChannelName;

    @TableField(exist = false)
    @Schema(description = "外部渠道名称")
    private String outerChannelName;

}
