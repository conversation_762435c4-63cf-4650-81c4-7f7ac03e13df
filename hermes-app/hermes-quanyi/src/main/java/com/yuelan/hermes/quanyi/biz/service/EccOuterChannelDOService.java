package com.yuelan.hermes.quanyi.biz.service;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccNcOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderDO;
import com.yuelan.hermes.quanyi.common.util.MpPageUtil;
import com.yuelan.hermes.quanyi.config.satoken.StpAdminUtil;
import com.yuelan.hermes.quanyi.config.satoken.context.AdminContext;
import com.yuelan.hermes.quanyi.controller.request.EccOuterChannelListReq;
import com.yuelan.hermes.quanyi.controller.request.EccOuterChannelSaveReq;
import com.yuelan.hermes.quanyi.controller.response.EccOuterChannelResp;
import com.yuelan.hermes.quanyi.controller.response.EccQueryOrderApiV2Resp;
import com.yuelan.hermes.quanyi.mapper.EccOuterChannelDOMapper;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.yuelan.hermes.quanyi.common.constant.CommonConstants.ECC_OUT_CHANNEL_MAX_LEVEL;

/**
 * <AUTHOR> 2024/5/17 下午4:02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EccOuterChannelDOService extends ServiceImpl<EccOuterChannelDOMapper, EccOuterChannelDO> {

    private final EccOuterChannelDOMapper eccOuterChannelDOMapper;
    private final EccAdminChannelDOService adminChannelDOService;

    @Lazy
    @Autowired
    private DeductionCalculationService deductionCalculationService;

    public boolean existsById(Long outerChannelId) {
        return getById(outerChannelId) != null;
    }

    /**
     * 获取 Mapper 实例，供其他服务调用递归CTE方法
     */
    public EccOuterChannelDOMapper getMapper() {
        return this.baseMapper;
    }

    /**
     * 分页列表
     */
    public PageData<EccOuterChannelResp> pageList(EccOuterChannelListReq req) {
        List<Long> outChannelsLimit = adminChannelDOService.listAllOutChannelsLimit();
        IPage<EccOuterChannelDO> page = MpPageUtil.convertPageRequest(req);
        page = eccOuterChannelDOMapper.selectPage(page, req.buildQueryWrapper(outChannelsLimit));
        List<EccOuterChannelResp> respList = page.getRecords().stream()
                .map(EccOuterChannelResp::buildResp).collect(Collectors.toList());
        return PageData.create(respList, page.getTotal(), req.getPage(), req.getSize());
    }

    /**
     * 新增外部渠道
     */
    public void save(EccOuterChannelSaveReq req) {
        EccOuterChannelDO channelDO = req.convert();
        // 权限检测
        checkAddPermission(channelDO);
        // 初始化新增渠道的基础字段
        initializeNewChannel(channelDO);
        // 执行业务验证
        validateChannelForSave(channelDO);
        // 执行保存前检查
        saveCheck(channelDO);
        // 保存到数据库
        save(channelDO);
        log.info("新增外部渠道成功，渠道名称: {}, 上级渠道ID: {}, 渠道等级: {}, 扣量比例: {}%",
                channelDO.getChannelName(), channelDO.getParentChannelId(), channelDO.getChannelLevel(), channelDO.getDeductionRate());
    }

    /**
     * 新增权限
     * 1. 系统管理员可以新增任意渠道
     * 2. 渠道管理员只能新增上级渠道是自己或者自己下级的渠道
     */
    private void checkAddPermission(EccOuterChannelDO channelDO) {
        Long parentChannelId = channelDO.getParentChannelId();
        List<Long> outChannelsLimit = adminChannelDOService.listAllOutChannelsLimit();
        if (Objects.isNull(outChannelsLimit)) {
            // 无限制
            return;
        }
        if (outChannelsLimit.isEmpty()) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "没有权限");
        }
        if (!outChannelsLimit.contains(parentChannelId)) {
            // 只能新增到自己有权限的渠道下
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "没有权限:上级渠道不在权限范围内");
        }
    }

    /**
     * 更新
     */
    public void update(EccOuterChannelSaveReq req) {
        EccOuterChannelDO channelDO = req.convert();
        // 权限检测
        checkUpdatePermission(channelDO);
        // 执行业务验证
        validateChannelForUpdate(channelDO);
        // 执行更新前检查
        updateCheck(channelDO);
        // 更新到数据库
        updateById(channelDO);

        log.info("更新外部渠道成功，渠道ID: {}, 渠道名称: {}, 上级渠道ID: {}, 扣量比例: {}%",
                channelDO.getOuterChannelId(), channelDO.getChannelName(), channelDO.getParentChannelId(), channelDO.getDeductionRate());
    }

    /**
     * 更新权限
     * 1. 系统管理员可以更新任意渠道
     * 2. 渠道管理员只能更新自己或者自己下级的渠道
     * 3. 不可以操作修改自己
     * 4. 上级渠道智能修改到自己或者自己下级的渠道
     */
    private void checkUpdatePermission(EccOuterChannelDO channelDO) {
        Long parentChannelId = channelDO.getParentChannelId();
        Long outerChannelId = channelDO.getOuterChannelId();
        List<Long> outChannelsLimit = adminChannelDOService.listAllOutChannelsLimit();
        if (Objects.isNull(outChannelsLimit)) {
            // 无限制
            return;
        }
        AdminContext adminContext = StpAdminUtil.getAdminContext();
        Long myOutChannelId = adminContext.getOutChannelContext().getOuterChannelId();
        if (outChannelsLimit.isEmpty()) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "没有权限");
        }
        if (!outChannelsLimit.contains(parentChannelId)) {
            // 只能新增到自己有权限的渠道下
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "没有权限:上级渠道不在权限范围内");
        }
        if (!outChannelsLimit.contains(outerChannelId)) {
            // 只能新增到自己有权限的渠道下
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "没有权限:该渠道不在权限范围内");
        }
        if (Objects.equals(outerChannelId, myOutChannelId)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "没有权限:不可以操作修改自己");
        }
    }

    /**
     * 通过apiKey查询外部渠道
     */
    public EccOuterChannelDO getByApiKey(String apiKey) {
        return eccOuterChannelDOMapper.selectOne(Wrappers.lambdaQuery(EccOuterChannelDO.class).eq(EccOuterChannelDO::getApiKey, apiKey));
    }

    /**
     * 保存前校验
     *
     * @param saveDO 保存对象
     */
    private void saveCheck(EccOuterChannelDO saveDO) {
        nameRepeatCheck(saveDO);
    }

    /**
     * 更新前校验
     *
     * @param updateDO 更新对象
     */
    private void updateCheck(EccOuterChannelDO updateDO) {
        EccOuterChannelDO dbChannelDO = getById(updateDO.getOuterChannelId());
        if (Objects.isNull(dbChannelDO)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "渠道不存在");
        }
        if (!Objects.equals(dbChannelDO.getChannelName(), updateDO.getChannelName())) {
            nameRepeatCheck(updateDO);
        }
    }

    /**
     * 重名检查
     */
    private void nameRepeatCheck(EccOuterChannelDO channelDO) {
        EccOuterChannelDO dbChannelDO = eccOuterChannelDOMapper.selectByName(channelDO.getChannelName());
        if (Objects.nonNull(dbChannelDO)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "渠道名字重复");
        }
    }

    /**
     * 前端展示渠道选项
     */
    public List<EccOuterChannelResp> selectOptionsByAdmin() {
        List<Long> channelIds = adminChannelDOService.listAllOutChannelsLimit();
        List<EccOuterChannelDO> dbChannelDOS = this.list();
        List<EccOuterChannelResp> list = new ArrayList<>();
        if (Objects.isNull(channelIds)) {
            // 没有限制
            for (EccOuterChannelDO channelDO : dbChannelDOS) {
                list.add(EccOuterChannelResp.buildResp(channelDO));
            }
        } else {
            for (EccOuterChannelDO channelDO : dbChannelDOS) {
                if (channelIds.contains(channelDO.getOuterChannelId())) {
                    list.add(EccOuterChannelResp.buildResp(channelDO));
                }
            }
        }
        return list;
    }

    /**
     * 返回 返回渠道限制 返回null 表示可以查看所有渠道
     */
    public List<Long> selectLimitChannelIds() {
        return adminChannelDOService.listAllOutChannelsLimit();
    }

    public String buildSignBody(EccNcOrderDO orderDo) {
        EccQueryOrderApiV2Resp bodyResp = EccQueryOrderApiV2Resp.buildResp(orderDo);
        JSONObject bodyReq = JSON.parseObject(JSON.toJSONString(bodyResp));
        String signStr = bodyReq.entrySet().stream().sorted(Map.Entry.comparingByKey())
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        Long channelId = orderDo.getChannelId();
        EccOuterChannelDO channelDO = this.getById(channelId);
        if (Objects.isNull(channelDO)) {
            log.error("外部渠道回调通知异常 outerChannelId:{}, orderId:{}", channelId, orderDo.getOrderId());
            throw BizException.create(BizErrorCodeEnum.MCH_NOT_FOUND, "外部渠道回调通知异常");
        }
        String sign = DigestUtils.md5Hex(signStr + "&secret=" + channelDO.getApiSecret());
        bodyReq.put("sign", sign);
        return JSON.toJSONString(bodyReq);
    }

    public String buildSignBody(EccZopOrderDO updatedOrder) {
        EccQueryOrderApiV2Resp req = EccQueryOrderApiV2Resp.buildResp(updatedOrder);
        JSONObject bodyReq = JSON.parseObject(JSON.toJSONString(req));
        String signStr = bodyReq.entrySet().stream().sorted(Map.Entry.comparingByKey())
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        Long channelId = updatedOrder.getChannelId();
        EccOuterChannelDO channelDO = this.getById(channelId);
        if (Objects.isNull(channelDO)) {
            log.error("外部渠道回调通知异常 outerChannelId:{}, orderId:{}", channelId, updatedOrder.getZopOrderId());
            throw BizException.create(BizErrorCodeEnum.MCH_NOT_FOUND, "外部渠道回调通知异常");
        }
        String sign = DigestUtils.md5Hex(signStr + "&secret=" + channelDO.getApiSecret());
        bodyReq.put("sign", sign);
        return JSON.toJSONString(bodyReq);
    }

    /**
     * 初始化新增渠道的基础字段
     */
    private void initializeNewChannel(EccOuterChannelDO channelDO) {
        // 清空ID，确保是新增操作
        channelDO.setOuterChannelId(null);

        // 随机生成API密钥
        channelDO.setApiKey(RandomUtil.randomString(16));
        channelDO.setApiSecret(RandomUtil.randomString(32));
        if (channelDO.getDeductionRate() == null) {
            channelDO.setDeductionRate(0);
        }
    }

    /**
     * 新增渠道业务验证
     */
    private void validateChannelForSave(EccOuterChannelDO channelDO) {

        // 验证渠道等级
        validateChannelLevel(channelDO);

        // 验证扣量比例
        validateDeductionRate(channelDO.getDeductionRate());
    }

    /**
     * 更新渠道业务验证
     */
    private void validateChannelForUpdate(EccOuterChannelDO channelDO) {
        EccOuterChannelDO dbChannelDO = getById(channelDO.getOuterChannelId());
        if (Objects.isNull(dbChannelDO)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "渠道不存在");
        }
        if (Objects.equals(dbChannelDO.getChannelLevel(), channelDO.getChannelLevel())) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "渠道等级不能修改");
        }

        // 验证渠道等级
        validateChannelLevel(channelDO);

        // 验证扣量比例
        validateDeductionRate(channelDO.getDeductionRate());
    }


    /**
     * 验证渠道等级
     */
    private void validateChannelLevel(EccOuterChannelDO channelDO) {
        Integer channelLevel = channelDO.getChannelLevel();
        Long parentChannelId = channelDO.getParentChannelId();

        // 验证渠道等级范围
        if (Objects.isNull(channelLevel) || channelLevel < 1 || channelLevel > ECC_OUT_CHANNEL_MAX_LEVEL) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "渠道等级在1-" + ECC_OUT_CHANNEL_MAX_LEVEL + "之间");
        }
        // 前面已经判断过 新增的上级上级 ID有权限 值要检测 level 和这个上级渠道是否匹配就行
        if (parentChannelId != null) {
            EccOuterChannelDO parentChannel = eccOuterChannelDOMapper.selectById(parentChannelId);
            if (parentChannel.getChannelLevel() + 1 != channelLevel) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "该渠道等级不能选择该上级渠道");
            }
        } else if (channelLevel != 1) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "一级渠道的无需选择上级渠道");
        }
    }

    /**
     * 验证扣量比例
     */
    private void validateDeductionRate(Integer deductionRate) {
        if (Objects.equals(deductionRate, 0)) {
            return;
        }
        if (deductionRate == null || deductionRate < 0 || deductionRate > 95) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "扣量比例必须在0-95之间");
        }
        if (!deductionCalculationService.isSupportedDeductionRate(deductionRate)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "扣量比例必须是5的倍数");
        }
    }


    /**
     * 获取所有子渠道（所有层级的子渠道）
     * 使用递归CTE一次性查询，避免N+1问题
     */
    public List<Long> getAllDescendantChannelIds(Long parentChannelId) {
        if (parentChannelId == null) {
            return new ArrayList<>();
        }

        // 使用递归CTE一次性查询所有下级渠道
        List<EccOuterChannelDO> descendants = baseMapper.selectAllDescendantChannels(parentChannelId);
        return descendants.stream()
                .map(EccOuterChannelDO::getOuterChannelId)
                .collect(Collectors.toList());
    }



}
