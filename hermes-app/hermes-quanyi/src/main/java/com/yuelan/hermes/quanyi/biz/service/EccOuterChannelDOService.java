package com.yuelan.hermes.quanyi.biz.service;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccNcOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderDO;
import com.yuelan.hermes.quanyi.common.util.MpPageUtil;
import com.yuelan.hermes.quanyi.controller.request.EccOuterChannelListReq;
import com.yuelan.hermes.quanyi.controller.request.EccOuterChannelSaveReq;
import com.yuelan.hermes.quanyi.controller.response.EccOuterChannelResp;
import com.yuelan.hermes.quanyi.controller.response.EccQueryOrderApiV2Resp;
import com.yuelan.hermes.quanyi.mapper.EccOuterChannelDOMapper;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/5/17 下午4:02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EccOuterChannelDOService extends ServiceImpl<EccOuterChannelDOMapper, EccOuterChannelDO> {

    private final EccOuterChannelDOMapper eccOuterChannelDOMapper;
    private final EccAdminChannelDOService adminChannelDOService;

    public boolean existsById(Long outerChannelId) {
        return getById(outerChannelId) != null;
    }

    /**
     * 分页列表
     */
    public PageData<EccOuterChannelResp> pageList(EccOuterChannelListReq req) {
        IPage<EccOuterChannelDO> page = MpPageUtil.convertPageRequest(req);
        page = eccOuterChannelDOMapper.selectPage(page, req.buildQueryWrapper());
        List<EccOuterChannelResp> respList = page.getRecords().stream()
                .map(EccOuterChannelResp::buildResp).collect(Collectors.toList());
        return PageData.create(respList, page.getTotal(), req.getPage(), req.getSize());
    }

    /**
     * 新增外部渠道
     */
    public void save(EccOuterChannelSaveReq req) {
        EccOuterChannelDO channelDO = req.convert();
        channelDO.setOuterChannelId(null);
        // 随机生成apiKey
        channelDO.setApiKey(RandomUtil.randomString(16));
        channelDO.setApiSecret(RandomUtil.randomString(32));
        saveCheck(channelDO);
        save(channelDO);
    }

    /**
     * 更新
     */
    public void update(EccOuterChannelSaveReq req) {
        EccOuterChannelDO channelDO = req.convert();
        updateCheck(channelDO);
        updateById(channelDO);
    }

    /**
     * 通过apiKey查询外部渠道
     */
    public EccOuterChannelDO getByApiKey(String apiKey) {
        return eccOuterChannelDOMapper.selectOne(Wrappers.lambdaQuery(EccOuterChannelDO.class).eq(EccOuterChannelDO::getApiKey, apiKey));
    }

    /**
     * 保存前校验
     *
     * @param saveDO 保存对象
     */
    private void saveCheck(EccOuterChannelDO saveDO) {
        nameRepeatCheck(saveDO);
    }

    /**
     * 更新前校验
     *
     * @param updateDO 更新对象
     */
    private void updateCheck(EccOuterChannelDO updateDO) {
        EccOuterChannelDO dbChannelDO = getById(updateDO.getOuterChannelId());
        if (Objects.isNull(dbChannelDO)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "渠道不存在");
        }
        if (!Objects.equals(dbChannelDO.getChannelName(), updateDO.getChannelName())) {
            nameRepeatCheck(updateDO);
        }
    }

    /**
     * 重名检查
     */
    private void nameRepeatCheck(EccOuterChannelDO channelDO) {
        EccOuterChannelDO dbChannelDO = eccOuterChannelDOMapper.selectByName(channelDO.getChannelName());
        if (Objects.nonNull(dbChannelDO)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "渠道名字重复");
        }
    }

    /**
     * 前端展示渠道选项
     */
    public List<EccOuterChannelResp> selectOptionsByAdmin(long adminId) {
        List<Long> channelIds = adminChannelDOService.listChannelIdsByAdminId(adminId);
        List<EccOuterChannelDO> dbChannelDOS = this.list();
        List<EccOuterChannelResp> list = new ArrayList<>();
        if (Objects.isNull(channelIds)) {
            // 没有限制
            for (EccOuterChannelDO channelDO : dbChannelDOS) {
                list.add(EccOuterChannelResp.buildResp(channelDO));
            }
        } else {
            for (EccOuterChannelDO channelDO : dbChannelDOS) {
                if (channelIds.contains(channelDO.getOuterChannelId())) {
                    list.add(EccOuterChannelResp.buildResp(channelDO));
                }
            }
        }
        return list;
    }

    /**
     * 返回 返回渠道限制 返回null 表示可以查看所有渠道
     */
    public List<Long> selectLimitChannelIds(long adminId) {
        List<Long> channelIds = adminChannelDOService.listChannelIdsByAdminId(adminId);
        if (Objects.isNull(channelIds)) {
            // 没有限制
            return null;
        }
        List<Long> result = new ArrayList<>();
        List<Long> dbChannelIds = this.list().stream().map(EccOuterChannelDO::getOuterChannelId).collect(Collectors.toList());
        for (Long channelId : channelIds) {
            if (dbChannelIds.contains(channelId)) {
                result.add(channelId);
            }
        }
        return result;
    }

    public String buildSignBody(EccNcOrderDO orderDo) {
        EccQueryOrderApiV2Resp bodyResp = EccQueryOrderApiV2Resp.buildResp(orderDo);
        JSONObject bodyReq = JSON.parseObject(JSON.toJSONString(bodyResp));
        String signStr = bodyReq.entrySet().stream().sorted(Map.Entry.comparingByKey())
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        Long channelId = orderDo.getChannelId();
        EccOuterChannelDO channelDO = this.getById(channelId);
        if (Objects.isNull(channelDO)) {
            log.error("外部渠道回调通知异常 outerChannelId:{}, orderId:{}", channelId, orderDo.getOrderId());
            throw BizException.create(BizErrorCodeEnum.MCH_NOT_FOUND, "外部渠道回调通知异常");
        }
        String sign = DigestUtils.md5Hex(signStr + "&secret=" + channelDO.getApiSecret());
        bodyReq.put("sign", sign);
        return JSON.toJSONString(bodyReq);
    }

    public String buildSignBody(EccZopOrderDO updatedOrder) {
        EccQueryOrderApiV2Resp req = EccQueryOrderApiV2Resp.buildResp(updatedOrder);
        JSONObject bodyReq = JSON.parseObject(JSON.toJSONString(req));
        String signStr = bodyReq.entrySet().stream().sorted(Map.Entry.comparingByKey())
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        Long channelId = updatedOrder.getChannelId();
        EccOuterChannelDO channelDO = this.getById(channelId);
        if (Objects.isNull(channelDO)) {
            log.error("外部渠道回调通知异常 outerChannelId:{}, orderId:{}", channelId, updatedOrder.getZopOrderId());
            throw BizException.create(BizErrorCodeEnum.MCH_NOT_FOUND, "外部渠道回调通知异常");
        }
        String sign = DigestUtils.md5Hex(signStr + "&secret=" + channelDO.getApiSecret());
        bodyReq.put("sign", sign);
        return JSON.toJSONString(bodyReq);
    }
}
