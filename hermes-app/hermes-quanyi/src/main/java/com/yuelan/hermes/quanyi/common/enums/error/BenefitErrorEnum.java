package com.yuelan.hermes.quanyi.common.enums.error;

import com.yuelan.result.able.IErrorCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 2025/4/21
 * @since 2025/4/21
 */
@AllArgsConstructor
@Getter
public enum BenefitErrorEnum implements IErrorCode<BenefitErrorEnum> {

    ORDER_TYPE_NOT_SUPPORT(50000, "订单类型不支持"),
    // 订单真正处理中 并发下单
    ORDER_PROCESSING(50001, "订单处理中"),
    // 权益包不存在或已下架
    BENEFIT_PACKAGE_NOT_FOUND(50002, "权益包不存在或已下架"),
    // 订单号已经存在
    ORDER_NO_EXIST(50003, "订单号已经存在"),
    // 权益订单状态错误
    ORDER_STATUS_ERROR(50004, "权益订单状态错误"),
    // 订单不存在
    ORDER_NOT_FOUND(50005, "订单不存在"),
    // 领取异常
    REDEEM_ERROR(50006, "系统错误，请稍后再试"),


    ;

    private final int code;
    private final String desc;

    @Override
    public String getCode() {
        return String.valueOf(code);
    }

    @Override
    public String getDesc() {
        return desc;
    }

}
