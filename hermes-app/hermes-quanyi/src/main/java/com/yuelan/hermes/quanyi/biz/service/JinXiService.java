package com.yuelan.hermes.quanyi.biz.service;

import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.biz.handler.CacheHandler;
import com.yuelan.hermes.quanyi.common.event.JinXiOrderNotifyEvent;
import com.yuelan.hermes.quanyi.common.pojo.properties.JinXiProperties;
import com.yuelan.hermes.quanyi.remote.JinXiManager;
import com.yuelan.hermes.quanyi.remote.jinxi.request.JinXiCallBackReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 今溪服务类
 * 参考SoftGameService实现
 *
 * <AUTHOR> 2025/8/2
 * @since 2025/8/2
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JinXiService {

    private final JinXiProperties jinXiProperties;
    private final JinXiManager jinXiManager;
    private final CacheHandler cacheHandler;
    private final ApplicationEventPublisher applicationEventPublisher;

    /**
     * 处理异步通知
     * 参考SoftGameService实现
     *
     * @param request 通知请求
     * @return 处理结果
     */
    public boolean handleNotify(JinXiCallBackReq request) {
        String orderNo = request.getCustomOrderId();
        RLock lock = cacheHandler.getCommOrderLock(orderNo);
        try {
            if (Objects.isNull(lock)) {
                log.error("今溪异步通知获取锁失败,orderId:{}", orderNo);
                // 订单正在处理请稍后回调
                return false;
            }

            // 1. 验证签名
            if (!verifyNotifySign(request)) {
                log.error("今溪异步通知签名验证失败: {}", JSON.toJSONString(request));
                return false;
            }

            // 2. 只处理成功和失败状态，其他状态忽略
            if (!request.isFinalStatus()) {
                log.info("今溪异步通知,订单处理中,忽略: {}", request.getCustomOrderId());
                return true; // 处理中状态直接返回成功
            }

            // 3. 发布事件，让多个模块处理
            JinXiOrderNotifyEvent event = new JinXiOrderNotifyEvent();
            event.setRequest(request);
            applicationEventPublisher.publishEvent(event);

            if (!event.getIsHandled()) {
                log.error("今溪异步通知未被处理: {}", JSON.toJSONString(request));
                return false;
            } else {
                return event.getSuccessDeal();
            }
        } catch (Exception e) {
            log.error("今溪异步通知处理异常: 订单号={}", request.getCustomOrderId(), e);
            return false;
        } finally {
            cacheHandler.releaseLock(lock);
        }
    }

    /**
     * 验证异步通知签名
     * 使用JinXiManager的generateOrderRechargeSign方法
     *
     * @param request 通知请求
     * @return 验证结果
     */
    private boolean verifyNotifySign(JinXiCallBackReq request) {
        String sign = request.getSign();
        request.setSign(null);
        // 使用JinXiManager的签名生成方法
        String expectedSign = JinXiManager.generateOrderRechargeSign(request, jinXiProperties.getSignKey());
        boolean isValid = expectedSign.equals(sign);
        if (!isValid) {
            log.error("今溪异步通知签名验证失败: 期望={}, 实际={}", expectedSign, sign);
        }
        return isValid;
    }
}
