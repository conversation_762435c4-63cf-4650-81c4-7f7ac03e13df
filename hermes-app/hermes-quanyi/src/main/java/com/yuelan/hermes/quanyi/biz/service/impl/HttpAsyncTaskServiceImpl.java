package com.yuelan.hermes.quanyi.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuelan.hermes.quanyi.biz.service.HttpAsyncTaskService;
import com.yuelan.hermes.quanyi.common.enums.TaskStatusEnum;
import com.yuelan.hermes.quanyi.common.interfaces.SuccessStrategy;
import com.yuelan.hermes.quanyi.common.pojo.domain.HttpAsyncTask;
import com.yuelan.hermes.quanyi.config.task.CallbackRegistry;
import com.yuelan.hermes.quanyi.config.task.HttpTaskRequest;
import com.yuelan.hermes.quanyi.config.task.SuccessStrategyFactory;
import com.yuelan.hermes.quanyi.config.task.TaskCompletedEvent;
import com.yuelan.hermes.quanyi.mapper.HttpAsyncTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2025/3/21
 * @since 2025/3/21
 */
@Service
@Slf4j
public class HttpAsyncTaskServiceImpl extends ServiceImpl<HttpAsyncTaskMapper, HttpAsyncTask> implements HttpAsyncTaskService {

    private final HttpAsyncTaskMapper httpAsyncTaskDao;
    private final SuccessStrategyFactory successStrategyFactory;
    private final ThreadPoolTaskExecutor taskExecutor;
    private final ApplicationEventPublisher eventPublisher;
    private final CallbackRegistry callbackRegistry;
    @Resource
    private HttpAsyncTaskServiceImpl self;

    public HttpAsyncTaskServiceImpl(
            HttpAsyncTaskMapper httpAsyncTaskDao,
            SuccessStrategyFactory successStrategyFactory,
            @Qualifier("httpTaskExecutor") ThreadPoolTaskExecutor taskExecutor,
            ApplicationEventPublisher eventPublisher,
            CallbackRegistry callbackRegistry) {
        this.httpAsyncTaskDao = httpAsyncTaskDao;
        this.successStrategyFactory = successStrategyFactory;
        this.taskExecutor = taskExecutor;
        this.eventPublisher = eventPublisher;
        this.callbackRegistry = callbackRegistry;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTask(HttpTaskRequest request) {
        // 构建任务实体
        HttpAsyncTask task = httpTaskRequest2Task(request);
        // 插入数据库
        save(task);

        return task.getRequestId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTask(List<HttpTaskRequest> requests) {
        List<HttpAsyncTask> tasks = requests.stream()
                .map(this::httpTaskRequest2Task)
                .collect(Collectors.toList());

        // 插入数据库
        saveBatch(tasks, 1000);
    }


    private HttpAsyncTask httpTaskRequest2Task(HttpTaskRequest request) {
        return HttpAsyncTask.builder()
                .requestId(UUID.randomUUID().toString().replace("-", ""))
                .url(request.getUrl())
                .method(request.getMethod())
                .body(request.getBody())
                .headers(request.getHeaders() != null ? JSON.toJSONString(request.getHeaders()) : null)
                .contentType(request.getContentType() != null ? request.getContentType() : null)
                .sourceSystem(request.getSourceSystem())
                .businessType(request.getBusinessType())
                .businessId(request.getBusinessId())
                .status(TaskStatusEnum.PENDING.getCode())
                .retryCount(0)
                .maxRetryCount(request.getMaxRetryCount())
                .retryInterval(request.getRetryInterval())
                .nextRetryTime(LocalDateTime.now())
                .successStrategy(request.getSuccessStrategy().name())
                .connectTimeout(request.getConnectTimeout())
                .readTimeout(request.getSocketTimeout())
                .extData(request.getExtData())
                .build();

    }

    @Override
    public boolean processTask(HttpAsyncTask task) {
        if (task == null) {
            return false;
        }

        // 更新任务状态为处理中
        self.updateTaskStatus(task, TaskStatusEnum.PROCESSING.getCode(),
                task.getLastResponse(), task.getLastResponseCode());

        // 使用线程池异步处理任务
        taskExecutor.execute(() -> {
            try {
                HttpRequest httpReq = createHttpRequest(task);
                configureRequestTimeouts(httpReq, task);
                log.info("request: {}", httpReq);
                HttpResponse executeResp = httpReq.execute();
                log.info("executeResp: {}", executeResp);
                String responseBody = executeResp.body();
                int statusCode = executeResp.getStatus();

                // 更新响应信息
                task.setLastResponseCode(statusCode);
                task.setLastResponse(StrUtil.sub(responseBody, 0, 1024));

                // 判断是否成功
                SuccessStrategy strategy = successStrategyFactory.getStrategy(task.getSuccessStrategy());
                boolean isSuccess = strategy.isSuccess(task);

                if (isSuccess) {
                    handleTaskSuccess(task, responseBody, statusCode);
                } else {
                    handleTaskFailure(task, responseBody, statusCode, null);
                }
            } catch (Exception e) {
                log.error("处理HTTP异步任务异常, taskId: {}, error: {}", task.getId(), e.getMessage(), e);
                handleTaskFailure(task, "请求异常: " + e.getMessage(), null, e);
            }
        });

        return true;
    }

    private void configureRequestTimeouts(HttpRequest httpReq, HttpAsyncTask task) {
        if (Objects.nonNull(task.getConnectTimeout())) {
            httpReq.setConnectionTimeout(task.getConnectTimeout());
        }
        if (Objects.nonNull(task.getReadTimeout())) {
            httpReq.setReadTimeout(task.getReadTimeout());
        }
    }

    public void handleTaskSuccess(HttpAsyncTask task, String responseBody, Integer statusCode) {
        // 更新为成功状态
        self.updateTaskStatus(task, TaskStatusEnum.SUCCESS.getCode(), responseBody, statusCode);

        // 使用 CallbackRegistry 执行回调
        callbackRegistry.executeCallback(task, true);

        // 发布任务完成事件
        eventPublisher.publishEvent(new TaskCompletedEvent(this, task, true));
    }

    private void handleTaskFailure(HttpAsyncTask task, String responseBody, Integer statusCode, Exception exception) {
        // 判断是否需要重试
        if (task.getRetryCount() < task.getMaxRetryCount()) {
            int newRetryCount = task.getRetryCount() + 1;
            LocalDateTime nextRetryTime = calculateExponentialRetryTime(newRetryCount, task.getRetryInterval());

            // 更新重试信息和状态
            self.updateRetryInfoAndStatus(task.getId(), newRetryCount, nextRetryTime,
                    TaskStatusEnum.FAILED.getCode(), responseBody, statusCode);

            log.info("更新任务失败状态，设置重试，任务ID: {}, 重试次数: {}, 下次重试时间: {}",
                    task.getId(), newRetryCount, nextRetryTime);
        } else {
            // 已达到最大重试次数，标记为最终失败
            self.updateTaskStatus(task, TaskStatusEnum.FINAL_FAILED.getCode(), responseBody, statusCode);

            // 执行回调和发布事件
            callbackRegistry.executeCallback(task, false);
            eventPublisher.publishEvent(new TaskCompletedEvent(this, task, false));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateTaskStatus(HttpAsyncTask task, Integer status, String response, Integer responseCode) {
        log.info("更新任务状态，任务ID: {}, 状态: {}", task.getId(), status);
        boolean updateResult = httpAsyncTaskDao.updateStatus(
                task.getId(),
                status,
                response,
                responseCode
        );
        log.info("更新任务状态结果: {}", updateResult);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRetryInfoAndStatus(Long taskId, int newRetryCount, LocalDateTime nextRetryTime,
                                         Integer status, String response, Integer responseCode) {
        log.info("开始更新任务ID: {} 的重试信息, 重试次数: {}, 下次重试时间: {}",
                taskId, newRetryCount, nextRetryTime);

        boolean retryResult = httpAsyncTaskDao.updateRetryInfo(taskId, newRetryCount, nextRetryTime);
        log.info("更新重试信息结果: {}", retryResult);

        boolean statusResult = httpAsyncTaskDao.updateStatus(
                taskId,
                status,
                response,
                responseCode
        );
        log.info("更新状态结果: {}", statusResult);
    }

    @Override
    public void processPendingTasks(int batchSize) {
        List<HttpAsyncTask> pendingTasks = httpAsyncTaskDao.selectPendingTasks(batchSize);
        for (HttpAsyncTask task : pendingTasks) {
            try {
                processTask(task);
            } catch (Exception e) {
                log.error("处理待执行任务异常, taskId: {}, error: {}", task.getId(), e.getMessage(), e);
            }
        }
    }

    @Override
    public void processRetryTasks(int batchSize) {
        List<HttpAsyncTask> retryTasks = httpAsyncTaskDao.selectTasksForRetry(LocalDateTime.now(), batchSize);
        for (HttpAsyncTask task : retryTasks) {
            try {
                processTask(task);
            } catch (Exception e) {
                log.error("处理重试任务异常, taskId: {}, error: {}", task.getId(), e.getMessage(), e);
            }
        }
    }

    @Override
    public List<HttpAsyncTask> getTasksByBusiness(String businessType, String businessId) {
        return httpAsyncTaskDao.selectByBusiness(businessType, businessId);
    }

    @Override
    public HttpAsyncTask getTaskByRequestId(String requestId) {
        return httpAsyncTaskDao.selectByRequestId(requestId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean retryTask(String requestId) {
        HttpAsyncTask task = httpAsyncTaskDao.selectByRequestId(requestId);
        if (task == null || !TaskStatusEnum.FAILED.getCode().equals(task.getStatus())) {
            return false;
        }

        // 重置重试次数和下次重试时间
        task.setRetryCount(0);
        task.setNextRetryTime(LocalDateTime.now());
        task.setStatus(TaskStatusEnum.PENDING.getCode());

        return updateById(task);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelTask(String requestId) {
        HttpAsyncTask task = httpAsyncTaskDao.selectByRequestId(requestId);
        if (task == null || TaskStatusEnum.SUCCESS.getCode().equals(task.getStatus())) {
            return false;
        }

        // 标记为失败状态
        task.setStatus(TaskStatusEnum.FAILED.getCode());
        task.setRemark(task.getRemark() != null ? task.getRemark() + " [手动取消]" : "[手动取消]");

        return updateById(task);
    }

    /**
     * 创建HTTP请求对象
     */
    private HttpRequest createHttpRequest(HttpAsyncTask task) {
        String method = task.getMethod().toUpperCase();
        String url = task.getUrl();
        Map<String, String> headerMap = JSONObject.parseObject(task.getHeaders(), new TypeReference<Map<String, String>>() {
        });
        HttpRequest request;
        switch (method) {
            case "GET":
                request = HttpRequest.get(url);
                break;
            case "POST":
                request = HttpRequest.post(url);
                request.body(task.getBody());
                break;
            default:
                throw new IllegalArgumentException("不支持的HTTP方法: " + method);
        }
        request.addHeaders(headerMap);
        return request;
    }

    /**
     * 计算指数级增长的重试时间
     *
     * @param currentRetryCount 当前重试次数
     * @param baseInterval      基础间隔时间（秒）
     * @return 下次重试时间
     */
    private LocalDateTime calculateExponentialRetryTime(int currentRetryCount, int baseInterval) {
        // 使用阶梯式增长系数而不是纯指数增长
        double[] multipliers = {
                // 第1次重试 - 基础间隔 (例如5分钟)
                1,
                // 第2次重试 - 2倍基础间隔 (例如10分钟)
                2,
                // 第3次重试 - 12倍基础间隔 (例如1小时)
                12,
                // 第4次重试 - 72倍基础间隔 (例如6小时)
                72,
                // 第5次重试 - 144倍基础间隔 (例如12小时)
                144,
                // 第6次及以后重试 - 288倍基础间隔 (例如24小时)
                288
        };

        // 确定使用哪个倍数
        int index = Math.min(currentRetryCount, multipliers.length - 1);
        double multiplier = multipliers[index];

        // 计算下次重试间隔
        long nextInterval = Math.round(baseInterval * multiplier);

        log.info("计算阶梯式重试间隔: 基础间隔{}秒, 第{}次重试, 使用倍数{}, 计算结果={}秒",
                baseInterval, currentRetryCount + 1, multiplier, nextInterval);

        return LocalDateTime.now().plusSeconds(nextInterval);
    }
}