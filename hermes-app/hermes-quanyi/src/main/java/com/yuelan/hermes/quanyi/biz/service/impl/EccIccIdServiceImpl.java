package com.yuelan.hermes.quanyi.biz.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuelan.core.util.MapstructUtils;
import com.yuelan.hermes.commons.util.EasyExcelUtil;
import com.yuelan.hermes.commons.util.IccidUtil;
import com.yuelan.hermes.quanyi.biz.service.EccChannelDOService;
import com.yuelan.hermes.quanyi.biz.service.EccIccIdService;
import com.yuelan.hermes.quanyi.biz.service.EccOuterChannelDOService;
import com.yuelan.hermes.quanyi.biz.service.EccProductDOService;
import com.yuelan.hermes.quanyi.common.enums.EccChannelTypeEnum;
import com.yuelan.hermes.quanyi.common.enums.SpProdEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccIccIdDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import com.yuelan.hermes.quanyi.common.pojo.excel.EccIccIdImportExcel;
import com.yuelan.hermes.quanyi.common.util.MpPageUtil;
import com.yuelan.hermes.quanyi.controller.request.EccIccIdReq;
import com.yuelan.hermes.quanyi.controller.response.EccIccIdResp;
import com.yuelan.hermes.quanyi.controller.response.ImportResp;
import com.yuelan.hermes.quanyi.mapper.EccIccIdMapper;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ICCID服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EccIccIdServiceImpl extends ServiceImpl<EccIccIdMapper, EccIccIdDO> implements EccIccIdService {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final int BATCH_SIZE = 500;
    private final EccOuterChannelDOService eccOuterChannelDOService;
    private final EccChannelDOService eccChannelDOService;
    private final EccProductDOService eccProductDOService;

    @Override
    @Lock4j(name = "ecc_iccid_import")
    public ImportResp importIccIdFile(MultipartFile file, Integer channelType, Long channelId, Long productId) throws IOException {
        log.info("开始导入ICCID文件: {}", file.getOriginalFilename());

        // 读取Excel文件
        List<EccIccIdImportExcel> excelRows = EasyExcelFactory.read(file.getInputStream())
                .head(EccIccIdImportExcel.class)
                .sheet()
                .doReadSync();

        if (excelRows.isEmpty()) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "未找到需要导入的数据");
        }

        log.info("读取到{}条ICCID记录", excelRows.size());
        EccChannelTypeEnum eccChannelTypeEnum = EccChannelTypeEnum.of(channelType);
        boolean existsChannel = false;
        if (EccChannelTypeEnum.INNER == eccChannelTypeEnum) {
            existsChannel = eccChannelDOService.getById(channelId) != null;
        } else if (EccChannelTypeEnum.OUTER == eccChannelTypeEnum) {
            existsChannel = eccOuterChannelDOService.existsById(channelId);
        } else {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "无效渠道类型");
        }
        BizException.assertCheck(existsChannel, BaseErrorCodeEnum.PARAMS_ERROR, "无效渠道 Id");

        // 产品检查
        prodCheck(productId);

        // 验证数据并收集ICCID列表
        List<String> iccIdList = new ArrayList<>();
        Set<String> duplicateCheck = new LinkedHashSet<>();
        Long batchId = System.currentTimeMillis();

        for (int i = 0; i < excelRows.size(); i++) {
            EccIccIdImportExcel excelRow = excelRows.get(i);
            int rowNum = i + 2; // Excel行号从2开始（第1行是表头）

            // 验证ICCID
            String iccId = excelRow.getIccId();
            if (CharSequenceUtil.isBlank(iccId)) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,
                        String.format("第%d行：ICCID不能为空", rowNum));
            }

            if (!validateIccIdFormat(iccId)) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,
                        String.format("第%d行：ICCID格式不正确：%s", rowNum, iccId));
            }

            // 检查文件内重复
            if (!duplicateCheck.add(iccId)) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,
                        String.format("第%d行：发现重复ICCID：%s", rowNum, iccId));
            }

            // 验证有效期格式
            if (CharSequenceUtil.isNotBlank(excelRow.getValidityPeriod())) {
                try {
                    LocalDate validityDate = LocalDate.parse(excelRow.getValidityPeriod(), DATE_FORMATTER);
                    if (validityDate.isBefore(LocalDate.now())) {
                        log.warn("第{}行：有效期{}早于当前日期，将发出警告但不拒绝导入", rowNum, validityDate);
                    }
                } catch (DateTimeParseException e) {
                    throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,
                            String.format("第%d行：有效期格式错误，应为YYYY-MM-DD格式：%s", rowNum, excelRow.getValidityPeriod()));
                }
            }

            iccIdList.add(iccId);
        }

        // 批量插入数据
        List<EccIccIdDO> batchInsertList = new ArrayList<>();
        for (EccIccIdImportExcel excelRow : excelRows) {
            EccIccIdDO iccIdDO = convertToEntity(excelRow, batchId, productId, channelType, channelId);
            batchInsertList.add(iccIdDO);
        }
        ImportResp resp = new ImportResp();
        resp.setBatchId(batchId);

        saveBatchIgnore(batchInsertList, 1000);

        Long count = baseMapper.countByBatchId(batchId);
        resp.setSuccessCount(count.intValue());
        resp.setIgnoreCount(excelRows.size() - count.intValue());

        return resp;
    }

    private void prodCheck(Long prodId) {
        EccProductDO productDO = eccProductDOService.getById(prodId);
        BizException.assertCheck(productDO != null, BaseErrorCodeEnum.PARAMS_ERROR, "无效产品 Id");
        SpProdEnum hnDxSxc = SpProdEnum.HN_DX_SXC;
        BizException.assertCheck(hnDxSxc.getProdId().equals(productDO.getSpProdId()), BaseErrorCodeEnum.PARAMS_ERROR, "仅支持导入湖南南电信随销卡");
    }

    private void saveBatchIgnore(List<EccIccIdDO> batchInsertList, int batchSize) {
        List<EccIccIdDO> saveList = new ArrayList<>();
        int index = 0;
        for (EccIccIdDO iccIdDO : batchInsertList) {
            saveList.add(iccIdDO);
            index++;
            if (index % batchSize == 0) {
                baseMapper.saveBatchIgnore(saveList);
                saveList.clear();
            }
        }
        if (!saveList.isEmpty()) {
            baseMapper.saveBatchIgnore(saveList);
        }
    }


    /**
     * 转换Excel行为实体对象
     */
    private EccIccIdDO convertToEntity(EccIccIdImportExcel excelRow, Long batchId, Long productId, Integer channelType, Long channelId) {
        EccIccIdDO iccIdDO = new EccIccIdDO();
        iccIdDO.setIccId(excelRow.getIccId());
        iccIdDO.setProductId(productId);
        iccIdDO.setChannelType(channelType);
        iccIdDO.setChannelId(channelId);
        iccIdDO.setBatchId(batchId);
        // 解析有效期
        if (CharSequenceUtil.isNotBlank(excelRow.getValidityPeriod())) {
            iccIdDO.setValidityPeriod(LocalDate.parse(excelRow.getValidityPeriod(), DATE_FORMATTER));
        }

        return iccIdDO;
    }

    @Override
    public PageData<EccIccIdResp> pageIccId(EccIccIdReq req) {
        IPage<EccIccIdDO> page = MpPageUtil.convertPageRequest(req);
        page = baseMapper.selectPage(page, req.buildQueryWrapper());

        List<EccIccIdResp> respList = page.getRecords().stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());

        return PageData.create(
                respList,
                page.getTotal(),
                req.getPage(),
                req.getSize()
        );
    }

    /**
     * 转换实体为响应对象
     */
    private EccIccIdResp convertToResp(EccIccIdDO iccIdDO) {
        EccIccIdResp resp = MapstructUtils.convertNotNull(iccIdDO, EccIccIdResp.class);
        // 补充渠道名称
        if (EccChannelTypeEnum.INNER.getType().equals(iccIdDO.getChannelType())) {
            resp.setChannelName(iccIdDO.getInnerChannelName());
        } else if (EccChannelTypeEnum.OUTER.getType().equals(iccIdDO.getChannelType())) {
            resp.setChannelName(iccIdDO.getOuterChannelName());
        }
        resp.setStatusDesc();
        return resp;
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        EasyExcelUtil.download(response, "ICCID导入模板", EccIccIdImportExcel.class, Collections.emptyList());
    }

    @Override
    public EccIccIdDO getByIccId(String iccId) {
        return baseMapper.selectOne(Wrappers.<EccIccIdDO>lambdaQuery()
                .eq(EccIccIdDO::getIccId, iccId));
    }

    @Override
    public boolean validateIccIdFormat(String iccId) {
        return IccidUtil.verifyIccid(iccId);
    }
}
