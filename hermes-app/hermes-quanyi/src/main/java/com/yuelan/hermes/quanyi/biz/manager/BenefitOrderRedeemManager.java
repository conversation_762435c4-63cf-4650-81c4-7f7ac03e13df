package com.yuelan.hermes.quanyi.biz.manager;

import cn.hutool.core.util.StrUtil;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.commons.enums.PreorderStatusEnum;
import com.yuelan.hermes.commons.enums.SupplierEnum;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderDOService;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderItemDOService;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitGoodsDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderItemDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.RytProperties;
import com.yuelan.hermes.quanyi.common.pojo.properties.SoftGameProperties;
import com.yuelan.hermes.quanyi.common.pojo.properties.TeZhenProperties;
import com.yuelan.hermes.quanyi.remote.JinXiManager;
import com.yuelan.hermes.quanyi.remote.SoftGameManager;
import com.yuelan.hermes.quanyi.remote.TezhenManager;
import com.yuelan.hermes.quanyi.remote.jinxi.request.JinXiOrderRechargeReq;
import com.yuelan.hermes.quanyi.remote.jinxi.response.JinXiBaseResponse;
import com.yuelan.hermes.quanyi.remote.jinxi.response.JinXiOrderRechargeResp;
import com.yuelan.hermes.quanyi.remote.response.VProductRechargeRsp;
import com.yuelan.hermes.quanyi.remote.softgame.request.SoftGameOrderSubmitReq;
import com.yuelan.hermes.quanyi.remote.softgame.response.SoftGameBaseResponse;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR> 2024/4/8 10:08
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BenefitOrderRedeemManager {
    @Resource
    private BenefitOrderItemDOService benefitOrderItemDOService;
    @Resource
    private BenefitOrderDOService benefitOrderDOService;
    @Resource
    private SoftGameManager softGameManager;
    @Resource
    private SoftGameProperties softGameProperties;
    @Resource
    private RytProperties rytProperties;
    @Resource
    private TezhenManager tezhenManager;
    @Resource
    private TeZhenProperties teZhenProperties;
    @Resource
    private JinXiManager jinXiManager;

    /**
     * 权益兑换（给供应商下单实时下单）
     * <p>
     * 兑换失败异常之后 还可以重试兑换
     */
    public boolean redeemBenefit(BenefitOrderDO orderDO, BenefitOrderItemDO orderItemDo, BenefitGoodsDO goodsDO) {
        String supplierOrderNo;
        Integer preOrderStatus;
        String respContent;
        StringBuilder respBuild = new StringBuilder();
        try {
            Integer supplierType = orderItemDo.getSupplierType();
            String itemNo = orderItemDo.getItemNo();
            String phone = orderItemDo.getPhone();
            String supplierGoodsCode = orderItemDo.getSupplierGoodsNo();
            if (Objects.equals(SupplierEnum.RYT.getCode(), supplierType)) {
                SoftGameBaseResponse<String> softGameBaseResponse = submitRechargeOrder(orderDO, orderItemDo, goodsDO, new StringBuilder(), respBuild);
                supplierOrderNo = null; // 软游通新接口没有订单号
                preOrderStatus = softGameBaseResponse.isSuccess() ? PreorderStatusEnum.SUCCESS.getCode() : PreorderStatusEnum.FAIL.getCode();
            } else if (Objects.equals(SupplierEnum.TE_ZHEN.getCode(), supplierType)) {
                VProductRechargeRsp rechargeRsp = tezhenManager.vproductrecharge(itemNo, phone, supplierGoodsCode, teZhenProperties.getCommCallBackUrl(), new StringBuilder(), respBuild);
                supplierOrderNo = String.valueOf(rechargeRsp.getOrderId());
                preOrderStatus = PreorderStatusEnum.SUCCESS.getCode();
            } else if (Objects.equals(SupplierEnum.JIN_XI.getCode(), supplierType)) {
                JinXiBaseResponse<JinXiOrderRechargeResp> submittedJinXiOrderResp = submitJinXiOrder(orderDO, orderItemDo, goodsDO, new StringBuilder(), respBuild);
                JinXiOrderRechargeResp data = submittedJinXiOrderResp.getData();
                preOrderStatus = submittedJinXiOrderResp.isSuccess() ? PreorderStatusEnum.SUCCESS.getCode() : PreorderStatusEnum.FAIL.getCode();
                if (data != null) {
                    supplierOrderNo = data.getOrderId();
                } else {
                    supplierOrderNo = null;
                }
            } else {
                log.error("不支持该商品渠道兑换");
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "不支持该商品兑换");
            }
        } catch (Exception e) {
            supplierOrderNo = "";
            preOrderStatus = PreorderStatusEnum.FAIL.getCode();
            log.error("权益兑换异常", e);
        }
        respContent = StrUtil.sub(respBuild.toString(), 0, 500);
        BenefitOrderItemDO updateOrderItem = new BenefitOrderItemDO();
        updateOrderItem.setItemId(orderItemDo.getItemId());
        updateOrderItem.setSupplierOrderNo(supplierOrderNo);
        updateOrderItem.setPreorderContent(respContent);
        updateOrderItem.setPreorderStatus(preOrderStatus);
        OrderStatusEnum orderStatus = preOrderStatus.equals(PreorderStatusEnum.SUCCESS.getCode()) ? OrderStatusEnum.PROCESSING : OrderStatusEnum.ABNORMAL;
        updateOrderItem.setOrderStatus(orderStatus.getCode());
        benefitOrderItemDOService.updateById(updateOrderItem);
        afterPreorder(preOrderStatus, orderDO.getOrderId());
        return preOrderStatus.equals(PreorderStatusEnum.SUCCESS.getCode());
    }

    public SoftGameBaseResponse<String> submitRechargeOrder(BenefitOrderDO orderDO, BenefitOrderItemDO orderItemDo, BenefitGoodsDO goodsDO, StringBuilder reqBuild, StringBuilder respBuild) {
        final String callBackUrl = softGameProperties.getNoticeUrl();
        String itemNo = orderItemDo.getItemNo();
        String goodsCode = goodsDO.getSupplierGoodsNo();
        BigDecimal parValue = goodsDO.getParValue();
        String phone = orderDO.getPhone();
        String goodsName = goodsDO.getGoodsName();

        SoftGameOrderSubmitReq req = new SoftGameOrderSubmitReq();
        req.setUserOrderId(itemNo);
        req.setGoodsId(goodsCode);
        req.setUserName(phone);
        req.setAcctType("手机号");
        req.setGameName(goodsName);
        req.setGoodsNum(parValue.stripTrailingZeros().toPlainString());
        req.setNoticeUrl(callBackUrl);

        SoftGameBaseResponse<String> response = softGameManager.submitOrder(req, reqBuild, respBuild);

        if (response.isSuccess()) {
            log.info("软游通v2订单提交成功: 订单号={}", itemNo);
        } else {
            log.error("软游通v2订单提交失败: 订单号={}, 错误={}", itemNo, response.getMes());
        }
        return response;
    }

    public JinXiBaseResponse<JinXiOrderRechargeResp> submitJinXiOrder(BenefitOrderDO orderDO, BenefitOrderItemDO orderItemDo, BenefitGoodsDO goodsDO, StringBuilder reqBuild, StringBuilder respBuild) {
        JinXiOrderRechargeReq req = new JinXiOrderRechargeReq();
        req.setBuyNumber(1);
        req.setCustomOrderId(orderItemDo.getItemNo());
        req.setProductId(goodsDO.getSupplierGoodsNo());
        req.setRechargeAccount(orderDO.getPhone());

        JinXiBaseResponse<JinXiOrderRechargeResp> orderedRecharge = jinXiManager.orderRecharge(req, reqBuild, respBuild);
        if (orderedRecharge.isSuccess()) {
            log.info("今溪订单提交成功: 订单号={}", orderItemDo.getItemNo());
        } else {
            log.error("今溪订单提交失败: 订单号={}, 错误={}", orderItemDo.getItemNo(), orderedRecharge.getMessage());
        }
        return orderedRecharge;
    }

    /**
     * @param preorderStatus 预下单状态
     * @param orderId        总订单的订单id
     */
    private void afterPreorder(Integer preorderStatus, Long orderId) {
        if (PreorderStatusEnum.SUCCESS.getCode().equals(preorderStatus)) {
            // 减少订单可兑换次数
            boolean successUpdate = benefitOrderDOService.decreaseOrderRemainNum(orderId);
            if (!successUpdate) {
                log.error("更新订单可兑换次数失败");
                throw BizException.create(BaseErrorCodeEnum.INTERNAL_SERVER_ERROR);
            }
        } else {
            //  更新主订单异常状态
            benefitOrderDOService.updateOrderStatus(orderId, OrderStatusEnum.ABNORMAL);
        }
    }


}
