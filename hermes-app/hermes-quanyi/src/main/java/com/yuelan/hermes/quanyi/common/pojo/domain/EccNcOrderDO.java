package com.yuelan.hermes.quanyi.common.pojo.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 2024/12/25
 * @since 2024/12/25
 * 通用号卡订单
 */
@Schema(description = "通用号卡订单")
@Data
@TableName(value = "ecc_nc_order")
public class EccNcOrderDO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId(value = "order_id", type = IdType.AUTO)
    @Schema(description = "主键id")
    private Long orderId;
    /**
     * 我方订单号
     */
    @TableField(value = "order_no")
    @Schema(description = "我方订单号")
    private String orderNo;
    /**
     * 渠道订单号
     */
    @TableField(value = "channel_order_no")
    @Schema(description = "渠道订单号")
    private String channelOrderNo;
    /**
     * 服务提供商订单号
     */
    @TableField(value = "sp_order_no")
    @Schema(description = "服务提供商订单号")
    private String spOrderNo;
    /**
     * 原始订单号（补发类型订单特有）
     */
    @TableField(value = "original_order_no")
    @Schema(description = "原始订单号（补发类型订单特有）")
    private String originalOrderNo;
    /**
     * 运营商
     */
    @TableField(value = "`operator`")
    @Schema(description = "运营商")
    private Integer operator;
    /**
     * 我方号卡产品id
     */
    @TableField(value = "prod_id")
    @Schema(description = "我方号卡产品id")
    private Long prodId;
    /**
     * 权益包商品名字
     */
    @TableField(value = "prod_name")
    @Schema(description = "权益包商品名字")
    private String prodName;
    /**
     * 发展渠道id（原推广渠道id）
     */
    @TableField(value = "channel_id")
    @Schema(description = "发展渠道id")
    private Long channelId;
    /**
     * 发展渠道类型:1-内部渠道，2-外部渠道
     */
    @TableField(value = "channel_type")
    @Schema(description = "发展渠道类型:1-内部渠道，2-外部渠道")
    private Integer channelType;
    /**
     * 发展渠道名字
     */
    @TableField(value = "channel_name")
    @Schema(description = "发展渠道名字")
    private String channelName;

    /**
     * 扣量渠道等级
     */
    @TableField(value = "deduction_channel_level")
    @Schema(description = " 扣量渠道等级")
    private Integer deductionChannelLevel;

    /**
     * 扣量渠道ID（根据扣量计算结果确定）
     */
    @TableField(value = "deduction_channel_id")
    @Schema(description = "扣量渠道ID")
    private Long deductionChannelId;

    /**
     * 扣量渠道名字
     */
    @TableField(value = "deduction_channel_name")
    @Schema(description = "扣量渠道名字")
    private String deductionChannelName;
    /**
     * 广告渠道id
     */
    @TableField(value = "ad_channel_id")
    @Schema(description = "广告渠道id")
    private Long adChannelId;
    /**
     * 广告渠道名字
     */
    @TableField(value = "ad_channel_name")
    @Schema(description = "广告渠道名字")
    private String adChannelName;
    /**
     * 广告扩展参数
     */
    @TableField(value = "ad_ext")
    @Schema(description = "广告扩展参数")
    private String adExt;
    /**
     * 订购页url
     */
    @TableField(value = "page_url")
    @Schema(description = "订购页url")
    private String pageUrl;
    /**
     * 广告代理平台
     */
    @TableField(value = "ad_agent_platform")
    @Schema(description = "广告代理平台")
    private Integer adAgentPlatform;
    /**
     * 身份证名字
     */
    @TableField(value = "id_card_name")
    @Schema(description = "身份证名字")
    private String idCardName;
    /**
     * 身份证号码
     */
    @TableField(value = "id_card")
    @Schema(description = "身份证号码")
    private String idCard;
    /**
     * sp产品编码
     */
    @TableField(value = "sp_goods_id")
    @Schema(description = "sp产品编码")
    private String spGoodsId;
    /**
     * 收货地址省份编码
     */
    @TableField(value = "post_province_code")
    @Schema(description = "收货地址省份编码")
    private String postProvinceCode;
    /**
     * 收货地址省份
     */
    @TableField(value = "post_province")
    @Schema(description = "收货地址省份")
    private String postProvince;
    /**
     * 收货城市编码
     */
    @TableField(value = "post_city_code")
    @Schema(description = "收货城市编码")
    private String postCityCode;
    /**
     * 收货城市
     */
    @TableField(value = "post_city")
    @Schema(description = "收货城市")
    private String postCity;
    /**
     * 收货区县代码
     */
    @TableField(value = "post_district_code")
    @Schema(description = "收货区县代码")
    private String postDistrictCode;
    /**
     * 收货区县
     */
    @TableField(value = "post_district")
    @Schema(description = "收货区县")
    private String postDistrict;
    /**
     * 详细收货地址
     */
    @TableField(value = "address")
    @Schema(description = "详细收货地址")
    private String address;
    /**
     * 收货联系号码
     */
    @TableField(value = "contact_phone")
    @Schema(description = "收货联系号码")
    private String contactPhone;
    /**
     * 选择的号码
     */
    @TableField(value = "phone")
    @Schema(description = "选择的号码")
    private String phone;
    /**
     * 号码归属地编码-省
     */
    @TableField(value = "province_code")
    @Schema(description = "号码归属地编码-省")
    private String provinceCode;
    /**
     * 号码归属地-省
     */
    @TableField(value = "province")
    @Schema(description = "号码归属地-省")
    private String province;
    /**
     * 号码归属地编码-市
     */
    @TableField(value = "city_code")
    @Schema(description = "号码归属地编码-市")
    private String cityCode;
    /**
     * 号码归属地-市
     */
    @TableField(value = "city")
    @Schema(description = "号码归属地-市")
    private String city;
    /**
     * 选号类型：0-随机选号，1-用户选号
     */
    @TableField(value = "select_type")
    @Schema(description = "选号类型：0-随机选号，1-用户选号")
    private Integer selectType;
    /**
     * 请求领卡时间
     */
    @TableField(value = "req_card_time")
    @Schema(description = "请求领卡时间")
    private LocalDateTime reqCardTime;
    /**
     * 订单状态：0-提交失败 1-审核中 2-领卡失败 3-领卡成功(运营商受理成功) 4-退单
     */
    @TableField(value = "order_status")
    @Schema(description = "订单状态：0-提交失败 1-审核中 2-领卡失败 3-领卡成功(运营商受理成功) 4-退单")
    private Integer orderStatus;
    /**
     * sim卡状态：0-待激活 1-激活 2-停机 3-销户
     */
    @TableField(value = "card_status")
    @Schema(description = "sim卡状态：0-待激活 1-激活 2-停机 3-销户")
    private Integer cardStatus;
    /**
     * 物流状态：0-待发货 1-发货 2-签收 3-拒收
     */
    @TableField(value = "express_status")
    @Schema(description = "物流状态：0-待发货 1-发货 2-签收 3-拒收")
    private Integer expressStatus;
    /**
     * 物流公司
     */
    @TableField(value = "express_company")
    @Schema(description = "物流公司")
    private String expressCompany;
    /**
     * 物流订单号
     */
    @TableField(value = "express_no")
    @Schema(description = "物流订单号")
    private String expressNo;
    /**
     * 发货时间
     */
    @TableField(value = "deliver_time")
    @Schema(description = "发货时间")
    private LocalDateTime deliverTime;
    /**
     * 激活时间
     */
    @TableField(value = "activate_time")
    @Schema(description = "激活时间")
    private LocalDateTime activateTime;
    /**
     * 停机时间
     */
    @TableField(value = "stop_time")
    @Schema(description = "停机时间")
    private LocalDateTime stopTime;
    /**
     * 销户时间
     */
    @TableField(value = "close_time")
    @Schema(description = "销户时间")
    private LocalDateTime closeTime;
    /**
     * 首次充值金额
     */
    @TableField(value = "first_charge_amount")
    @Schema(description = "首次充值金额")
    private Integer firstChargeAmount;
    /**
     * 首冲充值时间
     */
    @TableField(value = "first_charge_time")
    @Schema(description = "首冲充值时间")
    private LocalDateTime firstChargeTime;
    /**
     * 领卡失败原因
     */
    @TableField(value = "fail_reason")
    @Schema(description = "领卡失败原因")
    private String failReason;

    /**
     * 回调地址
     */
    @TableField(value = "callback_url")
    @Schema(description = "回调地址")
    private String callbackUrl;


    /**
     * 扩展字段
     */
    @TableField(value = "ext")
    @Schema(description = "扩展字段")
    private String ext;
    /**
     * 扩展字段2
     */
    @TableField(value = "ext2")
    @Schema(description = "扩展字段2")
    private String ext2;
    /**
     * 扩展json
     */
    @TableField(value = "ext_json")
    @Schema(description = "扩展json")
    private String extJson;
    /**
     * 备注
     */
    @TableField(value = "remark")
    @Schema(description = "备注")
    private String remark;
    /**
     * 插入数据库时间
     */
    @TableField(value = "create_time")
    @Schema(description = "插入数据库时间")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}