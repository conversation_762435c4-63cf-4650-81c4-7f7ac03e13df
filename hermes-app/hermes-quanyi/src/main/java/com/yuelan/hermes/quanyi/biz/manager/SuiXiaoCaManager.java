package com.yuelan.hermes.quanyi.biz.manager;

import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.commons.enums.BizNoPrefixEnum;
import com.yuelan.hermes.commons.enums.SpEnum;
import com.yuelan.hermes.quanyi.biz.service.*;
import com.yuelan.hermes.quanyi.biz.service.impl.EccImeiBindServiceImpl;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.enums.*;
import com.yuelan.hermes.quanyi.common.event.EccSxcActiveEvent;
import com.yuelan.hermes.quanyi.common.pojo.domain.*;
import com.yuelan.hermes.quanyi.common.pojo.properties.SuiXiaoKaBenefitProperties;
import com.yuelan.hermes.quanyi.common.util.DingTalkRobotUtil;
import com.yuelan.hermes.quanyi.common.util.LocalBizNoPlusUtils;
import com.yuelan.hermes.quanyi.common.util.OuterChannelSignUtil;
import com.yuelan.hermes.quanyi.controller.request.ChannelBenefitPackageOrderReq;
import com.yuelan.hermes.quanyi.controller.request.HuNanDxOrderStatusChangeReq;
import com.yuelan.hermes.quanyi.controller.request.SxcOrderCreateReq;
import com.yuelan.hermes.quanyi.controller.response.PackageOrderResp;
import com.yuelan.plugins.redisson.util.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> 2025/6/20
 * @since 2025/6/20
 * <p>
 * 湖南电信随销卡
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SuiXiaoCaManager {

    private final EccImeiBindServiceImpl eccImeiBindService;
    private final EccIccIdService iccIdService;
    private final NumberCardManager numberCardManager;
    private final EccProductDOService productDOService;
    private final EccNcOrderService eccNcOrderService;
    private final EccImeiService eccImeiService;
    private final EccOuterChannelDOService eccOuterChannelDOService;
    private final EccChannelDOService eccChannelDOService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final SuiXiaoKaBenefitProperties suiXiaoKaBenefitProperties;
    private final NewBenefitsOrderManager newBenefitsOrderManager;

    /**
     * 创建基础订单
     */
    public void createNcOrder(SxcOrderCreateReq req) {
        String spOrderNo = req.getOrder_id();
        String imei = req.getImei();
        if (StringUtils.isBlank(spOrderNo)) {
            log.error("创建订单失败，spOrderNo不能为空");
            return;
        }
        EccNcOrderDO dbOrder = eccNcOrderService.getBySpOrderNo(spOrderNo);
        if (Objects.nonNull(dbOrder)) {
            log.info("订单号：{} 重复接收创建订单回调已经忽略", spOrderNo);
            return;
        }

        EccNcOrderDO order = new EccNcOrderDO();
        order.setOrderNo(LocalBizNoPlusUtils.genBillNo(BizNoPrefixEnum.ECC_ORDER_NUMBER_CARD.getPrefix()));
        order.setSpOrderNo(spOrderNo);
        Long prodId = null;
        // 优先匹配 iccid 如果没有 iccid 的导入记录再去查询 imei
        EccIccIdDO iccIdDO = iccIdService.getByIccId(req.getIccid());
        if (Objects.nonNull(iccIdDO)) {
            prodId = iccIdDO.getProductId();
            order.setChannelType(iccIdDO.getChannelType());
            order.setChannelId(iccIdDO.getChannelId());
            EccChannelTypeEnum channelTypeEnum = EccChannelTypeEnum.of(iccIdDO.getChannelType());
            if (EccChannelTypeEnum.INNER == channelTypeEnum) {
                EccChannelDO channelDO = eccChannelDOService.getById(iccIdDO.getChannelId());
                if (Objects.nonNull(channelDO)) {
                    order.setChannelName(channelDO.getChannelName());
                }
            } else if (EccChannelTypeEnum.OUTER == channelTypeEnum) {
                EccOuterChannelDO channelDO = eccOuterChannelDOService.getById(iccIdDO.getChannelId());
                if (Objects.nonNull(channelDO)) {
                    order.setChannelName(channelDO.getChannelName());
                }
            }
        } else {
            EccImeiDO imeiDO = eccImeiService.getByImei(imei);
            if (Objects.nonNull(imeiDO)) {
                prodId = imeiDO.getProductId();
                order.setChannelType(imeiDO.getChannelType());
                order.setChannelId(imeiDO.getChannelId());
                EccChannelTypeEnum channelTypeEnum = EccChannelTypeEnum.of(imeiDO.getChannelType());
                if (EccChannelTypeEnum.INNER == channelTypeEnum) {
                    EccChannelDO channelDO = eccChannelDOService.getById(imeiDO.getChannelId());
                    if (Objects.nonNull(channelDO)) {
                        order.setChannelName(channelDO.getChannelName());
                    }
                } else if (EccChannelTypeEnum.OUTER == channelTypeEnum) {
                    EccOuterChannelDO channelDO = eccOuterChannelDOService.getById(imeiDO.getChannelId());
                    if (Objects.nonNull(channelDO)) {
                        order.setChannelName(channelDO.getChannelName());
                    }
                }
            }
        }
        order.setProdId(prodId);
        if (Objects.nonNull(prodId)) {
            EccProductDO matchProd = productDOService.getById(prodId);
            if (Objects.nonNull(matchProd)) {
                order.setProdId(matchProd.getProdId());
                order.setProdName(matchProd.getProdName());
                SpProdEnum spProdEnum = SpProdEnum.of(matchProd.getSpProdId());
                Integer operator = Optional.ofNullable(spProdEnum)
                        .map(SpProdEnum::getSpEnum)
                        .map(EccSpEnum::getSpEnum)
                        .map(SpEnum::getCode)
                        .orElse(null);
                order.setOperator(operator);
            }
        }
        order.setSelectType(PhoneNumSelectType.USER_SELECT.getCode());
        order.setReqCardTime(LocalDateTime.now());
        order.setOrderStatus(NcOrderStatusEnum.GET_CARD_SUCCESS.getCode());
        order.setCardStatus(SimCardStatusEnum.WAIT_ACTIVE.getCode());
        order.setExpressStatus(ExpressStatusEnum.SIGN.getCode());
        order.setDeliverTime(LocalDateTime.now());
        order.setExt2(JSON.toJSONString(req));
        numberCardManager.saveOrderDirectly(order);
    }

    /**
     * 订单状态变更
     * <p>
     * 大概顺序：
     * 2025-06-30 09:11:02.638 {"webOrder":"100320250626234212475196","isInvest":"1"}
     * 2025-06-30 14:17:16.230 {"cardNo":"330184200002033128","otherStatus":"AC001","orderStatusDesc":"激活异常","webOrder":"100320250630141442397537"}
     * 2025-06-30 14:17:40.128 {"webOrder":"100320250630141442397537","logisticsName":"EMS邮政快递","sendNo":"1398624948523"}
     * 2025-06-30 14:25:58.135 {"cardNo":"330184200002033128","otherStatus":"AC002","orderStatusDesc":"激活成功","webOrder":"100320250630141442397537"}
     * 2025-06-30 14:27:06.384 {"webOrder":"100320250630141442397537","otherStatus":"AC002","orderStatusDesc":"集团面对面激活成功"}
     */
    public void orderStatusChange(HuNanDxOrderStatusChangeReq req) {
        String webOrder = req.getWebOrder();
        String otherStatus = req.getOtherStatus();
        String cardNo = req.getCardNo();
        if (StringUtils.isBlank(webOrder)) {
            log.error("订单状态变更失败，webOrder不能为空");
            return;
        }
        if (!HuNanOtherStatus.ACTIVE_SUCCESS.getCode().equals(otherStatus)) {
            log.error("非激活状态通知已经忽略，webOrder: {}, otherStatus: {}", webOrder, otherStatus);
            return;
        }
        EccNcOrderDO dbOrder = eccNcOrderService.getBySpOrderNo(webOrder);
        if (Objects.isNull(dbOrder)) {
            log.error("订单状态变更失败，订单不存在，webOrder: {}", webOrder);
            return;
        }
        if (Objects.nonNull(cardNo) && Objects.isNull(dbOrder.getIdCard())) {
            EccNcOrderDO idCardUpdate = new EccNcOrderDO();
            idCardUpdate.setOrderId(dbOrder.getOrderId());
            idCardUpdate.setIdCard(cardNo);
            eccNcOrderService.updateById(idCardUpdate);
        }
        if (SimCardStatusEnum.ACTIVE.getCode().equals(dbOrder.getCardStatus())) {
            log.info("订单号：{} 已经激活过了，重复接收激活状态变更回调已经忽略", webOrder);
            return;
        }
        HuNanOtherStatus huNanOtherStatus = HuNanOtherStatus.of(otherStatus);


        EccNcOrderDO updateOrder = new EccNcOrderDO();
        updateOrder.setOrderId(dbOrder.getOrderId());
        updateOrder.setCardStatus(HuNanOtherStatus.ACTIVE_SUCCESS == huNanOtherStatus
                ? SimCardStatusEnum.ACTIVE.getCode()
                : SimCardStatusEnum.ACTIVE_ERROR.getCode());
        updateOrder.setActivateTime(LocalDateTime.now());
        eccNcOrderService.updateById(updateOrder);

        // 激活成功后 绑定IMEI和ICCID
        if (HuNanOtherStatus.ACTIVE_SUCCESS == huNanOtherStatus) {
            EccSxcActiveEvent activeEvent = new EccSxcActiveEvent();
            activeEvent.setActiveOrder(dbOrder);
            applicationEventPublisher.publishEvent(activeEvent);
        }
    }


    public void dispatchBenefit(EccNcOrderDO activeOrder) {
        log.info("随销卡激活后处理订单: {}，准备发放权益", activeOrder.getOrderId());
        Long timestamp = System.currentTimeMillis() / 1000;
        ChannelBenefitPackageOrderReq req = new ChannelBenefitPackageOrderReq();
        req.setApiKey(suiXiaoKaBenefitProperties.getApiKey());
        req.setChannelOrderNo(activeOrder.getOrderNo());
        req.setMobile(activeOrder.getPhone());
        req.setTimestamp(timestamp);
        req.setPackageCode(suiXiaoKaBenefitProperties.getBenefitPackageCode());

        String sign = OuterChannelSignUtil.sign(suiXiaoKaBenefitProperties.getApiSecret(), req);
        req.setSign(sign);
        boolean dispatchSuccess = false;
        try {
            PackageOrderResp benefitOrder = newBenefitsOrderManager.createBenefitOrder(req);
            if (benefitOrder.getOrderNo() != null) {
                dispatchSuccess = true;
            }
        } catch (Exception e) {
            log.error("随销卡激活后处理订单失{}错误: {}", activeOrder.getOrderId(), e.getMessage());
        }
        if (!dispatchSuccess) {
            DingTalkRobotUtil.sendMsgToDingTalk(DingTalkRobotTypeEnum.DING_TALK_ROBOT_ECC, "随销卡激活后权益订单创建失败: " + activeOrder.getOrderId());
        } else {
            String cacheKey = RedisKeys.getSxkBenefit(activeOrder.getOrderId());
            RedisUtils.deleteObject(cacheKey);
        }
    }
}
