package com.yuelan.hermes.quanyi.biz.manager;

import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.commons.enums.BizNoPrefixEnum;
import com.yuelan.hermes.commons.enums.SpEnum;
import com.yuelan.hermes.quanyi.biz.service.*;
import com.yuelan.hermes.quanyi.biz.service.impl.EccImeiBindServiceImpl;
import com.yuelan.hermes.quanyi.common.enums.*;
import com.yuelan.hermes.quanyi.common.event.EccSxcActiveEvent;
import com.yuelan.hermes.quanyi.common.pojo.domain.*;
import com.yuelan.hermes.quanyi.common.util.LocalBizNoPlusUtils;
import com.yuelan.hermes.quanyi.controller.request.HuNanDxOrderStatusChangeReq;
import com.yuelan.hermes.quanyi.controller.request.SxcOrderCreateReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2025/6/20
 * @since 2025/6/20
 * <p>
 * 湖南电信随销卡
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SuiXiaoCaManager {

    private final EccImeiBindServiceImpl eccImeiBindService;
    private final NumberCardManager numberCardManager;
    private final EccProductDOService productDOService;
    private final EccNcOrderService eccNcOrderService;
    private final EccImeiService eccImeiService;
    private final EccOuterChannelDOService eccOuterChannelDOService;
    private final EccChannelDOService eccChannelDOService;
    private final ApplicationEventPublisher applicationEventPublisher;

    /**
     * 创建基础订单
     */
    public void createNcOrder(SxcOrderCreateReq req) {
        String spOrderNo = req.getOrder_id();
        String imei = req.getImei();
        if (StringUtils.isBlank(spOrderNo)) {
            log.error("创建订单失败，spOrderNo不能为空");
            return;
        }
        EccNcOrderDO dbOrder = eccNcOrderService.getBySpOrderNo(spOrderNo);
        if (Objects.nonNull(dbOrder)) {
            log.info("订单号：{} 重复接收创建订单回调已经忽略", spOrderNo);
            return;
        }

        EccNcOrderDO order = new EccNcOrderDO();
        order.setOrderNo(LocalBizNoPlusUtils.genBillNo(BizNoPrefixEnum.ECC_ORDER_NUMBER_CARD.getPrefix()));
        order.setSpOrderNo(spOrderNo);
        order.setOperator(SpEnum.TELECOM.getCode());
        List<EccProductDO> productList = productDOService.getBySpProdId(SpProdEnum.HN_DX_SXC.getProdId());
        if (!productList.isEmpty()) {
            EccProductDO productDO = productList.get(0);
            order.setProdId(productDO.getProdId());
            order.setProdName(productDO.getProdName());
        }
        EccImeiDO imeiDO = eccImeiService.getByImei(imei);
        if (Objects.nonNull(imeiDO)) {
            order.setChannelType(imeiDO.getChannelType());
            order.setChannelId(imeiDO.getChannelId());
            EccChannelTypeEnum channelTypeEnum = EccChannelTypeEnum.of(imeiDO.getChannelType());
            if (EccChannelTypeEnum.INNER == channelTypeEnum) {
                EccChannelDO channelDO = eccChannelDOService.getById(imeiDO.getChannelId());
                if (Objects.nonNull(channelDO)) {
                    order.setChannelName(channelDO.getChannelName());
                }
            } else if (EccChannelTypeEnum.OUTER == channelTypeEnum) {
                EccOuterChannelDO channelDO = eccOuterChannelDOService.getById(imeiDO.getChannelId());
                if (Objects.nonNull(channelDO)) {
                    order.setChannelName(channelDO.getChannelName());
                }
            }
        }
        order.setSelectType(PhoneNumSelectType.USER_SELECT.getCode());
        order.setReqCardTime(LocalDateTime.now());
        order.setOrderStatus(NcOrderStatusEnum.GET_CARD_SUCCESS.getCode());
        order.setCardStatus(SimCardStatusEnum.WAIT_ACTIVE.getCode());
        order.setExpressStatus(ExpressStatusEnum.SIGN.getCode());
        order.setDeliverTime(LocalDateTime.now());
        order.setExt2(JSON.toJSONString(req));
        numberCardManager.saveOrderDirectly(order);
    }

    /**
     * 订单状态变更
     * <p>
     * 大概顺序：
     * 2025-06-30 09:11:02.638 {"webOrder":"100320250626234212475196","isInvest":"1"}
     * 2025-06-30 14:17:16.230 {"cardNo":"330184200002033128","otherStatus":"AC001","orderStatusDesc":"激活异常","webOrder":"100320250630141442397537"}
     * 2025-06-30 14:17:40.128 {"webOrder":"100320250630141442397537","logisticsName":"EMS邮政快递","sendNo":"1398624948523"}
     * 2025-06-30 14:25:58.135 {"cardNo":"330184200002033128","otherStatus":"AC002","orderStatusDesc":"激活成功","webOrder":"100320250630141442397537"}
     * 2025-06-30 14:27:06.384 {"webOrder":"100320250630141442397537","otherStatus":"AC002","orderStatusDesc":"集团面对面激活成功"}
     */
    public void orderStatusChange(HuNanDxOrderStatusChangeReq req) {
        String webOrder = req.getWebOrder();
        String otherStatus = req.getOtherStatus();
        String cardNo = req.getCardNo();
        if (StringUtils.isBlank(webOrder)) {
            log.error("订单状态变更失败，webOrder不能为空");
            return;
        }
        if (!HuNanOtherStatus.ACTIVE_SUCCESS.getCode().equals(otherStatus)) {
            log.error("非激活状态通知已经忽略，webOrder: {}, otherStatus: {}", webOrder, otherStatus);
            return;
        }
        EccNcOrderDO dbOrder = eccNcOrderService.getBySpOrderNo(webOrder);
        if (Objects.isNull(dbOrder)) {
            log.error("订单状态变更失败，订单不存在，webOrder: {}", webOrder);
            return;
        }
        if (Objects.nonNull(cardNo) && Objects.isNull(dbOrder.getIdCard())) {
            EccNcOrderDO idCardUpdate = new EccNcOrderDO();
            idCardUpdate.setOrderId(dbOrder.getOrderId());
            idCardUpdate.setIdCard(cardNo);
            eccNcOrderService.updateById(idCardUpdate);
        }
        if (SimCardStatusEnum.ACTIVE.getCode().equals(dbOrder.getCardStatus())) {
            log.info("订单号：{} 已经激活过了，重复接收激活状态变更回调已经忽略", webOrder);
            return;
        }
        HuNanOtherStatus huNanOtherStatus = HuNanOtherStatus.of(otherStatus);


        EccNcOrderDO updateOrder = new EccNcOrderDO();
        updateOrder.setOrderId(dbOrder.getOrderId());
        updateOrder.setCardStatus(HuNanOtherStatus.ACTIVE_SUCCESS == huNanOtherStatus
                ? SimCardStatusEnum.ACTIVE.getCode()
                : SimCardStatusEnum.ACTIVE_ERROR.getCode());
        updateOrder.setActivateTime(LocalDateTime.now());
        eccNcOrderService.updateById(updateOrder);

        // 激活成功后 绑定IMEI和ICCID
        if (HuNanOtherStatus.ACTIVE_SUCCESS == huNanOtherStatus) {
            EccSxcActiveEvent activeEvent = new EccSxcActiveEvent();
            activeEvent.setActiveOrder(dbOrder);
            applicationEventPublisher.publishEvent(activeEvent);
        }

    }
}
