package com.yuelan.hermes.quanyi.biz.handler.impl;

import com.alibaba.fastjson2.JSONArray;
import com.yuelan.hermes.commons.enums.BizNoPrefixEnum;
import com.yuelan.hermes.commons.enums.SendTypeEnum;
import com.yuelan.hermes.quanyi.biz.manager.BenefitDispatchManager;
import com.yuelan.hermes.quanyi.biz.service.BenefitItemOrderService;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderExtensionService;
import com.yuelan.hermes.quanyi.biz.service.BenefitsPackageOrderService;
import com.yuelan.hermes.quanyi.biz.service.BenefitsPackageService;
import com.yuelan.hermes.quanyi.common.enums.*;
import com.yuelan.hermes.quanyi.common.interfaces.BenefitOrderStrategy;
import com.yuelan.hermes.quanyi.common.pojo.domain.*;
import com.yuelan.hermes.quanyi.common.util.LocalBizNoPlusUtils;
import com.yuelan.hermes.quanyi.controller.request.ChannelBenefitBasicOrderReq;
import com.yuelan.hermes.quanyi.controller.request.ChannelBenefitPackageOrderReq;
import com.yuelan.hermes.quanyi.mq.producer.ProducerHelper;
import com.yuelan.result.enums.YesOrNoEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2025/4/21
 * @since 2025/4/21
 * <p>
 * 权益包订单策略
 */
@Component
@RequiredArgsConstructor
public class PackageOrderStrategy implements BenefitOrderStrategy {

    private final BenefitsPackageService benefitsPackageService;
    private final BenefitItemOrderService benefitItemOrderService;
    private final BenefitOrderExtensionService benefitOrderExtensionService;
    private final BenefitsPackageOrderService benefitsPackageOrderService;
    private final ProducerHelper producerHelper;
    private final BenefitDispatchManager benefitDispatchManager;

    @Override
    public Class<? extends ChannelBenefitBasicOrderReq> getSupportedRequestType() {
        return ChannelBenefitPackageOrderReq.class;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String processOrder(ApiChannel apiChannel, ChannelBenefitBasicOrderReq orderReq) {
        ChannelBenefitPackageOrderReq req = (ChannelBenefitPackageOrderReq) orderReq;
        // 权益包信息
        BenefitsPackageDO packageDO = getPackageAndItems(req.getPackageCode());
        // 让这个手机号码的上一笔订单的权益包失效
        invalidateLastOrder(req.getMobile(), packageDO.getPackageId());
        // 扩展信息保存
        BenefitOrderExtensionDO orderExtension = initializeExtension(apiChannel, req);
        // 权益包订单
        BenefitsPackageOrderDO orderDO = initializeOrder(orderExtension, packageDO);
        // 即时 发放的权益
        List<BenefitItemDO> instantBenefits = filterBenefitsByDispatchTiming(packageDO.getBenefits(), BenefitDispatchTimingEnum.INSTANT);
        // 即时 发放的权益的订单保存
        List<BenefitItemOrderDO> benefitItemOrders = buildBenefitItemOrderList(orderExtension, orderDO, instantBenefits);
        if (!benefitItemOrders.isEmpty()) {
            benefitItemOrderService.saveBatch(benefitItemOrders);
        }
        producerHelper.packageBenefitsDistributeMsg(orderExtension.getExtensionId().toString(), OrderEvent.BENEFIT_ORDER_DISTRIBUTE);
        return orderExtension.getOrderNo();
    }

    /**
     * 过滤权益按发放时机
     *
     * @param benefits 权益列表
     * @return 即时发放的权益列表
     */
    private List<BenefitItemDO> filterBenefitsByDispatchTiming(List<BenefitItemDO> benefits, BenefitDispatchTimingEnum dispatchTimingEnum) {
        return benefits.stream()
                .filter(item -> Objects.equals(item.getDispatchTiming(), dispatchTimingEnum.getCode()))
                .collect(Collectors.toList());
    }

    /**
     * 让上一笔订单失效
     */
    private void invalidateLastOrder(String phone, Long packageId) {
        // 让上一笔订单失效
        BenefitsPackageOrderDO lastPackageOrder = benefitsPackageOrderService.findLastPackageOrder(phone, packageId);
        if (lastPackageOrder != null) {
            BenefitsPackageOrderDO updateOrder = new BenefitsPackageOrderDO();
            updateOrder.setPackageOrderId(lastPackageOrder.getPackageOrderId());
            updateOrder.setIsValid(YesOrNoEnum.NO.getCode());
            benefitsPackageOrderService.updateById(updateOrder);
        }
    }

    /**
     * 初始化权益包订单扩展信息 保存到数据库
     *
     * @param apiChannel 渠道
     * @param req        请求参数
     * @return 权益包订单扩展信息
     */
    private BenefitOrderExtensionDO initializeExtension(ApiChannel apiChannel, ChannelBenefitBasicOrderReq req) {
        BenefitOrderExtensionDO extension = new BenefitOrderExtensionDO();
        extension.setChannelOrderNo(req.getChannelOrderNo());
        extension.setOrderSource(BenefitOrderSource.PRODUCT_ORDER.getCode());
        extension.setOrderNo(LocalBizNoPlusUtils.genBillNo(BizNoPrefixEnum.BENEFIT_PACKAGE_ORDER.getPrefix()));
        extension.setMobile(req.getMobile());
        extension.setChannelId(apiChannel.getApiChannelId());
        extension.setChannelName(apiChannel.getChannelName());
        extension.setPayChannelId(null);
        extension.setPayChannelName(null);
        extension.setPayPkgId(null);
        extension.setPayPkgName(null);
        benefitOrderExtensionService.save(extension);
        return extension;
    }

    /**
     * 初始化权益包订单
     *
     * @param orderExtension 订单扩展
     * @param packageDO      权益包以及权益项
     * @return 订单对象
     */
    private BenefitsPackageOrderDO initializeOrder(BenefitOrderExtensionDO orderExtension, BenefitsPackageDO packageDO) {
        BenefitsPackageOrderDO order = new BenefitsPackageOrderDO();
        order.setExtensionId(orderExtension.getExtensionId());
        order.setPackageId(packageDO.getPackageId());
        order.setPackageName(packageDO.getPackageName());
        order.setPackageCode(packageDO.getPackageCode());
        order.setSellingPrice(packageDO.getSellingPrice());
        order.setMobile(orderExtension.getMobile());
        order.setSellingPrice(packageDO.getSellingPrice());
        List<Long> benefitsIds = packageDO.getBenefits().stream().map(BenefitItemDO::getBenefitItemId).collect(Collectors.toList());
        // 选发权益
        List<BenefitItemDO> userSelectBenefits = filterBenefitsByDispatchTiming(packageDO.getBenefits(), BenefitDispatchTimingEnum.USER_SELECT);
        order.setOptionalIds(JSONArray.toJSONString(userSelectBenefits.stream().map(BenefitItemDO::getBenefitItemId).collect(Collectors.toList())));
        order.setOptionalCount(benefitsIds.size());
        order.setOptionalDispatchCount(0);
        order.setMaxSelectable(packageDO.getRedemptionLimit());
        // 即时发放权益
        List<BenefitItemDO> instantBenefits = filterBenefitsByDispatchTiming(packageDO.getBenefits(), BenefitDispatchTimingEnum.INSTANT);
        order.setInstantIds(JSONArray.toJSONString(instantBenefits.stream().map(BenefitItemDO::getBenefitItemId).collect(Collectors.toList())));
        order.setInstantCount(instantBenefits.size());
        order.setInstantDispatchCount(0);
        order.setDispatchErrorCount(0);
        // 截止n+1天0点
        LocalDateTime redemptionDeadline = LocalDate.now()
                .plusDays(packageDO.getRedemptionPeriod()).atTime(23, 59, 59);
        order.setRedemptionDeadline(redemptionDeadline);
        order.setIsRefunded(YesOrNoEnum.NO.getCode());
        order.setIsValid(YesOrNoEnum.YES.getCode());
        benefitsPackageOrderService.save(order);
        return order;
    }

    private BenefitsPackageDO getPackageAndItems(String packageCode) {
        return benefitsPackageService.getAvailablePackageAndItems(packageCode);
    }

    /**
     * @param orderExtension            扩展信息
     * @param packageOrderDO            包订单
     * @param benefitsAndDispatchTiming 权益必须包含dispatchTiming
     * @return 权益包订单列表
     */
    public List<BenefitItemOrderDO> buildBenefitItemOrderList(BenefitOrderExtensionDO orderExtension, BenefitsPackageOrderDO packageOrderDO, List<BenefitItemDO> benefitsAndDispatchTiming) {
        List<BenefitItemOrderDO> orderList = new ArrayList<>();
        for (BenefitItemDO benefit : benefitsAndDispatchTiming) {
            BenefitItemOrderDO orderDO = new BenefitItemOrderDO();
            orderDO.setExtensionId(packageOrderDO.getExtensionId());
            orderDO.setOrderType(SendTypeEnum.ORIGINAL_ORDER.getType());
            orderDO.setOriginalItemOrderId(null);
            orderDO.setSupplierRequestOrder(LocalBizNoPlusUtils.genBillNo(BizNoPrefixEnum.BENEFIT_ITEM_ORDER.getPrefix()));
            orderDO.setBenefitItemId(benefit.getBenefitItemId());
            orderDO.setBenefitItemName(benefit.getBenefitName());
            orderDO.setDispatchTiming(benefit.getDispatchTiming());
            // 没有配置在权益配置内，供应商下发时候动态设置的更新的
            orderDO.setDeliveryType(null);
            orderDO.setMobile(packageOrderDO.getMobile());
            orderDO.setSupplierId(benefit.getSupplierId());
            orderDO.setSupplierName(benefit.getSupplierName());
            orderDO.setSupplierGoodsParam(benefit.getSupplierGoodsParam());
            // 供应商下单返回时候设置
            orderDO.setSupplierOrderNo(null);
            // 以后兑换码配置在外面后台时候设置
            orderDO.setRedeemCodeId(null);
            // 根据redeemCodeId 读取数据库  或者供应商下单返回配置
            orderDO.setRedeemCode(null);
            orderDO.setRedeemCodePwd(null);
            orderDO.setRedeemCodeExpireTime(null);
            orderDO.setOrderStatus(BenefitItemOrderStatus.DISPATCHING.getCode());
            orderDO.setProcessState(BenefitItemOrderProcessStateEnum.WAIT_REQUEST.getCode());

            orderDO.setOrderExtension(orderExtension);
            orderList.add(orderDO);
        }
        return orderList;
    }
}