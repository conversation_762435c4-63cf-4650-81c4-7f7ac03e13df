package com.yuelan.hermes.quanyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 2024/6/12 下午2:12
 */
@AllArgsConstructor
@Getter
public enum SysRoleEnum {

    ADMIN(1, "admin"),
    ECC_OUTER(2, "ecc-outer");

    private final Integer code;
    private final String name;

    public static SysRoleEnum getRoleByCode(Integer code) {
        for (SysRoleEnum value : SysRoleEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
