package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccNcOrderDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/12/28
 * @since 2024/12/28
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class EccNcOrderListReq extends PageRequest {

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private Long orderId;

    /**
     * 我方订单号
     */
    @Schema(description = "我方订单号")
    private String orderNo;

    /**
     * 渠道订单号
     */
    @Schema(description = "渠道订单号")
    private String channelOrderNo;

    /**
     * 服务提供商订单号
     */
    @Schema(description = "服务提供商订单号")
    private String spOrderNo;

    /**
     * 原始订单号（补发类型订单特有）
     */
    @Schema(description = "原始订单号（补发类型订单特有）")
    private String originalOrderNo;

    /**
     * 运营商
     */
    @Schema(description = "运营商")
    private Integer operator;

    /**
     * 我方号卡产品id
     */
    @Schema(description = "我方号卡产品id")
    private Long prodId;


    /**
     * 推广渠道id
     */
    @Schema(description = "推广渠道id")
    private Long channelId;

    /**
     * 推广渠道类型:1-内部渠道，2-外部渠道
     */
    @Schema(description = "推广渠道类型:1-内部渠道，2-外部渠道")
    private Integer channelType;

    /**
     * 推广渠道名字
     */
    @Schema(description = "推广渠道名字")
    private String channelName;

    /**
     * 广告渠道id
     */
    @Schema(description = "广告渠道id")
    private Long adChannelId;

    /**
     * 广告渠道名字
     */
    @Schema(description = "广告渠道名字")
    private String adChannelName;

    /**
     * 广告代理平台
     */
    @Schema(description = "广告代理平台")
    private Integer adAgentPlatform;

    /**
     * 身份证名字
     */
    @Schema(description = "身份证名字")
    private String idCardName;

    /**
     * 身份证号码
     */
    @Schema(description = "身份证号码")
    private String idCard;

    /**
     * sp产品编码
     */
    @Schema(description = "sp产品编码")
    private String spGoodsId;

    /**
     * 收货地址省份编码
     */
    @Schema(description = "收货地址省份编码")
    private String postProvinceCode;

    /**
     * 收货地址省份
     */
    @Schema(description = "收货地址省份")
    private String postProvince;

    /**
     * 收货城市编码
     */
    @Schema(description = "收货城市编码")
    private String postCityCode;

    /**
     * 收货城市
     */
    @Schema(description = "收货城市")
    private String postCity;

    /**
     * 收货区县代码
     */
    @Schema(description = "收货区县代码")
    private String postDistrictCode;

    /**
     * 收货区县
     */
    @Schema(description = "收货区县")
    private String postDistrict;

    /**
     * 详细收货地址
     */
    @Schema(description = "详细收货地址")
    private String address;

    /**
     * 收货联系号码
     */
    @Schema(description = "收货联系号码")
    private String contactPhone;

    /**
     * 选择的号码
     */
    @Schema(description = "选择的号码")
    private String phone;

    /**
     * 号码归属地编码-省
     */
    @Schema(description = "号码归属地编码-省")
    private String provinceCode;

    /**
     * 号码归属地-省
     */
    @Schema(description = "号码归属地-省")
    private String province;

    /**
     * 号码归属地编码-市
     */
    @Schema(description = "号码归属地编码-市")
    private String cityCode;

    /**
     * 号码归属地-市
     */
    @Schema(description = "号码归属地-市")
    private String city;

    /**
     * 选号类型：0-随机选号，1-用户选号
     */
    @Schema(description = "选号类型：0-随机选号，1-用户选号")
    private Integer selectType;

    /**
     * 请求领卡时间
     */
    @Schema(description = "领卡时间-开始")
    private LocalDateTime reqCardTimeStart;

    /**
     * 请求领卡时间
     */
    @Schema(description = "领卡时间-结束")
    private LocalDateTime reqCardTimeEnd;

    /**
     * 订单状态：0-提交失败 1-审核中 2-领卡失败 3-领卡成功(运营商受理成功) 4-退单
     */
    @Schema(description = "订单状态：0-提交失败 1-审核中 2-领卡失败 3-领卡成功(运营商受理成功) 4-退单")
    private Integer orderStatus;

    /**
     * sim卡状态：0-待激活 1-激活 2-停机 3-销户
     */
    @Schema(description = "sim卡状态：0-待激活 1-激活 2-停机 3-销户")
    private Integer cardStatus;

    /**
     * 物流状态：0-待发货 1-发货 2-签收 3-拒收
     */
    @Schema(description = "物流状态：0-待发货 1-发货 2-签收 3-拒收")
    private Integer expressStatus;

    /**
     * 物流公司
     */
    @Schema(description = "物流公司")
    private String expressCompany;

    /**
     * 物流订单号
     */
    @Schema(description = "物流订单号")
    private String expressNo;

    /**
     * 发货时间
     */
    @Schema(description = "发货时间-开始")
    private LocalDateTime deliverTimeStart;

    /**
     * 发货时间
     */
    @Schema(description = "发货时间-结束")
    private LocalDateTime deliverTimeEnd;

    /**
     * 激活时间
     */
    @Schema(description = "激活时间-开始")
    private LocalDateTime activateTimeStart;

    /**
     * 激活时间
     */
    @Schema(description = "激活时间-结束")
    private LocalDateTime activateTimeEnd;


    /**
     * 首冲状态
     */
    @Schema(description = "首冲状态 0-未首冲 1-已首冲")
    private Integer firstChargeStatus;


    @Schema(description = "首充金额查询范围，格式为二维数组[[0，10],[10,20]....[90,100],[100,-1]]")
    private List<String> amountLimit;
    /**
     * 首冲充值时间
     */
    @Schema(description = "首冲充值时间-开始")
    private LocalDateTime firstChargeTimeStart;

    /**
     * 首冲充值时间
     */
    @Schema(description = "首冲充值时间-结束")
    private LocalDateTime firstChargeTimeEnd;

    /**
     * 领卡失败原因
     */
    @Schema(description = "领卡失败原因")
    private String failReason;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;


    @Schema(description = "扣量类型 -1-不扣量 0-平台 1-一级扣量 2-二级扣量")
    private Integer deductionType;


    public LambdaQueryWrapper<EccNcOrderDO> buildQueryWrapper(List<Long> outChannelLimit, List<Long> innerChannelLimit, boolean isAsc) {

        List<Long> thisOutChannelLimit = new ArrayList<>();
        if (outChannelLimit !=null) {
            if (outChannelLimit.isEmpty()) {
                thisOutChannelLimit.add(-1L);
            }else {
                thisOutChannelLimit.addAll(outChannelLimit);
            }
        }
        List<Long> thisInnerChannelLimit = new ArrayList<>();
        if (innerChannelLimit !=null) {
            if (innerChannelLimit.isEmpty()) {
                thisInnerChannelLimit.add(-1L);
            }else {
                thisInnerChannelLimit.addAll(innerChannelLimit);
            }
        }
        List<Long> allChannelLimit = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(thisOutChannelLimit)) {
            allChannelLimit.addAll(thisOutChannelLimit);
        }
        if (CollectionUtil.isNotEmpty(thisInnerChannelLimit)) {
            allChannelLimit.addAll(thisInnerChannelLimit);
        }




        return Wrappers.lambdaQuery(EccNcOrderDO.class)
                .eq(orderId != null, EccNcOrderDO::getOrderId, orderId)
                .eq(orderNo != null, EccNcOrderDO::getOrderNo, orderNo)
                .eq(channelOrderNo != null, EccNcOrderDO::getChannelOrderNo, channelOrderNo)
                .eq(spOrderNo != null, EccNcOrderDO::getSpOrderNo, spOrderNo)
                .eq(originalOrderNo != null, EccNcOrderDO::getOriginalOrderNo, originalOrderNo)
                .eq(operator != null, EccNcOrderDO::getOperator, operator)
                .eq(prodId != null, EccNcOrderDO::getProdId, prodId)
                .eq(channelId != null, EccNcOrderDO::getChannelId, channelId)
                .eq(channelType != null, EccNcOrderDO::getChannelType, channelType)
                .like(channelName != null, EccNcOrderDO::getChannelName, channelName)
                .eq(adChannelId != null, EccNcOrderDO::getAdChannelId, adChannelId)
                .like(adChannelName != null, EccNcOrderDO::getAdChannelName, adChannelName)
                .eq(adAgentPlatform != null, EccNcOrderDO::getAdAgentPlatform, adAgentPlatform)
                .like(idCardName != null, EccNcOrderDO::getIdCardName, idCardName)
                .eq(idCard != null, EccNcOrderDO::getIdCard, idCard)
                .eq(spGoodsId != null, EccNcOrderDO::getSpGoodsId, spGoodsId)
                .eq(postProvinceCode != null, EccNcOrderDO::getPostProvinceCode, postProvinceCode)
                .like(postProvince != null, EccNcOrderDO::getPostProvince, postProvince)
                .eq(postCityCode != null, EccNcOrderDO::getPostCityCode, postCityCode)
                .like(postCity != null, EccNcOrderDO::getPostCity, postCity)
                .eq(postDistrictCode != null, EccNcOrderDO::getPostDistrictCode, postDistrictCode)
                .like(postDistrict != null, EccNcOrderDO::getPostDistrict, postDistrict)
                .like(address != null, EccNcOrderDO::getAddress, address)
                .eq(contactPhone != null, EccNcOrderDO::getContactPhone, contactPhone)
                .eq(phone != null, EccNcOrderDO::getPhone, phone)
                .eq(provinceCode != null, EccNcOrderDO::getProvinceCode, provinceCode)
                .like(province != null, EccNcOrderDO::getProvince, province)
                .eq(cityCode != null, EccNcOrderDO::getCityCode, cityCode)
                .like(city != null, EccNcOrderDO::getCity, city)
                .eq(selectType != null, EccNcOrderDO::getSelectType, selectType)
                .ge(reqCardTimeStart != null, EccNcOrderDO::getReqCardTime, reqCardTimeStart)
                .lt(reqCardTimeEnd != null, EccNcOrderDO::getReqCardTime, reqCardTimeEnd)
                .eq(orderStatus != null, EccNcOrderDO::getOrderStatus, orderStatus)
                .eq(cardStatus != null, EccNcOrderDO::getCardStatus, cardStatus)
                .eq(expressStatus != null, EccNcOrderDO::getExpressStatus, expressStatus)
                .like(expressCompany != null, EccNcOrderDO::getExpressCompany, expressCompany)
                .eq(expressNo != null, EccNcOrderDO::getExpressNo, expressNo)
                .ge(deliverTimeStart != null, EccNcOrderDO::getDeliverTime, deliverTimeStart)
                .lt(deliverTimeEnd != null, EccNcOrderDO::getDeliverTime, deliverTimeEnd)
                .ge(activateTimeStart != null, EccNcOrderDO::getActivateTime, activateTimeStart)
                .lt(activateTimeEnd != null, EccNcOrderDO::getActivateTime, activateTimeEnd)
                .ge(firstChargeTimeStart != null, EccNcOrderDO::getFirstChargeTime, firstChargeTimeStart)
                .lt(firstChargeTimeEnd != null, EccNcOrderDO::getFirstChargeTime, firstChargeTimeEnd)
                .in(!allChannelLimit.isEmpty(), EccNcOrderDO::getChannelId, allChannelLimit)
                .like(failReason != null && !failReason.isEmpty(), EccNcOrderDO::getFailReason, failReason)
                .like(remark != null && !remark.isEmpty(), EccNcOrderDO::getRemark, remark)
                .isNotNull(Objects.equals(firstChargeStatus, 1), EccNcOrderDO::getFirstChargeAmount)
                .isNull(Objects.equals(firstChargeStatus, 0), EccNcOrderDO::getFirstChargeAmount)
                .and()
                .in(!allChannelLimit.isEmpty(), EccNcOrderDO::getDeductionChannelId, allChannelLimit)
                .and(amountLimit != null && !amountLimit.isEmpty(), (item) -> {
                    for (String scope : amountLimit) {
                        String[] integers = scope.split(",");
                        item.or((query) -> {
                            query.ge(EccNcOrderDO::getFirstChargeAmount, Integer.parseInt(integers[0]) * 100);
                            if (!Objects.equals(integers[1], "-1")) {
                                query.lt(EccNcOrderDO::getFirstChargeAmount, Integer.parseInt(integers[1]) * 100);
                            }
                        });
                    }
                })
                .orderBy(true, isAsc, EccNcOrderDO::getOrderId);
    }
}