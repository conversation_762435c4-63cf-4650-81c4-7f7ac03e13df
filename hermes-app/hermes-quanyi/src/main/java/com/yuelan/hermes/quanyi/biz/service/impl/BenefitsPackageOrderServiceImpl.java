package com.yuelan.hermes.quanyi.biz.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.quanyi.biz.handler.FileExportTaskHandler;
import com.yuelan.hermes.quanyi.biz.manager.NewBenefitsOrderManager;
import com.yuelan.hermes.quanyi.biz.service.BenefitsPackageOrderService;
import com.yuelan.hermes.quanyi.biz.service.FileExportTaskDOService;
import com.yuelan.hermes.quanyi.common.enums.FileExportTaskCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.FileExportTaskDO;
import com.yuelan.hermes.quanyi.common.util.MpPageUtil;
import com.yuelan.hermes.quanyi.controller.request.BenefitsPackageDevOrderReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitsPackageOrderRefundReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitsPackageOrderReq;
import com.yuelan.hermes.quanyi.controller.request.ChannelBenefitPackageOrderReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitsPackageOrderResp;
import com.yuelan.hermes.quanyi.controller.response.FileExportTaskCreateResp;
import com.yuelan.hermes.quanyi.mapper.BenefitsPackageOrderMapper;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2025/4/21
 * @since 2025/4/21
 */
@Slf4j
@Service
@AllArgsConstructor
public class BenefitsPackageOrderServiceImpl extends ServiceImpl<BenefitsPackageOrderMapper, BenefitsPackageOrderDO> implements BenefitsPackageOrderService, FileExportTaskHandler {

    @Resource
    private FileExportTaskDOService fileExportTaskDOService;


    @Override
    public BenefitsPackageOrderDO findLastPackageOrder(String phone, Long packageId) {
        return lambdaQuery()
                .eq(BenefitsPackageOrderDO::getMobile, phone)
                .eq(BenefitsPackageOrderDO::getPackageId, packageId)
                .orderByDesc(BenefitsPackageOrderDO::getPackageOrderId)
                .last("limit 1")
                .one();
    }

    @Override
    public List<BenefitsPackageOrderDO> selectUserPackageOrder(String phone) {
        return lambdaQuery()
                .eq(BenefitsPackageOrderDO::getMobile, phone)
                .orderByDesc(BenefitsPackageOrderDO::getCreateTime)
                .list();
    }

    @Override
    public BenefitsPackageOrderDO getByIdAndPhone(String phone, Long packageOrderId) {
        return lambdaQuery()
                .eq(BenefitsPackageOrderDO::getPackageOrderId, packageOrderId)
                .eq(BenefitsPackageOrderDO::getMobile, phone)
                .one();
    }

    @Override
    public PageData<BenefitsPackageOrderResp> pageList(BenefitsPackageOrderReq req) {
        IPage<BenefitsPackageOrderDO> page = MpPageUtil.convertPageRequest(req);
        page = baseMapper.selectJoinPage(page, BenefitsPackageOrderDO.class, req.buildQueryWrapper(false));
        List<BenefitsPackageOrderResp> list = page.getRecords().stream()
                .map(BenefitsPackageOrderResp::buildResp)
                .collect(Collectors.toList());
        return PageData.create(list, page.getTotal(), req.getPage(), req.getSize());
    }

    @Override
    public BenefitsPackageOrderDO getByExtensionId(Long extensionId) {
        return lambdaQuery()
                .eq(BenefitsPackageOrderDO::getExtensionId, extensionId)
                .one();
    }

    @Override
    public boolean updateDispatchInfo(Long packageOrderId, int dispatchErrorCountIncrement, int instantDispatchCountIncrement, int optionalDispatchCountIncrement) {
        return baseMapper.updateDispatchInfoById(packageOrderId, dispatchErrorCountIncrement, instantDispatchCountIncrement, optionalDispatchCountIncrement) > 0;
    }

    @Override
    public void refund(BenefitsPackageOrderRefundReq req) {
        if (req.getIds().size() > 200) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "批量退款数量不能超过200");
        }
        if (req.getIds().isEmpty()) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "退款数量不能为0");
        }
        int updateCount = baseMapper.updateRefundStatusByIds(req.getIds());
        if (updateCount != req.getIds().size()) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "成功退款" + updateCount + "条，失败" + (req.getIds().size() - updateCount) + "条，请检查订单状态是否正确");
        }
    }

    @Override
    public FileExportTaskCreateResp export(BenefitsPackageOrderReq req) {
        if (req.getCreateTimeEnd() == null || req.getCreateTimeEnd().isAfter(LocalDateTime.now())) {
            // 防止数据一直在变化
            req.setCreateTimeEnd(LocalDateTime.now());
        }
        MPJLambdaWrapper<BenefitsPackageOrderDO> wrapper = req.buildQueryWrapper(true);
        Long count = baseMapper.selectJoinCount(wrapper);
        if (count > 500000) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "导出数据量过大，请缩小查询范围");
        }

        FileExportTaskDO taskDO = fileExportTaskDOService.createDefaultTask(JSONObject.toJSONString(req), getTaskCode(), "xlsx");
        return new FileExportTaskCreateResp(taskDO.getTaskId(), taskDO.getFileName());

    }


    @Override
    public FileExportTaskCodeEnum getTaskCode() {
        return FileExportTaskCodeEnum.BENEFIT_PACKAGE_ORDER_EXPORT;
    }

    @Override
    public void generateExportFile(FileExportTaskDO taskDO) {
        String evalParam = taskDO.getEvalParam();
        BenefitsPackageOrderReq req = JSONObject.parseObject(evalParam, BenefitsPackageOrderReq.class);
        Long startId = 0L;
        long limit = 1000;

        try (ExcelWriter excelWriter = EasyExcel.write(taskDO.getFilePath(), BenefitsPackageOrderResp.class).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            while (true) {
                MPJLambdaWrapper<BenefitsPackageOrderDO> wrapper = req.buildQueryWrapper(true);
                wrapper.last("LIMIT " + limit);
                wrapper.gt(BenefitsPackageOrderDO::getPackageOrderId, startId);

                List<BenefitsPackageOrderDO> doList = baseMapper.selectJoinList(BenefitsPackageOrderDO.class, wrapper);
                if (doList.isEmpty()) {
                    break;
                }
                List<BenefitsPackageOrderResp> respList = doList.stream()
                        .map(BenefitsPackageOrderResp::buildResp)
                        .collect(Collectors.toList());
                excelWriter.write(respList, writeSheet);
                // 更新起始ID
                startId = doList.get(doList.size() - 1).getPackageOrderId();

            }

        }

    }


    @Override
    public void devOrder(BenefitsPackageDevOrderReq req) {
        if (AppConstants.isReal()) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "非测试环境不允许调用");
        }
        ChannelBenefitPackageOrderReq orderReq = new ChannelBenefitPackageOrderReq();
        orderReq.setChannelOrderNo(IdUtil.fastSimpleUUID());
        orderReq.setPackageCode(req.getPackageCode());
        orderReq.setMobile(req.getMobile());
        orderReq.setTimestamp(System.currentTimeMillis());
        orderReq.setApiKey("123");
        orderReq.setSign(getSign(orderReq, "123"));
        SpringUtil.getBean(NewBenefitsOrderManager.class).createBenefitOrder(orderReq);
    }

    private <T> String getSign(T data, String secret) {
        // 验证签名
        JSONObject dataJson = JSONObject.parseObject(JSONObject.toJSONString(data));
        StringBuilder dataStr = new StringBuilder();
        // 自然排序拼接
        dataJson.keySet().stream().sorted().forEach(key -> {
            Object value = dataJson.get(key);
            if (Objects.nonNull(value)) {
                dataStr.append(key).append("=").append(value).append("&");
            }
        });
        dataStr.append("secret=").append(secret);
        String mySign = DigestUtils.md5Hex(dataStr.toString());
        log.debug("dataStr={},sign={}", dataStr, mySign);
        return mySign;
    }
}

