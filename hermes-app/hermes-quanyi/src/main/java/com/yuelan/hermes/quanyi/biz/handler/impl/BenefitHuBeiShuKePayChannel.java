package com.yuelan.hermes.quanyi.biz.handler.impl;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.commons.enums.PayStatusEnum;
import com.yuelan.hermes.quanyi.biz.handler.BenefitPayHandler;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderDOService;
import com.yuelan.hermes.quanyi.biz.service.BenefitPlatformService;
import com.yuelan.hermes.quanyi.biz.service.HttpAsyncTaskService;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.enums.BenefitPayChannelEnum;
import com.yuelan.hermes.quanyi.common.enums.HuBeiShuKePayPkgEnum;
import com.yuelan.hermes.quanyi.common.enums.SuccessStrategyEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.HuBeiShuKeEduProperties;
import com.yuelan.hermes.quanyi.common.proxy.KuaiProxyHttpRequestSetter;
import com.yuelan.hermes.quanyi.common.proxy.ProxyHttpRequestSetter;
import com.yuelan.hermes.quanyi.config.task.HttpTaskRequest;
import com.yuelan.hermes.quanyi.controller.request.HuBeiShuKeEduNotifyExt;
import com.yuelan.hermes.quanyi.controller.request.HuBeiShuKeEduNotifyReq;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.remote.benefit.HuBeiShuKeEduManager;
import com.yuelan.hermes.quanyi.remote.benefit.HuBeiShuKeMusicManager;
import com.yuelan.hermes.quanyi.remote.hbsk.request.EduOrderConfirmReq;
import com.yuelan.hermes.quanyi.remote.hbsk.request.EduSmsCodeReq;
import com.yuelan.hermes.quanyi.remote.hbsk.request.ShuKeTwReportReq;
import com.yuelan.hermes.quanyi.remote.hbsk.response.*;
import com.yuelan.hermes.quanyi.remote.kuaiProxy.KuaiProxyManager;
import com.yuelan.plugins.redisson.util.RedisUtils;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR> 2025/4/29
 * @since 2025/4/29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BenefitHuBeiShuKePayChannel implements BenefitPayHandler {

    public static final String BUSINESS_TYPE = "SHUKE_EDU";

    private final HuBeiShuKeMusicManager musicManager;
    private final HuBeiShuKeEduManager eduManager;
    private final HuBeiShuKeEduProperties huBeiShuKeEduProperties;
    private final HttpAsyncTaskService httpAsyncTaskService;
    private final KuaiProxyManager kuaiProxyManager;
    @Resource
    @Lazy
    BenefitPlatformService benefitPlatformService;


    @Override
    public BenefitPayChannelEnum getChannel() {
        return BenefitPayChannelEnum.HB_SHU_KE;
    }

    /**
     * rsa公钥加密
     *
     * @param data      待加密数据
     * @param publicKey 公钥
     * @return 加密后字符串, 采用Base64编码
     */
    private static String encryptByRsa(String data, String publicKey) {
        RSA rsa = SecureUtil.rsa(null, publicKey);
        return rsa.encryptBase64(data, StandardCharsets.UTF_8, KeyType.PublicKey);
    }

    @Override
    public BenefitPayResultBO getPayUrl(UserBenefitOrderReq req, BenefitProductDO productDO, BenefitOrderDO orderDO) {
        String mobile = req.getMobile();
        String smsCode = req.getSmsCode();
        HuBeiShuKePayPkgEnum payPkgEnum = HuBeiShuKePayPkgEnum.of(productDO.getPayChannelPkgId());
        HuBeiShuKePayPkgEnum.PkgType pkgType = payPkgEnum.getPkgType();
        if (Objects.isNull(smsCode)) {
            log.error("短信验证码不能为空，手机号: {},-请重新提交验证码", mobile);
            return BenefitPayResultBO.fail("短信验证码不能为空");
        }
        String spOrderNo = RedisUtils.getCacheObject(cacheKey(mobile));
        if (Objects.isNull(spOrderNo)) {
            log.error("短信验证码已失效，手机号: {},-请重新获取验证码", mobile);
            return BenefitPayResultBO.fail("短信验证码失效，请重新获取验证码");
        }
        BenefitPayResultBO resultBO = null;
        try {
            if (HuBeiShuKePayPkgEnum.PkgType.EDU.equals(pkgType)) {
                return verifyEduSmsCode(req, productDO, orderDO);
            }
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "暂时只支持教育包订购");
        } catch (Exception e) {
            log.error("验证验证码异常，手机号: {},-请重新获取验证码", mobile, e);
            resultBO = BenefitPayResultBO.fail("验证验证码异常");
        } finally {
            if (Objects.nonNull(resultBO) && resultBO.isSuccess()) {
                RedisUtils.deleteObject(cacheKey(mobile));
            }
        }
        return resultBO;
    }

    private String cacheKey(String mobile) {
        return RedisKeys.getBenefitNumber(getChannel(), mobile);
    }

    /**
     * 获取新的代理IP并创建代理设置器
     *
     * @return 代理HTTP请求设置器
     */
    private ProxyHttpRequestSetter createNewProxyForUser() {
        try {
            // 获取新的代理IP
            String proxyIpAndPort = kuaiProxyManager.getProxyIpAndPort(new HashMap<>());
            log.info("获取新的代理IP: {}", proxyIpAndPort);

            String[] parts = proxyIpAndPort.split(":");
            if (parts.length < 2) {
                throw new RuntimeException("代理IP格式错误: " + proxyIpAndPort);
            }

            String proxyIp = parts[0].trim();
            int proxyPort = Integer.parseInt(parts[1].trim());

            return KuaiProxyHttpRequestSetter.create(kuaiProxyManager, proxyIp, proxyPort);
        } catch (Exception e) {
            log.error("获取代理IP失败", e);
            throw new RuntimeException("获取代理IP失败", e);
        }
    }


    public HuBeiShiKeCallBackResp dealCallBack(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        log.info("收到回调参数: {}", JSON.toJSONString(parameterMap));
        return HuBeiShiKeCallBackResp.success();
    }

    @Override
    public boolean orderSendSmsCode(UserBenefitOrderReq reqParams, BenefitProductDO productDO) {
        HuBeiShuKePayPkgEnum payPkgEnum = HuBeiShuKePayPkgEnum.of(productDO.getPayChannelPkgId());
        HuBeiShuKePayPkgEnum.PkgType pkgType = payPkgEnum.getPkgType();
        if (HuBeiShuKePayPkgEnum.PkgType.EDU.equals(pkgType)) {
            return sendEduSmsCode(reqParams, productDO);
        }
        throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "暂时只支持教育包订购");
    }

    /**
     * 教育包获取验证码
     */
    public boolean sendEduSmsCode(UserBenefitOrderReq reqParams, BenefitProductDO productDO) {
        String mobile = reqParams.getMobile();
        HuBeiShuKePayPkgEnum payPkgEnum = HuBeiShuKePayPkgEnum.of(productDO.getPayChannelPkgId());
        HuBeiShuKeEduProperties.EduConf eduConf = HuBeiShuKeEduProperties.pkgConfigMap.get(payPkgEnum);

        // 获取新的代理IP（每次获取验证码都使用新的代理IP）
        ProxyHttpRequestSetter proxySetter = createNewProxyForUser();

        // 使用代理获取活动信息
        ActivityInfoResp activityInfo = eduManager.getActivityInfo(eduConf.getActivityId(), proxySetter);
        if (!activityInfo.isBizSuccess()) {
            log.error("PJ-获取包信息失败: {}", JSON.toJSONString(activityInfo));
            throw BizException.create(BaseErrorCodeEnum.FALLBACK, "获取包信息失败");
        }
        ActivityDetail activityDetail = activityInfo.getExt();
        ActivityGood activityGood = activityDetail.getGoods().get(0);


        // 使用代理检查手机号是否为电信用户
        TelecomCheckResp telecomCheckResp = eduManager.checkIsTelecomWithResponse(mobile, proxySetter);
        if (!telecomCheckResp.isBizSuccess()) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK, "该业务不支持这个电话号码");
        }

        EduSmsCodeReq req = new EduSmsCodeReq();
        req.setPhone(mobile);
        req.setApp_id(eduConf.getAppId());
        req.setSale_product_id(activityGood.getSaleProductId());
        req.setSource_id(activityDetail.getActivityId());
        req.setProvince_pay_type("");

        // 使用代理发送短信验证码
        EduSmsCodeResp eduSmsCodeResp = eduManager.sendSmsCode(req, proxySetter);
        if (!eduSmsCodeResp.isBizSuccess()) {
            log.error("PJ-获取验证码失败: {}", JSON.toJSONString(eduSmsCodeResp));
            throw BizException.create(BaseErrorCodeEnum.FALLBACK, "获取验证码失败");
        }

        // 缓存活动信息和代理设置
        if (proxySetter instanceof KuaiProxyHttpRequestSetter) {
            KuaiProxyHttpRequestSetter kuaiProxySetter = (KuaiProxyHttpRequestSetter) proxySetter;
            eduCacheKey(mobile, activityInfo, kuaiProxySetter.getProxyIp(), kuaiProxySetter.getProxyPort());
        } else {
            // 如果不是快代理，则不缓存代理信息
            eduCacheKey(mobile, activityInfo, null, 0);
        }

        return true;
    }


    /**
     * 缓存教育包数据（包含活动信息和代理设置）
     *
     * @param mobile           用户手机号
     * @param activityInfoResp 活动信息响应
     * @param proxyIp          代理IP
     * @param proxyPort        代理端口
     */
    private void eduCacheKey(String mobile, ActivityInfoResp activityInfoResp, String proxyIp, int proxyPort) {
        String cacheKey = RedisKeys.getBenefitNumber(getChannel(), mobile);
        EduCacheData cacheData = EduCacheData.create(activityInfoResp, proxyIp, proxyPort);
        String cacheVal = JSONObject.toJSONString(cacheData);

        // 短信验证码是 5 分钟失效这边设置8 分钟
        RedisUtils.setCacheObject(cacheKey, cacheVal, Duration.ofMinutes(8));
        log.info("缓存教育包数据 - 用户: {}, 代理: {}:{}", mobile, proxyIp, proxyPort);
    }

    /**
     * 获取缓存的教育包数据
     *
     * @param mobile 用户手机号
     * @return 缓存的教育包数据，如果没有缓存则返回null
     */
    private EduCacheData getEduCacheData(String mobile) {
        String cacheKey = RedisKeys.getBenefitNumber(getChannel(), mobile);
        Object cacheObject = RedisUtils.getCacheObject(cacheKey);
        if (Objects.isNull(cacheObject)) {
            return null;
        }
        return JSONObject.parseObject(cacheObject.toString(), EduCacheData.class);
    }

    /**
     * 获取缓存的活动信息响应（保持向后兼容）
     *
     * @param mobile 用户手机号
     * @return 活动信息响应，如果没有缓存则返回null
     */
    private ActivityInfoResp getEduCacheKey(String mobile) {
        EduCacheData cacheData = getEduCacheData(mobile);
        return cacheData != null ? cacheData.getActivityInfoResp() : null;
    }


    public BenefitPayResultBO verifyEduSmsCode(UserBenefitOrderReq reqParams, BenefitProductDO productDO, BenefitOrderDO orderDO) {
        HuBeiShuKePayPkgEnum payPkgEnum = HuBeiShuKePayPkgEnum.of(productDO.getPayChannelPkgId());
        HuBeiShuKeEduProperties.EduConf eduConf = HuBeiShuKeEduProperties.pkgConfigMap.get(payPkgEnum);
        String mobile = reqParams.getMobile();
        String smsCode = reqParams.getSmsCode();


        // 获取缓存的教育包数据（包含活动信息和代理设置）
        EduCacheData cacheData = getEduCacheData(mobile);
        if (Objects.isNull(cacheData)) {
            return BenefitPayResultBO.fail("验证码已失效，请重新获取验证码");
        }

        ActivityInfoResp activityInfoResp = cacheData.getActivityInfoResp();

        // 获取缓存的代理设置器
        ProxyHttpRequestSetter proxySetter = null;
        if (cacheData.hasProxy()) {
            proxySetter = KuaiProxyHttpRequestSetter.create(kuaiProxyManager, cacheData.getProxyIp(), cacheData.getProxyPort());
            log.info("用户 {} 使用缓存的代理IP: {}:{}", mobile, cacheData.getProxyIp(), cacheData.getProxyPort());
        } else {
            log.warn("用户 {} 没有缓存的代理信息，将不使用代理", mobile);
        }
        ActivityDetail activityDetail = activityInfoResp.getExt();
        ActivityGood activityGood = activityDetail.getGoods().get(0);

        EduOrderConfirmReq confirmReq = new EduOrderConfirmReq();
        confirmReq.setPhone(eduManager.confirmOrderEncryptPhone(mobile));
        confirmReq.setApp_id(eduConf.getAppId());
        confirmReq.setSource_id(activityDetail.getActivityId());
        confirmReq.setSource_name(activityDetail.getActivityName());
        confirmReq.setSale_product_id(activityGood.getSaleProductId());
        confirmReq.setUa(HuBeiShuKeEduManager.USER_AGENT);
        confirmReq.setDevice_info(HuBeiShuKeEduManager.DEVICE_INFO);
        confirmReq.setSms_code(smsCode);
        confirmReq.setTime_stamp(System.currentTimeMillis());
        confirmReq.setCp_channel_code(eduConf.getCpChannelCode());
        confirmReq.setApp_name("");
        confirmReq.setProvince_pay_type("");

        // 使用代理确认订单
        EduOrderConfirmResp eduOrderConfirmResp = eduManager.confirmOrder(confirmReq, proxySetter);
        if (!eduOrderConfirmResp.isSuccess()) {
            return BenefitPayResultBO.fail("订购失败");
        }

        orderDO.setExtraData(eduOrderConfirmResp.getExt());
        // sendPhone2TwValue(orderDO.getOrderNo(), mobile, eduOrderConfirmResp.getExt());

        return BenefitPayResultBO.success();
    }

    public void sendPhone2TwValue(String orderNo, String mobile, String twValue) {
        try {

            ShuKeTwReportReq req = new ShuKeTwReportReq();
            req.setClientOrderId(twValue);
            req.setMobile(mobile);
            JSONObject json = JSONObject.from(req);

            String body = encryptByRsa(json.toJSONString(), HuBeiShuKeEduProperties.RSA_PUBLIC_KEY);
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "text/plain; charset=utf-8");
            headers.put("source", HuBeiShuKeEduProperties.SOURCE);

            HttpTaskRequest httpTaskRequest = HttpTaskRequest.builder()
                    .url(HuBeiShuKeEduProperties.PHONE_TW_REPORT_URL)
                    .method("POST")
                    .headers(headers)
                    .body(body)
                    .sourceSystem("SHUKE_EDU")
                    .businessType(BUSINESS_TYPE)
                    .businessId(orderNo)
                    .maxRetryCount(6)
                    .retryInterval(600)
                    .successStrategy(SuccessStrategyEnum.RESPONSE_SHU_KE_SPECIAL)
                    .extData(json.toJSONString())
                    .build();
            httpAsyncTaskService.createTask(httpTaskRequest);

        } catch (Exception e) {
            log.error("上报 TW任务失败 phone: {}, tw: {}", mobile, twValue, e);
        }
    }

    public String eduPayNotify(HuBeiShuKeEduNotifyReq req) {
        try {
            // 运营商要求无论什么情况回调 都要给他再回调一次手机和 TW对应关系
            // 订购类型为订购,code=0,表示订购成功
            // 订购类型为订购,code=非0,表示订购失败
            // 订购类型为退订,code=0,表示退订成功
            // 订购类型为退订,code=非0,表示退订失败
            boolean subscribeNotify = req.isSubscribeNotify();
            boolean unsubscribeNotify = req.isUnsubscribeNotify();
            boolean successStatusNotify = req.isSuccessStatusNotify();
            // tw值
            String tw = Optional.ofNullable(req.getExt())
                    .map(HuBeiShuKeEduNotifyExt::getClient_order_id)
                    .orElse(null);
            if (Objects.isNull(tw)) {
                log.error("tw值为空,直接返回失败");
                return "-1";
            }
            BenefitOrderDO dbOrderDo = findEduLastOrderByTwValue(tw);
            if (Objects.isNull(dbOrderDo)) {
                log.error("数科教育回调订单不存在,直接返回成功");
                return "0";
            }
            if (subscribeNotify) {
                log.info("订购通知订单状态: {}", successStatusNotify);
                if (!PayStatusEnum.DEFAULT.getCode().equals(dbOrderDo.getPayStatus())) {
                    log.error("订单号：{} 已处理 忽略", dbOrderDo.getOrderNo());
                    return "0";
                }
                BenefitOrderDO updateDO = new BenefitOrderDO();
                updateDO.setOrderId(dbOrderDo.getOrderId());
                updateDO.setPayNotifyTime(LocalDateTime.now());
                updateDO.setOutOrderNo(req.getExt().getOrder_id());
                updateDO.setPayNotifyContent(JSONObject.toJSONString(req));
                String msg;
                if (successStatusNotify) {
                    log.info("支付成功通知");  // 修复：成功通知使用 info 级别
                    msg = "success";
                    updateDO.setPayStatus(PayStatusEnum.SUCCESS.getCode());
                } else {
                    log.warn("支付失败通知");   // 修复：失败通知使用 warn 级别更合适
                    msg = req.getText() == null ? "fail" : req.getText();
                    updateDO.setPayStatus(PayStatusEnum.FAIL.getCode());
                }
                SpringUtil.getBean(BenefitOrderDOService.class)
                        .updateById(updateDO);

                benefitPlatformService.notifyMediaOrderResultAsync(dbOrderDo, msg);
            } else if (unsubscribeNotify) {
                log.info("【暂时不处理】退订通知订单状态: {}", successStatusNotify);
            } else {
                log.info("未知通知类型,直接返回成功");
            }
            log.info("上报 TW任务");
            sendPhone2TwValue(dbOrderDo.getOrderNo(), dbOrderDo.getPhone(), tw);
            return "0";

        } catch (Exception e) {
            log.error("数科教育回调失败", e);
        }
        return "-1";
    }

    public BenefitOrderDO findEduLastOrderByTwValue(String twValue) {
        List<Integer> payChannelPkgIds = new ArrayList<>();
        HuBeiShuKePayPkgEnum.getByPkgType(HuBeiShuKePayPkgEnum.PkgType.EDU)
                .forEach(x -> payChannelPkgIds.add(x.getChannelPkgId()));
        return benefitPlatformService.getShuKeEduOrder(getChannel().getCode(), payChannelPkgIds, twValue);
    }


}
