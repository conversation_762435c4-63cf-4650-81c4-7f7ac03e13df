package com.yuelan.hermes.quanyi.biz.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuelan.hermes.quanyi.biz.handler.FileExportTaskHandler;
import com.yuelan.hermes.quanyi.biz.service.*;
import com.yuelan.hermes.quanyi.common.enums.EccChannelTypeEnum;
import com.yuelan.hermes.quanyi.common.enums.FileExportTaskCodeEnum;
import com.yuelan.hermes.quanyi.common.enums.NcOrderStatusEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccNcOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.FileExportTaskDO;
import com.yuelan.hermes.quanyi.common.util.MpPageUtil;
import com.yuelan.hermes.quanyi.config.satoken.StpAdminUtil;
import com.yuelan.hermes.quanyi.controller.request.EccNcOrderListReq;
import com.yuelan.hermes.quanyi.controller.response.EccNcOrderResp;
import com.yuelan.hermes.quanyi.controller.response.FileExportTaskCreateResp;
import com.yuelan.hermes.quanyi.mapper.EccNcOrderMapper;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/12/25
 * @since 2024/12/25
 */
@Service
@RequiredArgsConstructor
public class EccNcOrderServiceImpl extends ServiceImpl<EccNcOrderMapper, EccNcOrderDO> implements EccNcOrderService, FileExportTaskHandler {

    private final EccOuterChannelDOService outerChannelDOService;
    private final EccChannelDOService eccChannelDOService;
    private final FileExportTaskDOService fileExportTaskDOService;
    private final EccNcOrderMapper eccNcOrderMapper;
    private final OrderVisibilityService orderVisibilityService;
    private final EccAdminChannelDOService eccAdminChannelDOService;


    @Override
    public boolean updateFailReasonAndStatus(EccNcOrderDO eccNcOrderDO) {
        return new LambdaUpdateChainWrapper<>(this.eccNcOrderMapper)
                .eq(EccNcOrderDO::getOrderNo, eccNcOrderDO.getOrderNo())
                .eq(EccNcOrderDO::getPhone, eccNcOrderDO.getPhone())
                .set(EccNcOrderDO::getFailReason, eccNcOrderDO.getFailReason())
                .set(EccNcOrderDO::getOrderStatus, eccNcOrderDO.getOrderStatus())
                .set(EccNcOrderDO::getUpdateTime, eccNcOrderDO.getUpdateTime())
                .update();
    }

    @Override
    public EccNcOrderDO getBySpOrderNo(String spOrderNo) {
        return this.getOne(Wrappers.lambdaQuery(EccNcOrderDO.class).eq(EccNcOrderDO::getSpOrderNo, spOrderNo));
    }

    @Override
    public EccNcOrderDO getByOrderNo(String orderNo) {
        return this.getOne(Wrappers.lambdaQuery(EccNcOrderDO.class).eq(EccNcOrderDO::getOrderNo, orderNo));
    }

    @Override
    public PageData<EccNcOrderResp> pageList(EccNcOrderListReq req, long adminId) {
        IPage<EccNcOrderDO> page = MpPageUtil.convertPageRequest(req);

        List<Long> innerChannelLimit = eccChannelDOService.selectLimitChannelIds();
        List<Long> outChannelsLimit = eccAdminChannelDOService.listAllOutChannelsLimit();
        //  1. 发展归属条件：该订单的「发展渠道」必须是 自己或自己所有下级渠道。
        //   2. 扣量归属条件：该订单的「扣量渠道」必须是 空值（未指定），或者是自己或自己所有下级渠道。
        page = this.baseMapper.selectPage(page, req.buildQueryWrapper(outChannelsLimit, innerChannelLimit, false));

        List<EccNcOrderResp> respList = page.getRecords().stream()
                .map(EccNcOrderResp::buildResp)
                .collect(Collectors.toList());
        return PageData.create(respList, page.getTotal(), req.getPage(), req.getSize());
    }

    @Override
    public FileExportTaskCreateResp export(EccNcOrderListReq req, long adminId) {
        if (req.getReqCardTimeEnd() == null) {
            // 防止数据一直在变化
            req.setReqCardTimeEnd(LocalDateTime.now());
        } else {
            // 如果结束时间大于当前时间
            if (req.getReqCardTimeEnd().isAfter(LocalDateTime.now())) {
                req.setReqCardTimeEnd(LocalDateTime.now());
            }
        }
        String param = JSONObject.toJSONString(req);
        JSONObject paramJson = JSONObject.parseObject(param);
        paramJson.put("adminId", adminId);

        // 检查是否是渠道用户
        List<Long> innerChannelLimit = eccChannelDOService.selectLimitChannelIds();
        List<Long> outChannelsLimit = eccAdminChannelDOService.listAllOutChannelsLimit();
        Long count = baseMapper.selectCount(req.buildQueryWrapper(outChannelsLimit, innerChannelLimit, true));
        if (count > 500000) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "导出数据量过大，请缩小查询范围");
        }
        FileExportTaskDO taskDO = fileExportTaskDOService.createDefaultTask(paramJson.toString(), getTaskCode(), "xlsx");
        return new FileExportTaskCreateResp(taskDO.getTaskId(), taskDO.getFileName());
    }

    @Override
    public int getOuterChannelOrderCount(Long outChannelId, EccChannelTypeEnum eccChannelTypeEnum, String channelOrderNo) {
        return eccNcOrderMapper.getOuterChannelOrderCount(outChannelId, eccChannelTypeEnum.getType(), channelOrderNo);
    }

    @Override
    public List<EccNcOrderDO> getSuccessOrderAndNoChargeByProductId(List<Long> productIds, LocalDateTime startTime, LocalDateTime endTime) {
        if (productIds == null || productIds.isEmpty()) {
            return Collections.emptyList();
        }
        return this.list(Wrappers.lambdaQuery(EccNcOrderDO.class)
                .eq(EccNcOrderDO::getOrderStatus, NcOrderStatusEnum.GET_CARD_SUCCESS.getCode())
                .in(EccNcOrderDO::getProdId, productIds)
                .ge(EccNcOrderDO::getReqCardTime, startTime)
                .le(EccNcOrderDO::getReqCardTime, endTime)
                .isNull(EccNcOrderDO::getFirstChargeAmount)
        );
    }

    @Override
    public FileExportTaskCodeEnum getTaskCode() {
        return FileExportTaskCodeEnum.ECC_NC_ORDER_EXPORT;
    }

    @Override
    public void generateExportFile(FileExportTaskDO taskDO) {
        String evalParam = taskDO.getEvalParam();
        JSONObject paramJson = JSONObject.parseObject(evalParam);
        Long adminId = paramJson.getLong("adminId");
        paramJson.remove("adminId");
        EccNcOrderListReq req = JSONObject.parseObject(paramJson.toJSONString(), EccNcOrderListReq.class);

        StpAdminUtil.login(adminId);
        // 检查是否是渠道用户
        List<Long> innerChannelLimit = eccChannelDOService.selectLimitChannelIds();
        List<Long> outChannelsLimit = eccAdminChannelDOService.listAllOutChannelsLimit();
        Long startId = 0L;
        long limit = 1000;

        try (ExcelWriter excelWriter = EasyExcel.write(taskDO.getFilePath(), EccNcOrderResp.class).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            while (true) {
                LambdaQueryWrapper<EccNcOrderDO> wrapper = req.buildQueryWrapper(outChannelsLimit, innerChannelLimit, true);
                wrapper.last("LIMIT " + limit);
                wrapper.gt(EccNcOrderDO::getOrderId, startId);

                List<EccNcOrderDO> doList = this.baseMapper.selectList(wrapper);
                if (doList.isEmpty()) {
                    break;
                }
                List<EccNcOrderResp> respList = doList.stream()
                        .map(EccNcOrderResp::buildResp)
                        .collect(Collectors.toList());
                excelWriter.write(respList, writeSheet);
                // 更新起始ID
                startId = doList.get(doList.size() - 1).getOrderId();
            }
        }
    }
}
