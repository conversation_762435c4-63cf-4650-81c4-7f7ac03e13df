package com.yuelan.hermes.quanyi.common.event;

import com.yuelan.hermes.quanyi.remote.jinxi.request.JinXiCallBackReq;
import lombok.Data;
import org.springframework.context.ApplicationEvent;

/**
 * 今溪订单通知事件
 * 参考SoftGameOrderNotifyEvent实现
 *
 * <AUTHOR> 2025/8/2
 * @since 2025/8/2
 */
@Data
public class JinXiOrderNotifyEvent extends ApplicationEvent {

    /**
     * 回调请求
     */
    private JinXiCallBackReq request;

    /**
     * 是否已处理
     */
    private Boolean isHandled = false;

    /**
     * 处理是否成功
     */
    private Boolean successDeal = false;

    public JinXiOrderNotifyEvent() {
        super(new Object());
    }
}
