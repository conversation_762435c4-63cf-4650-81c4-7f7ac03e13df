package com.yuelan.hermes.quanyi.common.enums;

import lombok.Getter;

/**
 * <AUTHOR> 2025/3/21
 * @since 2025/3/21
 */
@Getter
public enum SuccessStrategyEnum {
    HTTP_STATUS("HTTP_STATUS", "HTTP状态码判断"),
    RESPONSE_CONTAINS("RESPONSE_CONTAINS", "响应内容包含特定字符串"),
    RESPONSE_JSON_PATH("RESPONSE_JSON_PATH", "JSON路径表达式判断"),
    RESPONSE_JSON_CODE_0("RESPONSE_JSON_CODE_0", "响应JSON中code字段为0"),
    RESPONSE_SHU_KE_SPECIAL("RESPONSE_SHU_KE_SPECIAL", "湖北数科特别判断"),
    CUSTOM("CUSTOM", "自定义判断逻辑");

    private final String code;
    private final String desc;

    SuccessStrategyEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}