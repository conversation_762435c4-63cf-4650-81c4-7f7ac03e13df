package com.yuelan.hermes.quanyi.config.satoken.context;


import cn.hutool.extra.spring.SpringUtil;
import com.yuelan.hermes.quanyi.biz.service.EccChannelDOService;
import com.yuelan.hermes.quanyi.biz.service.EccOuterChannelDOService;
import com.yuelan.hermes.quanyi.common.enums.SysRoleEnum;
import com.yuelan.plugins.satoken.context.LoginContext;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AdminContext extends LoginContext {
    /**
     * 管理员ID
     */
    private Long adminId;
    /**
     * 管理员名称
     */
    private String nickname;

    /**
     * 手机号码
     */
    private String phone;

    /**
     */
    private List<SysRoleEnum> roles;

    /**
     * 外部渠道信息
     */
    private OutChannelContext outChannelContext;

    /**
     * 内部渠道信息
     */
    private InnerChannelContext innerChannelContext;

    @Data
    public static class InnerChannelContext{
        /**
         * 关联的内部渠道ID
         */
        private Long innerChannelId;

        /**
         * 上级渠道ID
         */
        private Long parentChannelId;
        /**
         * 渠道名称
         */
        private String channelName;
    }



    @Data
    public static class OutChannelContext{

        /**
         * 关联的外部渠道ID
         */
        private Long outerChannelId;

        /**
         * 渠道名称
         */
        private String channelName;

        /**
         * 渠道级别
         */
        private Integer channelLevel;

        /**
         * 上级渠道ID
         */
        private Long parentChannelId;

        /**
         * 扣量比例
         */
        private Integer deductionRate;

        /**
         * 是否禁用
         */
        private Integer isDisabled;

    }


    public boolean isSysAdmin(){
       return getRoles().contains(SysRoleEnum.ADMIN);
    }

    public boolean isEccChannel(){
        return getRoles().contains(SysRoleEnum.ECC_CHANNEL)
                || getRoles().contains(SysRoleEnum.ECC_OUTER_END_LEVEL);
    }

    public List<Long> listAllOutChannelsLimit() {
        List<Long> limit = new ArrayList<>();
        if (this.isSysAdmin()) {
            return null;
        }else if (this.isEccChannel()) {
            Long outerChannelId = this.getOutChannelContext().getOuterChannelId();
            EccOuterChannelDOService bean = SpringUtil.getBean(EccOuterChannelDOService.class);
            limit.add(outerChannelId);
            limit.addAll(bean.getAllDescendantChannelIds(outerChannelId));
        }
        return limit;
    }

    public List<Long> listAllInnerChannelsLimit() {
        List<Long> limit = new ArrayList<>();
        if (this.isSysAdmin()) {
            return null;
        }else if (this.isEccChannel()) {
            Long innerChannelId = this.getInnerChannelContext().getInnerChannelId();
            EccChannelDOService bean = SpringUtil.getBean(EccChannelDOService.class);
            limit.add(innerChannelId);
            limit.addAll(bean.getAllDescendantChannelIds(innerChannelId));
        }
        return limit;
    }
}
