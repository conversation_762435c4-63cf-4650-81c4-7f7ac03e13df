package com.yuelan.hermes.quanyi.config.satoken.context;


import com.yuelan.plugins.satoken.context.LoginContext;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AdminContext extends LoginContext {
    /**
     * 管理员ID
     */
    private Long adminId;
    /**
     * 管理员名称
     */
    private String nickname;
}
