package com.yuelan.hermes.quanyi.controller.request;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR> 2024/5/17 下午4:05
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class EccOuterChannelListReq extends PageRequest {

    @Schema(description = "外部渠道id")
    public Long outerChannelId;

    @Schema(description = "渠道名字")
    private String channelName;

    @Schema(description = "对接参数apiKey")
    private String apiKey;

    @Schema(description = "上级渠道ID")
    private Long parentChannelId;

    @Schema(description = "渠道层级类型：1-一级渠道，2-二级渠道")
    private Integer channelLevel;

    /**
     * @param outChannelLimit null 表示不限制 其他表示in 空 list 表示没有权限
     */
    public MPJLambdaWrapper<EccOuterChannelDO> buildQueryWrapper(List<Long> outChannelLimit) {
        return new MPJLambdaWrapper<>(EccOuterChannelDO.class)
                .selectAll(EccOuterChannelDO.class)
                .selectAs(EccOuterChannelDO::getChannelName, EccOuterChannelDO::getParentChannelName, "t1")
                .leftJoin(EccOuterChannelDO.class, "t1", EccOuterChannelDO::getOuterChannelId, EccOuterChannelDO::getParentChannelId)
                .eq(outerChannelId != null, EccOuterChannelDO::getOuterChannelId, outerChannelId)
                .like(channelName != null, EccOuterChannelDO::getChannelName, channelName)
                .eq(apiKey != null, EccOuterChannelDO::getApiKey, apiKey)
                .eq(parentChannelId != null, EccOuterChannelDO::getParentChannelId, parentChannelId)
                .in(outChannelLimit != null && !outChannelLimit.isEmpty(), EccOuterChannelDO::getParentChannelId, outChannelLimit)
                // 无权限
                .eq(outChannelLimit != null && outChannelLimit.isEmpty(), EccOuterChannelDO::getOuterChannelId, -1)
                .eq(channelLevel != null, EccOuterChannelDO::getChannelLevel, channelLevel)
                .orderByDesc(EccOuterChannelDO::getOuterChannelId);
    }

}
