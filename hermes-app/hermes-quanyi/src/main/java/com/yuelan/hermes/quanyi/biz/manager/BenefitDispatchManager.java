package com.yuelan.hermes.quanyi.biz.manager;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.commons.enums.DeliveryTypeEnum;
import com.yuelan.hermes.commons.enums.SupplierEnum;
import com.yuelan.hermes.quanyi.biz.handler.CacheHandler;
import com.yuelan.hermes.quanyi.biz.handler.factory.BenefitSupplierFactory;
import com.yuelan.hermes.quanyi.biz.service.BenefitItemOrderService;
import com.yuelan.hermes.quanyi.biz.service.BenefitItemService;
import com.yuelan.hermes.quanyi.common.enums.BenefitItemOrderProcessStateEnum;
import com.yuelan.hermes.quanyi.common.enums.BenefitItemOrderStatus;
import com.yuelan.hermes.quanyi.common.enums.RedemptionStatusEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BenefitErrorEnum;
import com.yuelan.hermes.quanyi.common.event.BenefitItemOrderStatusChangeEvent;
import com.yuelan.hermes.quanyi.common.interfaces.BenefitSupplier;
import com.yuelan.hermes.quanyi.common.pojo.bo.DispatchRedeemCodeResultDTO;
import com.yuelan.hermes.quanyi.common.pojo.bo.DispatchResultDTO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemOrderDO;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2025/4/24
 * @since 2025/4/24
 * <p>
 * 供应商管理类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BenefitDispatchManager {

    private final BenefitSupplierFactory benefitSupplierFactory;
    private final BenefitItemService benefitItemService;
    private final BenefitItemOrderService benefitItemOrderService;
    private final CacheHandler cacheHandler;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 下发和这个扩展id关联的所有待下发的权益
     *
     * @param extensionId 权益扩展ID
     * @return 是否消费成功消息
     */
    public boolean dispatchBenefits(Long extensionId) {
        List<BenefitItemOrderDO> orderList = benefitItemOrderService.getExtensionInInfo(extensionId, BenefitItemOrderProcessStateEnum.WAIT_REQUEST.getCode());
        if (orderList.isEmpty()) {
            log.info("权益下发，权益订单不存在。extensionId:{}", extensionId);
            return true;
        }
        log.info("准备权益下发，权益订单数量：{}", orderList.size());
        for (BenefitItemOrderDO benefitItemOrderDO : orderList) {
            try {
                DispatchResultDTO dispatchResultDTO = tryDispatch(benefitItemOrderDO);
                log.info("权益下发，权益订单号：{}，权益下发结果：{}", benefitItemOrderDO.getItemOrderId(), JSONObject.toJSONString(dispatchResultDTO));
            } catch (Exception e) {
                log.error("权益下发异常，订单号：{}，异常信息：{}", benefitItemOrderDO.getItemOrderId(), e.getMessage());
            }
        }
        return true;
    }

    /**
     * 尝试下发权益
     * 没有增加事务，如果增加事务异常情况
     * 场景1：更新陈DISPATCHING 发放中后接口调用也成功 但是更新数据库失败 如果回滚的话 变成WAIT状态多次调用会重复下发（因为部分供应商接口可能不支持幂等调用，比如爆米花都没有请求订单号）
     */
    public DispatchResultDTO tryDispatch(BenefitItemOrderDO orderDO) {
        RLock lock = cacheHandler.getBenefitDispatchLock(orderDO.getItemOrderId());
        BizException.assertCheck(Objects.nonNull(lock), BenefitErrorEnum.ORDER_PROCESSING);
        try {
            boolean wait = BenefitItemOrderProcessStateEnum.WAIT_REQUEST.getCode().equals(orderDO.getProcessState());
            BizException.assertCheck(wait, BenefitErrorEnum.ORDER_STATUS_ERROR);

            SupplierEnum supplierEnum = SupplierEnum.of(orderDO.getSupplierId());
            BenefitSupplier supplier = benefitSupplierFactory.getSupplier(supplierEnum);
            // 权益商品
            BenefitItemDO benefitItemDO = benefitItemService.getById(orderDO.getBenefitItemId());
            // 执行远程下发请求
            DispatchResultDTO dispatchResultDTO = supplier.dispatchBenefits(orderDO, benefitItemDO);
            updateDispatchResultToOrder(orderDO, dispatchResultDTO);
            return dispatchResultDTO;
        } catch (Exception e) {
            log.error("权益处理下发异常", e);
            DispatchResultDTO resultDTO = new DispatchResultDTO(false, false) {{
                setErrorMessage("权益处理下发异常");
            }};
            updateDispatchResultToOrder(orderDO, resultDTO);
            return resultDTO;
        } finally {
            cacheHandler.releaseLock(lock);
        }
    }


    private void updateDispatchResultToOrder(BenefitItemOrderDO orderDO, DispatchResultDTO resultDTO) {
        BenefitItemOrderDO updateOrder = new BenefitItemOrderDO();
        updateOrder.setItemOrderId(orderDO.getItemOrderId());
        updateOrder.setRequestTime(LocalDateTime.now());
        final int maxContentLength = 2000;
        updateOrder.setRequest(StrUtil.sub(resultDTO.getSupplierRequest(), 0, maxContentLength));
        updateOrder.setResponse(StrUtil.sub(resultDTO.getSupplierResponse(), 0, maxContentLength));
        updateOrder.setSupplierOrderNo(resultDTO.getSupplierOrderNo());
        if (resultDTO.isSuccess()) {
            // 实时到账 或者实时响应兑换码
            if (resultDTO.isRealTime()) {
                updateOrder.setOrderStatus(BenefitItemOrderStatus.SUCCESS.getCode());
                updateOrder.setProcessState(BenefitItemOrderProcessStateEnum.DISPATCH_SUCCESS.getCode());
                updateOrder.setCallbackTime(LocalDateTime.now());
            } else {
                updateOrder.setProcessState(BenefitItemOrderProcessStateEnum.REQUEST_SUCCESS.getCode());
            }
        } else {
            // 失败
            updateOrder.setOrderStatus(BenefitItemOrderStatus.FAIL.getCode());
            updateOrder.setProcessState(BenefitItemOrderProcessStateEnum.REQUEST_FAIL.getCode());
        }
        BenefitItemOrderProcessStateEnum processStateEnum = BenefitItemOrderProcessStateEnum.getByCode(updateOrder.getProcessState());
        String processStateDesc = processStateEnum == null ? null : processStateEnum.getDesc();
        if (Objects.nonNull(processStateDesc) && resultDTO.getErrorMessage() != null) {
            processStateDesc += resultDTO.getErrorMessage();
        }
        updateOrder.setProcessStateDesc(processStateDesc);
        updateOrder.setDeliveryType(DeliveryTypeEnum.DIRECT_RECHARGE.getCode());
        // 这样写不太好 懒得改了
        if (resultDTO instanceof DispatchRedeemCodeResultDTO) {
            DispatchRedeemCodeResultDTO redeemCodeResultDTO = (DispatchRedeemCodeResultDTO) resultDTO;
            updateOrder.setDeliveryType(DeliveryTypeEnum.REDEEM_CODE.getCode());
            updateOrder.setRedeemCode(redeemCodeResultDTO.getRedeemCode());
            updateOrder.setRedeemCodePwd(redeemCodeResultDTO.getRedeemCodePassword());
            updateOrder.setRedeemCodeExpireTime(redeemCodeResultDTO.getExpireTime());
            updateOrder.setRedemptionStatus(redeemCodeResultDTO.isHasNotify() ? RedemptionStatusEnum.PENDING.getCode() : RedemptionStatusEnum.NONE.getCode());
        }
        benefitItemOrderService.updateById(updateOrder);
        BenefitItemOrderStatusChangeEvent event = new BenefitItemOrderStatusChangeEvent();
        event.setItemOrderId(orderDO.getItemOrderId());
        event.setExtensionId(orderDO.getExtensionId());
        event.setDispatchTiming(orderDO.getDispatchTiming());
        event.setOldOrderStatus(orderDO.getOrderStatus());
        event.setNewOrderStatus(updateOrder.getOrderStatus());
        itemOrderStatusChangeEvent(event);

    }

    public void itemOrderStatusChangeEvent(BenefitItemOrderStatusChangeEvent event) {
        eventPublisher.publishEvent(event);
    }


}
