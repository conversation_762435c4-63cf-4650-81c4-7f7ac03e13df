package com.yuelan.hermes.quanyi.biz.manager;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson2.JSON;
import com.yuelan.boot.utils.HttpServletRequestUtil;
import com.yuelan.core.util.MapstructUtils;
import com.yuelan.hermes.commons.enums.BizNoPrefixEnum;
import com.yuelan.hermes.commons.enums.UpOffStatusEnum;
import com.yuelan.hermes.quanyi.biz.handler.CacheHandler;
import com.yuelan.hermes.quanyi.biz.handler.NumberCardServer;
import com.yuelan.hermes.quanyi.biz.service.*;
import com.yuelan.hermes.quanyi.common.enums.*;
import com.yuelan.hermes.quanyi.common.enums.error.EccErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.event.NumberCardStatusChangeEvent;
import com.yuelan.hermes.quanyi.common.pojo.bo.EccGetCardResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.PhonePrettyTagBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.*;
import com.yuelan.hermes.quanyi.common.util.LocalBizNoPlusUtils;
import com.yuelan.hermes.quanyi.controller.AppEccProdConfigResp;
import com.yuelan.hermes.quanyi.controller.request.*;
import com.yuelan.hermes.quanyi.controller.response.*;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/12/25
 * @since 2024/12/25
 * <p>
 * 号卡领取
 */
@Slf4j
@Service
@AllArgsConstructor
public class NumberCardManager {

    private static final Map<EccSpEnum, NumberCardServer> NUMBER_CARD_SERVER_MAP = new HashMap<>();
    private final CacheHandler cacheHandler;
    private final EccChannelDOService eccChannelDOService;
    private final EccOuterChannelDOService eccOuterChannelDOService;
    private final AdChannelDOService adChannelDOService;
    private final EccProductDOService eccProductDOService;
    private final EccAreaService eccAreaService;
    private final EccNcOrderService eccNcOrderService;
    private final EccAggregatePageService aggregatePageService;
    private final EccAggregatePageProductService aggregatePageProductService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final Ip2RegionManager ip2RegionManager;
    private final EccProductPageService eccProductPageService;

    @Autowired
    private void initNumberCardManager(List<NumberCardServer> numberCardServers) {
        for (NumberCardServer numberCardServer : numberCardServers) {
            EccSpEnum spEnum = numberCardServer.getSpEnum();
            NUMBER_CARD_SERVER_MAP.put(spEnum, numberCardServer);
        }
    }

    /**
     * 领卡
     *
     * @param req             请求对象
     * @param channelTypeEnum 渠道类型
     * @return 结果对象
     */
    public EccGetCardResultBO receiveSimCard(EccBaseGetCardReq req, EccChannelTypeEnum channelTypeEnum) {
        req.reqCheck();
        RLock lock = acquireLock(req.getIdCardNo());
        if (Objects.isNull(lock)) {
            throw BizException.create(EccErrorCodeEnum.OPERATE_FREQUENTLY, "操作太频繁请等待");
        }
        try {
            EccNcOrderDO orderDO = initializeOrder(req, channelTypeEnum);
            // 校验商品
            EccProductDO productDO = eccProductDOService.checkProduct(req.getEccProdCode());
            EccSpEnum spEnum = getSpEnumByProduct(productDO);
            // 设置收货地址以及号码归属地
            setPostAreaAndPhoneArea(spEnum, orderDO);
            orderDO.setOperator(spEnum.getSpEnum().getCode());
            orderDO.setProdId(productDO.getProdId());
            orderDO.setProdName(productDO.getProdName());
            orderDO.setSpGoodsId(productDO.getSpGoodsId());

            try {
                prohibitedAreasCheck(orderDO, productDO);
                // 提交订单
                submitOrder(orderDO, spEnum);
                return createResult(orderDO);
            } finally {
                saveOrder(orderDO);
            }
        } finally {
            releaseLock(lock);
        }

    }

    private void prohibitedAreasCheck(EccNcOrderDO orderDO, EccProductDO productDO) {
        if (eccProductDOService.isProhibitedAreas(orderDO.getPostProvince(), orderDO.getPostCity(), productDO)) {
            orderDO.setOrderStatus(NcOrderStatusEnum.GET_CARD_FAIL.getCode());
            orderDO.setFailReason("暂不支持该地区领卡");
            throw BizException.create(EccErrorCodeEnum.SYS_PROHIBITED_AREA);
        }
    }

    /**
     * 号码搜索
     *
     * @param req 请求对象
     * @return 搜索的号码列表
     */
    public PageData<PhonePrettyTagBO> searchPhone(NcPhoneKeySearchReq req) {
        EccProductDO productDO = eccProductDOService.checkProduct(req.getEccProdCode());
        EccSpEnum spEnum = getSpEnumByProduct(productDO);
        return NUMBER_CARD_SERVER_MAP.get(spEnum).selectPhoneNumPool(req, productDO);
    }


    public List<EccPostAreaResp> listProvince(String eccProductCode) {
        EccProductDO productDO = eccProductDOService.checkProduct(eccProductCode);
        EccSpEnum spEnum = getSpEnumByProduct(productDO);
        return eccAreaService.listProvince(spEnum.getSpEnum());
    }

    public List<EccPostAreaResp> listArea(String eccProductCode, String postProvinceCode) {
        EccProductDO productDO = eccProductDOService.checkProduct(eccProductCode);
        EccSpEnum spEnum = getSpEnumByProduct(productDO);
        return eccAreaService.listArea(spEnum.getSpEnum(), postProvinceCode);
    }


    /**
     * 领卡请求
     *
     * @param orderDO 订单对象
     */
    private void submitOrder(EccNcOrderDO orderDO, EccSpEnum spEnum) {
        NumberCardServer cardServer = NUMBER_CARD_SERVER_MAP.get(spEnum);
        cardServer.getCard(orderDO);
    }


    /**
     * 保存订单
     *
     * @param orderDO 订单对象
     */
    private void saveOrder(EccNcOrderDO orderDO) {
        eccNcOrderService.save(orderDO);
        sendEvent(null, orderDO);
    }

    /**
     * 非信息流订单直接插入记录下来
     */
    public void saveOrderDirectly(EccNcOrderDO orderDO) {
        eccNcOrderService.save(orderDO);
        // 发送事件
        sendEvent(null, orderDO);
    }

    /**
     * 发送事件
     *
     * @param orderDO 订单对象
     */
    public void sendEvent(EccNcOrderDO oldOrderDO, EccNcOrderDO orderDO) {
        NumberCardStatusChangeEvent event = new NumberCardStatusChangeEvent();
        if (oldOrderDO == null) {
            event.setOrderStatus(NcOrderStatusEnum.of(orderDO.getOrderStatus()));
            event.setSimCardStatus(SimCardStatusEnum.of(orderDO.getCardStatus()));
            event.setRechargeAmount(orderDO.getFirstChargeAmount());
        } else {
            Integer oldOrderStatus = oldOrderDO.getOrderStatus();
            Integer oldCardStatus = oldOrderDO.getCardStatus();
            Integer oldRechargeAmount = oldOrderDO.getFirstChargeAmount();

            if (!Objects.equals(oldOrderStatus, orderDO.getOrderStatus())) {
                event.setOrderStatus(NcOrderStatusEnum.of(orderDO.getOrderStatus()));
            }
            if (!Objects.equals(oldCardStatus, orderDO.getCardStatus())) {
                event.setSimCardStatus(SimCardStatusEnum.of(orderDO.getCardStatus()));
            }
            if (!Objects.equals(oldRechargeAmount, orderDO.getFirstChargeAmount())) {
                event.setRechargeAmount(orderDO.getFirstChargeAmount());
            }
        }
        event.setEccNcOrderDO(orderDO);
        applicationEventPublisher.publishEvent(event);
    }

    /**
     * 初始化订单
     *
     * @param req             请求对象
     * @param channelTypeEnum 渠道类型
     * @return 初始化后的订单对象
     */
    private EccNcOrderDO initializeOrder(EccBaseGetCardReq req, EccChannelTypeEnum channelTypeEnum) {
        EccNcOrderDO order = new EccNcOrderDO();
        order.setOrderNo(LocalBizNoPlusUtils.genBillNo(BizNoPrefixEnum.ECC_ORDER_NUMBER_CARD.getPrefix()));
        order.setIdCardName(req.getIdCardName());
        order.setReqCardTime(LocalDateTime.now());
        order.setIdCard(req.getIdCardNo().toUpperCase());
        order.setPostProvinceCode(req.getPostProvinceCode());
        order.setPostProvince(req.getPostProvince());
        order.setPostCityCode(req.getPostCityCode());
        order.setPostCity(req.getPostCity());
        order.setPostDistrictCode(req.getPostDistrictCode());
        order.setPostDistrict(req.getPostDistrict());
        order.setAddress(req.getAddress());
        order.setContactPhone(req.getContactPhone());
        order.setPhone(null);
        order.setProvinceCode(null);
        order.setProvince(null);
        order.setCityCode(null);
        order.setCity(null);
        order.setPageUrl(req.getPageUrl());
        order.setChannelType(channelTypeEnum.getType());
        setChannelPram(req, order);
        setPhoneInfo(req, order);
        return order;
    }


    /**
     * 设置渠道参数
     *
     * @param cardReq 请求对象
     * @param orderDO 订单对象
     */
    private void setChannelPram(EccBaseGetCardReq cardReq, EccNcOrderDO orderDO) {
        if (cardReq instanceof EccInnerChannelGetCardReq) {
            setInnerChannelParam((EccInnerChannelGetCardReq) cardReq, orderDO);
        } else {
            setOuterChannelParam((EccOuterChannelGetCardReq) cardReq, orderDO);
        }
    }

    /**
     * 设置内部渠道参数
     *
     * @param req     请求对象
     * @param orderDO 订单对象
     */
    private void setInnerChannelParam(EccInnerChannelGetCardReq req, EccNcOrderDO orderDO) {
        // 内部渠道
        EccChannelDO channelDO = eccChannelDOService.getById(req.getChannelId());
        orderDO.setChannelType(EccChannelTypeEnum.INNER.getType());
        if (Objects.nonNull(channelDO)) {
            orderDO.setChannelId(channelDO.getChannelId());
            orderDO.setChannelName(channelDO.getChannelName());
        }
        setAdChannelParam(req, orderDO);
    }

    /**
     * 设置外部渠道参数
     *
     * @param req     请求对象
     * @param orderDO 订单对象
     */
    private void setOuterChannelParam(EccOuterChannelGetCardReq req, EccNcOrderDO orderDO) {
        // 外部渠道
        EccOuterChannelDO outerChannelDO = eccOuterChannelDOService.getById(req.getChannelId());
        orderDO.setChannelType(EccChannelTypeEnum.OUTER.getType());
        if (Objects.nonNull(outerChannelDO)) {
            orderDO.setChannelId(outerChannelDO.getOuterChannelId());
            orderDO.setChannelName(outerChannelDO.getChannelName());
        }
        orderDO.setChannelOrderNo(req.getChannelOrderNo());
        orderDO.setCallbackUrl(req.getCallbackUrl());
    }


    /**
     * 设置手机号码信息 以及归属地
     *
     * @param req     请求对象
     * @param orderDO 订单对象
     */
    private void setPhoneInfo(EccBaseGetCardReq req, EccNcOrderDO orderDO) {
        if (req instanceof EccInnerChannelGetCardSelectPhoneReq) {
            EccInnerChannelGetCardSelectPhoneReq selectPhoneReq = (EccInnerChannelGetCardSelectPhoneReq) req;
            orderDO.setPhone(selectPhoneReq.getPhone());
            orderDO.setProvinceCode(selectPhoneReq.getProvinceCode());
            orderDO.setCityCode(selectPhoneReq.getCityCode());
        } else if (req instanceof EccOuterChannelGetCardSelectPhoneReq) {
            EccOuterChannelGetCardSelectPhoneReq selectPhoneReq = (EccOuterChannelGetCardSelectPhoneReq) req;
            orderDO.setPhone(selectPhoneReq.getPhone());
            orderDO.setProvinceCode(selectPhoneReq.getProvinceCode());
            orderDO.setCityCode(selectPhoneReq.getCityCode());
        }
    }

    /**
     * 设置广告渠道参数
     *
     * @param req     请求对象
     * @param orderDO 订单对象
     */
    private void setAdChannelParam(EccInnerChannelGetCardReq req, EccNcOrderDO orderDO) {
        // 广告参数设置
        if (Objects.nonNull(req.getAdChannelCode())) {
            AdChannelDO adChannelDO = adChannelDOService.getExistingChannel(SysModuleEnum.ECC, req.getAdChannelCode());
            orderDO.setAdChannelId(adChannelDO.getAdChannelId());
            orderDO.setAdChannelName(adChannelDO.getAdChannelName());
            AdAgentPlatformEnum adAgentPlatformEnum = AdAgentPlatformEnum.of(req.getAdAgent());
            if (Objects.nonNull(adAgentPlatformEnum)) {
                orderDO.setAdAgentPlatform(adAgentPlatformEnum.getCode());
            }
            if (req.getAdExt() != null) {
                String adExtStr = req.getAdExt().toString();
                if (adExtStr.length() > 1024) {
                    log.error("广告扩展字段长度超过1024,忽略处理{}", adExtStr);
                } else {
                    orderDO.setAdExt(adExtStr);
                }
            }
        }
    }

    private EccSpEnum getSpEnumByProduct(EccProductDO productDO) {
        SpProdEnum spProdEnum = SpProdEnum.of(productDO.getSpProdId());
        if (Objects.isNull(spProdEnum)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "商品信息错误");
        }
        return spProdEnum.getSpEnum();
    }


    /**
     * 创建结果对象
     *
     * @param orderDO 订单对象
     * @return 结果对象
     */
    private EccGetCardResultBO createResult(EccNcOrderDO orderDO) {
        EccGetCardResultBO eccGetCardResultBO = new EccGetCardResultBO();
        eccGetCardResultBO.setStatusCode(orderDO.getOrderStatus());
        eccGetCardResultBO.setDesc(orderDO.getFailReason());
        eccGetCardResultBO.setPhone(orderDO.getPhone());
        eccGetCardResultBO.setOrderNo(orderDO.getOrderNo());
        eccGetCardResultBO.setSpOrderNo(orderDO.getSpOrderNo());
        return eccGetCardResultBO;
    }


    /**
     * 设置收货地址以及号码归属地
     *
     * @param orderDO 订单对象
     */
    private void setPostAreaAndPhoneArea(EccSpEnum spEnum, EccNcOrderDO orderDO) {
        boolean isChineseProvinceInfo = CharSequenceUtil.isNotBlank(orderDO.getPostProvince()) && CharSequenceUtil.isNotBlank(orderDO.getPostCity()) && CharSequenceUtil.isNotBlank(orderDO.getPostDistrict());
        boolean isCodeProvinceInfo = CharSequenceUtil.isNotBlank(orderDO.getPostProvinceCode()) && CharSequenceUtil.isNotBlank(orderDO.getPostCityCode()) && CharSequenceUtil.isNotBlank(orderDO.getPostDistrictCode());
        EccAreaDO provinceInfo = null;
        if (isChineseProvinceInfo) {
            provinceInfo = eccAreaService.getOneAreaByName(spEnum.getSpEnum(), orderDO.getPostProvince(), orderDO.getPostCity(), orderDO.getPostDistrict());
        } else if (isCodeProvinceInfo) {
            provinceInfo = eccAreaService.getOneAreaByCode(spEnum.getSpEnum(), orderDO.getPostProvinceCode(), orderDO.getPostCityCode(), orderDO.getPostDistrictCode());
        }
        if (Objects.isNull(provinceInfo)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "收货地址信息错误");
        }
        orderDO.setPostProvince(provinceInfo.getAreaName());
        orderDO.setPostProvinceCode(provinceInfo.getPostCode());

        EccAreaDO cityInfo = provinceInfo.getSubAreaList().get(0);
        orderDO.setPostCity(cityInfo.getAreaName());
        orderDO.setPostCityCode(cityInfo.getPostCode());

        EccAreaDO districtInfo = cityInfo.getSubAreaList().get(0);
        orderDO.setPostDistrict(districtInfo.getAreaName());
        orderDO.setPostDistrictCode(districtInfo.getPostCode());

        if (orderDO.getPhone() != null) {
            orderDO.setSelectType(PhoneNumSelectType.USER_SELECT.getCode());
            EccAreaDO location = eccAreaService.getOneAreaByNumCode(spEnum.getSpEnum(), orderDO.getProvinceCode(), orderDO.getCityCode());
            if (Objects.isNull(location)) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "号码归属地信息错误");
            }
            // 验证省市是否匹配 再设置归属地中文名字
            orderDO.setProvince(location.getAreaName());
            EccAreaDO cityLocationInfo = location.getSubAreaList().get(0);
            orderDO.setCity(cityLocationInfo.getAreaName());
        } else {
            orderDO.setSelectType(PhoneNumSelectType.RANDOM_SELECT.getCode());
        }
    }


    public boolean isNumberCardOrder(String orderNo) {
        return orderNo.startsWith(BizNoPrefixEnum.ECC_ORDER_NUMBER_CARD.getPrefix());
    }

    /**
     * 获取领卡的锁
     *
     * @param idCardNo 身份证号
     * @return 获取成功返回的不为空
     */
    private RLock acquireLock(String idCardNo) {
        return cacheHandler.getReceiveSimCardLock(idCardNo);
    }


    /**
     * 释放锁
     *
     * @param lock 锁对象
     */
    private void releaseLock(RLock lock) {
        if (Objects.nonNull(lock) && lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    /**
     * 获取外部渠道订单数量
     *
     * @param outChannelId   外部渠道ID
     * @param channelOrderNo 渠道订单号
     * @return 订单数量
     */
    public int getOuterChannelOrderCount(Long outChannelId, String channelOrderNo) {
        return eccNcOrderService.getOuterChannelOrderCount(outChannelId, EccChannelTypeEnum.OUTER, channelOrderNo);
    }

    public EccAggregatePageRegionResp aggregatePage(Long aggregatePageId) {
        if (Objects.isNull(aggregatePageId)) {
            // 默认聚合页ID
            aggregatePageId = 1L;
        }
        EccAggregatePageDO pageDO = aggregatePageService.getById(aggregatePageId);
        BizException.assertCheck(pageDO != null, BaseErrorCodeEnum.PARAMS_ERROR, "聚合页配置不存在");
        List<EccAggregatePageProductDO> province2ProductList = aggregatePageProductService.listByPageId(aggregatePageId);
        Set<Long> productIds = province2ProductList.stream()
                .map(EccAggregatePageProductDO::getProductId)
                .collect(Collectors.toSet());

        List<EccProductDO> productDOList = eccProductDOService.listByIds(productIds).stream()
                .filter(product -> UpOffStatusEnum.UP.getCode().equals(product.getProdStatus()))
                .collect(Collectors.toList());

        Set<Long> validProductIds = productDOList.stream()
                .map(EccProductDO::getProdId)
                .collect(Collectors.toSet());

        province2ProductList = province2ProductList.stream()
                .filter(provinceProd -> validProductIds.contains(provinceProd.getProductId()))
                .collect(Collectors.toList());


        EccAggregatePageRegionResp resp = new EccAggregatePageRegionResp();

        String ip = ServletUtil.getClientIP(HttpServletRequestUtil.getCurrentRequest());
        Ip2RegionResp ip2RegionResp = ip2RegionManager.ip2Region(ip);
        resp.setIp2Region(ip2RegionResp);

        List<EccAggregatePageRegionResp.Province2ProductResp> province2Products = new ArrayList<>();
        province2ProductList.sort(Comparator.comparing(EccAggregatePageProductDO::getSort));
        for (EccAggregatePageProductDO provinceProd : province2ProductList) {
            EccAggregatePageRegionResp.Province2ProductResp province2ProductResp = new EccAggregatePageRegionResp.Province2ProductResp();
            province2ProductResp.setProvinceAdCode(provinceProd.getProvinceAdCode());
            province2ProductResp.setProvinceName(provinceProd.getProvinceName());
            province2ProductResp.setProductId(provinceProd.getProductId());
            province2ProductResp.setDefault(provinceProd.getPageDefault() == 1);
            province2Products.add(province2ProductResp);
        }
        resp.setProvince2Products(province2Products);

        List<ProdRegionResp> prodRegions = new ArrayList<>();
        for (EccProductDO eccProductDO : productDOList) {
            ProdRegionResp regionResp = new ProdRegionResp();
            regionResp.setProductId(eccProductDO.getProdId());
            regionResp.setProdCode(eccProductDO.getProdCode());
            regionResp.setRegionType(eccProductDO.getRegionType());
            if (eccProductDO.getRegionList() != null) {
                regionResp.setRegionList(JSON.parseArray(eccProductDO.getRegionList(), EccPostAreaResp.class));
            }
            prodRegions.add(regionResp);
        }

        resp.setProdRegions(prodRegions);
        return resp;

    }

    public AppEccProdConfigResp productConfig(String productCode, Long pageId) {
        AppEccProductPageResp pageConfig = new AppEccProductPageResp();
        EccProductDO productDo = eccProductDOService.getByProdCode(productCode);
        EccProductPageDO pageDO = null;
        if (Objects.nonNull(pageId)) {
            pageDO = eccProductPageService.getById(pageId);
            if (!Objects.equals(pageDO.getProductId(), productDo.getProdId())) {
                pageDO = null;
            }
        }
        if (Objects.isNull(pageDO)) {
            pageDO = eccProductPageService.getEnablePageByProductCode(productCode);
        }
        EccProductPageResp pageResp = EccProductPageResp.build(pageDO);
        if (pageResp != null) {
            pageConfig = MapstructUtils.convertNotNull(pageResp, AppEccProductPageResp.class);
            if (pageResp.getLicense() != null) {
                pageConfig.setLicenseImg(pageResp.getLicense().getUri());
            }
        }
        AppEccProdConfigResp resp = new AppEccProdConfigResp();
        resp.setPageConfig(pageConfig);


        if (productDo != null) {
            resp.setProdId(productDo.getProdId());
            resp.setProdName(productDo.getProdName());
            SpProdEnum spProdEnum = SpProdEnum.of(productDo.getSpProdId());
            EccSpEnum eccSpEnum = spProdEnum.getSpEnum();
            if (EccSpEnum.UNICOM_ZOP == eccSpEnum) {
                resp.setZopProduct(true);
            } else {
                resp.setZopProduct(false);
            }
            resp.setOperator(eccSpEnum.getSpEnum().getCode());
            resp.setRegionType(productDo.getRegionType());
            if (productDo.getRegionList() != null) {
                resp.setRegionList(JSON.parseArray(productDo.getRegionList(), EccPostAreaResp.class));
            }
        }
        return resp;
    }
}
