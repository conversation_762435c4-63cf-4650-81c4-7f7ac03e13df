package com.yuelan.hermes.quanyi.config.satoken;

/**
 * <AUTHOR> 2024/6/12 上午11:35
 */

import cn.dev33.satoken.stp.StpInterface;
import com.yuelan.hermes.quanyi.biz.manager.AdminManager;
import com.yuelan.hermes.quanyi.common.enums.SysRoleEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.AdminDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 自定义权限加载接口实现类
 */
@Component    // 保证此类被 SpringBoot 扫描，完成 Sa-Token 的自定义权限验证扩展
public class StpInterfaceImpl implements StpInterface {

    @Autowired
    AdminManager adminManager;

    /**
     * 返回一个账号所拥有的权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        return null;
    }

    /**
     * 返回一个账号所拥有的角色标识集合 (权限与角色可分开校验)
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        AdminDO admin = adminManager.findById(Long.valueOf(loginId.toString()));
        List<String> list = new ArrayList<>();
        Integer role = admin.getRole();
        SysRoleEnum sysRole = SysRoleEnum.getRoleByCode(role);
        if (Objects.nonNull(sysRole)) {
            list.add(sysRole.getName());
        }
        return list;
    }

}
