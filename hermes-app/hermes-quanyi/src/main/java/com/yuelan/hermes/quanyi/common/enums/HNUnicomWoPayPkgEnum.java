package com.yuelan.hermes.quanyi.common.enums;

import com.yuelan.hermes.quanyi.common.interfaces.PayChannelPkgInter;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum HNUnicomWoPayPkgEnum implements PayChannelPkgInter {

    //
    hn_quan_yi(1, "湖南云盘流量5G加油包-16元档（立即）", null),
    hn_ct(2, "湖南安全畅听出行权益礼包-29元档（立即）", null),
    hn_phone(3, "湖南云手机权益流量包29元档-立即（标准版）", null),
    hn_cloud_disk(4, "湖南U+会员云盘版-29元档（立即）", null),
    ;
    private final Integer pkgId;
    private final String name;
    private final Integer uniqueId;

    public static HNUnicomWoPayPkgEnum of(Integer pkgId) {
        for (HNUnicomWoPayPkgEnum value : values()) {
            if (value.pkgId.equals(pkgId)) {
                return value;
            }
        }
        return null;
    }


    @Override
    public Integer getChannelPkgId() {
        return pkgId;
    }

    @Override
    public String getChannelPkgName() {
        return name;
    }

    @Override
    public Integer getUniquePkgId() {
        return uniqueId;
    }
}
