package com.yuelan.hermes.quanyi.common.util;

import cn.hutool.core.util.XmlUtil;
import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.crypto.digest.Digester;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.yuelan.hermes.quanyi.remote.softgame.response.SoftGameCardInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import java.security.Key;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 软游通v2工具类
 *
 * <AUTHOR> 2025/7/31
 * @since 2025/7/31
 */
@Slf4j
public class SoftGameUtil {

    private static final String DESC3 = "DESede";
    private static final String ALGORITHM = "DESede/ECB/PKCS5Padding";

    /**
     * 生成订单提交签名
     * md5(businessId + userOrderId+ goodsId + goodsNum + orderIp + key)
     */
    public static String generateOrderSubmitSign(String businessId, String userOrderId,
                                                 String goodsId, String goodsNum,
                                                 String orderIp, String key) {
        String signData = businessId + userOrderId + goodsId + goodsNum +
                (orderIp != null ? orderIp : "") + key;
        log.info("软游通v2订单提交签名原串: {}", signData);
        return new Digester(DigestAlgorithm.MD5).digestHex(signData).toLowerCase();
    }

    /**
     * 生成订单查询签名
     * md5(businessId + userOrderId + key)
     */
    public static String generateOrderQuerySign(String businessId, String userOrderId, String key) {
        String signData = businessId + userOrderId + key;
        log.info("软游通v2订单查询签名原串: {}", signData);
        return new Digester(DigestAlgorithm.MD5).digestHex(signData).toLowerCase();
    }

    /**
     * 生成异步通知签名
     * lcase(md5(businessId + userOrderId + status + key))
     */
    public static String generateNotifySign(String businessId, String userOrderId,
                                            String status, String key) {
        String signData = businessId + userOrderId + status + key;
        log.info("软游通v2异步通知签名原串: {}", signData);
        return new Digester(DigestAlgorithm.MD5).digestHex(signData).toLowerCase();
    }

    /**
     * 验证签名
     */
    public static boolean verifySign(String expectedSign, String actualSign) {
        return Objects.equals(expectedSign, actualSign);
    }

    /**
     * 3DES解密卡密信息
     *
     * @param encryptedKmInfo 加密的卡密信息
     * @param key             解密密钥
     * @return 解密后的卡密信息列表
     */
    public static List<SoftGameCardInfo> decryptCardInfo(String encryptedKmInfo, String key) {
        try {
            String decryptedJson = des3Decrypt(encryptedKmInfo, key, "utf-8");
            log.info("软游通v2卡密解密结果: {}", decryptedJson);
            return JSON.parseObject(decryptedJson, new TypeReference<List<SoftGameCardInfo>>() {
            });
        } catch (Exception e) {
            log.error("软游通v2卡密解密失败", e);
            return null;
        }
    }

    /**
     * 3DES解密
     *
     * @param src      待解密数据
     * @param key      密钥
     * @param encoding 编码格式
     * @return 解密结果
     */
    public static String des3Decrypt(String src, String key, String encoding) {
        try {
            byte[] s = Base64.decodeBase64(src);
            byte[] b = Base64.decodeBase64(key);
            DESedeKeySpec spec = new DESedeKeySpec(b);
            SecretKeyFactory keyfactory = SecretKeyFactory.getInstance(DESC3);
            Key deskey = keyfactory.generateSecret(spec);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, deskey);
            byte[] decryptData = cipher.doFinal(s);
            return new String(decryptData, encoding);
        } catch (Exception e) {
            log.error("3DES解密失败", e);
            return null;
        }
    }

    /**
     * 3DES加密
     *
     * @param src 待加密数据
     * @param key 密钥
     * @return 加密结果
     */
    public static String des3Encrypt(String src, String key) {
        try {
            byte[] b = Base64.decodeBase64(key);
            DESedeKeySpec spec = new DESedeKeySpec(b);
            SecretKeyFactory keyfactory = SecretKeyFactory.getInstance(DESC3);
            Key deskey = keyfactory.generateSecret(spec);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, deskey);
            b = cipher.doFinal(src.getBytes());
            return Base64.encodeBase64String(b);
        } catch (Exception e) {
            log.error("3DES加密失败", e);
            return null;
        }
    }

    /**
     * 对象转表单参数，自动过滤空值
     *
     * @param obj 要转换的对象
     * @return 表单参数Map
     */
    public static Map<String, Object> objectToFormParams(Object obj) {
        // 使用fastjson2转换，然后过滤空值
        Map<String, Object> params = JSONObject.from(obj);
        params.entrySet().removeIf(entry -> entry.getValue() == null);
        return params;
    }

    /**
     * XML转Map - 支持多层级嵌套
     *
     * @param xmlBody XML字符串
     * @return Map<String, Object> 支持嵌套结构
     */
    public static Map<String, Object> parseXmlToMap(String xmlBody) {
        try {
            Document document = XmlUtil.parseXml(xmlBody);
            Element root = document.getDocumentElement();
            return elementToMap(root);
        } catch (Exception e) {
            log.error("XML解析失败", e);
            return new HashMap<>();
        }
    }

    /**
     * XML转简单Map - 只支持一层级（兼容旧版本）
     *
     * @param xmlBody XML字符串
     * @return Map<String, String>
     */
    public static Map<String, String> parseXmlToSimpleMap(String xmlBody) {
        try {
            Document document = XmlUtil.parseXml(xmlBody);
            Element root = document.getDocumentElement();
            Map<String, String> result = new HashMap<>();

            NodeList childNodes = root.getChildNodes();
            for (int i = 0; i < childNodes.getLength(); i++) {
                if (childNodes.item(i) instanceof Element) {
                    Element element = (Element) childNodes.item(i);
                    result.put(element.getTagName(), element.getTextContent());
                }
            }
            return result;
        } catch (Exception e) {
            log.error("XML解析失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 递归解析XML元素为Map
     *
     * @param element XML元素
     * @return Map<String, Object>
     */
    private static Map<String, Object> elementToMap(Element element) {
        Map<String, Object> result = new HashMap<>();
        NodeList childNodes = element.getChildNodes();

        for (int i = 0; i < childNodes.getLength(); i++) {
            if (childNodes.item(i) instanceof Element) {
                Element childElement = (Element) childNodes.item(i);
                String tagName = childElement.getTagName();

                // 检查是否有子元素
                NodeList grandChildren = childElement.getChildNodes();
                boolean hasElementChildren = false;
                for (int j = 0; j < grandChildren.getLength(); j++) {
                    if (grandChildren.item(j) instanceof Element) {
                        hasElementChildren = true;
                        break;
                    }
                }

                if (hasElementChildren) {
                    // 有子元素，递归解析
                    result.put(tagName, elementToMap(childElement));
                } else {
                    // 没有子元素，直接取文本内容
                    result.put(tagName, childElement.getTextContent());
                }
            }
        }

        return result;
    }

    /**
     * Map转对象 - 通用方法，支持多层级
     *
     * @param map   数据Map
     * @param clazz 目标类型
     * @return 转换后的对象
     */
    public static <T> T mapToObject(Map<String, ?> map, Class<T> clazz) {
        try {
            // 先转JSON再转对象，利用fastjson2的强大转换能力
            String jsonStr = JSON.toJSONString(map);
            return JSON.parseObject(jsonStr, clazz);
        } catch (Exception e) {
            log.error("Map转对象失败", e);
            return null;
        }
    }

    /**
     * 简单Map转对象 - 兼容方法
     *
     * @param map   简单数据Map
     * @param clazz 目标类型
     * @return 转换后的对象
     */
    public static <T> T simpleMapToObject(Map<String, String> map, Class<T> clazz) {
        return mapToObject(map, clazz);
    }
}
