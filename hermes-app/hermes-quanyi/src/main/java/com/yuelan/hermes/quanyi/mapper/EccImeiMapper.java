package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccImeiDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2025/6/18
 * @since 2025/6/18
 */
public interface EccImeiMapper extends MPJBaseMapper<EccImeiDO> {
    default List<EccImeiDO> selectBatchByImei(List<String> itemList) {
        return selectList(Wrappers.lambdaQuery(EccImeiDO.class)
                .in(EccImeiDO::getImei, itemList));
    }

    void saveBatchIgnore(@Param("list") List<EccImeiDO> list);

    default Long countByBatchId(Long batchId) {
        return selectCount(Wrappers.lambdaQuery(EccImeiDO.class)
                .eq(EccImeiDO::getBatchId, batchId));
    }
}