package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccImeiDO;

import java.util.List;

/**
 * <AUTHOR> 2025/6/18
 * @since 2025/6/18
 */
public interface EccImeiMapper extends MPJBaseMapper<EccImeiDO> {
    default List<EccImeiDO> selectBatchByImei(List<String> itemList) {
        return selectList(Wrappers.lambdaQuery(EccImeiDO.class)
                .in(EccImeiDO::getImei, itemList));
    }
}