package com.yuelan.hermes.quanyi.common.pojo.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> 2025/5/6
 * @since 2025/5/6
 * 权益组合包
 */
@Schema(description = "权益组合包")
@Data
@TableName(value = "benefits_package")
public class BenefitsPackageDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "package_id", type = IdType.AUTO)
    @Schema(description = "主键id")
    private Long packageId;

    /**
     * 权益包名字
     */
    @TableField(value = "package_name")
    @Schema(description = "权益包名字")
    private String packageName;

    /**
     * 用户侧展示名
     */
    @TableField(value = "user_display_name")
    @Schema(description = "用户侧展示名")
    private String userDisplayName;

    /**
     * 唯一编码
     */
    @TableField(value = "package_code")
    @Schema(description = "唯一编码")
    private String packageCode;

    /**
     * 兑换有效期天数
     */
    @TableField(value = "redemption_period")
    @Schema(description = "兑换有效期天数")
    private Integer redemptionPeriod;

    /**
     * 单位分
     */
    @TableField(value = "selling_price")
    @Schema(description = "单位分")
    private Integer sellingPrice;

    /**
     * 最大兑换次数（不包含即时发放的权益数量）
     */
    @TableField(value = "redemption_limit")
    @Schema(description = "最大兑换次数（不包含即时发放的权益数量）")
    private Integer redemptionLimit;

    /**
     * 状态：0-下架，1-上架
     */
    @TableField(value = "`status`")
    @Schema(description = "状态：0-下架，1-上架")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @TableField(exist = false)
    @Schema(description = "包含的权益")
    public List<BenefitItemDO> benefits;
}