package com.yuelan.hermes.quanyi.biz.handler.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.commons.enums.SupplierEnum;
import com.yuelan.hermes.quanyi.biz.manager.param.ParameterDefinition;
import com.yuelan.hermes.quanyi.common.constant.SupplierGoodsParameters;
import com.yuelan.hermes.quanyi.common.interfaces.BenefitSupplier;
import com.yuelan.hermes.quanyi.common.pojo.bo.DispatchResultDTO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderExtensionDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.RytProperties;
import com.yuelan.hermes.quanyi.common.pojo.properties.SoftGameProperties;
import com.yuelan.hermes.quanyi.remote.SoftGameManager;
import com.yuelan.hermes.quanyi.remote.softgame.request.SoftGameOrderSubmitReq;
import com.yuelan.hermes.quanyi.remote.softgame.response.SoftGameBaseResponse;
import com.yuelan.result.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> 2025/4/24
 * @since 2025/4/24
 * <p>
 * 软游通权益供应商
 */
@Slf4j
@Component
@AllArgsConstructor
public class RytBenefitSupplier implements BenefitSupplier {

    private final SoftGameManager softgameManager;
    private final SoftGameProperties softGameProperties;
    private final RytProperties rytProperties;

    @Override
    public SupplierEnum getSupplier() {
        return SupplierEnum.RYT;
    }

    @Override
    public Set<ParameterDefinition> goodsParams() {
        Set<ParameterDefinition> param = new HashSet<>();
        param.add(SupplierGoodsParameters.GOODS_CODE);
        param.add(SupplierGoodsParameters.PAR_VALUE);
        return param;
    }

    @Override
    public DispatchResultDTO dispatchBenefits(BenefitItemOrderDO orderDO, BenefitItemDO benefitItemDO) {
        BenefitOrderExtensionDO extension = orderDO.getOrderExtension();
        StringBuilder reqBuild = new StringBuilder();
        StringBuilder respBuild = new StringBuilder();
        SoftGameBaseResponse<String> stringSoftGameBaseResponse;
        boolean dispatchSuccess = false;
        String errorMessage = null;
        String supplierOrderNo = null;
        boolean realTime = false;
        try {
            stringSoftGameBaseResponse = submitRechargeOrder(orderDO, benefitItemDO, reqBuild, respBuild);
            if (Objects.nonNull(stringSoftGameBaseResponse) && stringSoftGameBaseResponse.isSuccess()) {
                dispatchSuccess = true;
                realTime = stringSoftGameBaseResponse.isRealTimeSuccess();
            } else {
                errorMessage = Objects.isNull(stringSoftGameBaseResponse) ? null : stringSoftGameBaseResponse.getMes();
            }
        } catch (BizException e) {
            errorMessage = String.format("软游通下单业务失败: %s", e.getMessage());
            log.error("软游通下单失败, 订单号: {}, 失败信息: {}", extension.getChannelOrderNo(), e.getMessage());
        } catch (Exception e) {
            errorMessage = "软游通下单未知错误";
            log.error("软游通下单未知错误, 订单号: {}", extension.getChannelOrderNo(), e);
        }
        // 软游通不是实时到账 要等待回调
        return new DispatchResultDTO(dispatchSuccess, realTime)
                .setSupplierRequest(reqBuild.length() > 0 ? reqBuild.toString() : null)
                .setSupplierResponse(respBuild.length() > 0 ? respBuild.toString() : null)
                .setSupplierOrderNo(supplierOrderNo)
                .setErrorMessage(errorMessage);
    }

    public SoftGameBaseResponse<String> submitRechargeOrder(BenefitItemOrderDO orderDO, BenefitItemDO benefitItemDO, StringBuilder reqBuild, StringBuilder respBuild) {
        final String callBackUrl = softGameProperties.getNoticeUrl();

        BenefitOrderExtensionDO extension = orderDO.getOrderExtension();
        String requestOrderNo = orderDO.getSupplierRequestOrder();
        String account = String.valueOf(extension.getMobile());

        // 解析产品配置参数
        String supplierGoodsParam = benefitItemDO.getSupplierGoodsParam();

        JSONObject param = JSONObject.parseObject(supplierGoodsParam);
        String goodsCode = param.getString(SupplierGoodsParameters.GOODS_CODE.getKey());
        BigDecimal pairValue = param.getBigDecimal(SupplierGoodsParameters.PAR_VALUE.getKey());

        SoftGameOrderSubmitReq req = new SoftGameOrderSubmitReq();
        req.setUserOrderId(requestOrderNo);
        req.setGoodsId(goodsCode);
        req.setUserName(account);
        req.setAcctType("手机号");
        req.setGameName(benefitItemDO.getBenefitName());
        req.setGoodsNum(pairValue.stripTrailingZeros().toPlainString());
        req.setNoticeUrl(callBackUrl);

        SoftGameBaseResponse<String> response = softgameManager.submitOrder(req, reqBuild, respBuild);

        if (response.isSuccess()) {
            log.info("软游通v2订单提交成功: 订单号={}", requestOrderNo);
        } else {
            log.error("软游通v2订单提交失败: 订单号={}, 错误={}", requestOrderNo, response.getMes());
        }
        return response;
    }

    private DispatchResultDTO dev(String supplierGoodsParam) {
        if (supplierGoodsParam.contains("yuelan_success")) {
            return new DispatchResultDTO(true, true)
                    .setSupplierRequest("ryt request")
                    .setSupplierResponse("ryt response")
                    .setSupplierOrderNo("ryt" + IdUtil.fastSimpleUUID())
                    .setErrorMessage(null);
        } else {
            return new DispatchResultDTO(false, false)
                    .setSupplierRequest("ryt request")
                    .setSupplierResponse("ryt response")
                    .setSupplierOrderNo("ryt" + IdUtil.fastSimpleUUID())
                    .setErrorMessage("ryt err");
        }

    }

}
