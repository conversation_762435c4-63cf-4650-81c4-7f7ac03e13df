package com.yuelan.hermes.quanyi.biz.handler.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.commons.enums.SupplierEnum;
import com.yuelan.hermes.quanyi.biz.manager.param.ParameterDefinition;
import com.yuelan.hermes.quanyi.common.constant.SupplierGoodsParameters;
import com.yuelan.hermes.quanyi.common.interfaces.BenefitSupplier;
import com.yuelan.hermes.quanyi.common.pojo.bo.DispatchResultDTO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderExtensionDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.RytProperties;
import com.yuelan.hermes.quanyi.remote.RytManager;
import com.yuelan.hermes.quanyi.remote.response.RytSendOrderRsp;
import com.yuelan.result.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> 2025/4/24
 * @since 2025/4/24
 * <p>
 * 软游通权益供应商
 */
@Slf4j
@Component
@AllArgsConstructor
public class RytBenefitSupplier implements BenefitSupplier {

    private final RytManager rytManager;
    private final RytProperties rytProperties;

    @Override
    public SupplierEnum getSupplier() {
        return SupplierEnum.RYT;
    }

    @Override
    public Set<ParameterDefinition> goodsParams() {
        Set<ParameterDefinition> param = new HashSet<>();
        param.add(SupplierGoodsParameters.GOODS_CODE);
        return param;
    }

    @Override
    public DispatchResultDTO dispatchBenefits(BenefitItemOrderDO orderDO, BenefitItemDO benefitItemDO) {
        BenefitOrderExtensionDO extension = orderDO.getOrderExtension();
        StringBuilder reqBuild = new StringBuilder();
        StringBuilder respBuild = new StringBuilder();
        RytSendOrderRsp rytSendOrderRsp;
        boolean dispatchSuccess = false;
        String errorMessage = null;
        String supplierOrderNo = null;

        try {
            String requestOrderNo = orderDO.getSupplierRequestOrder();
            String account = String.valueOf(extension.getMobile());

            // 解析产品配置参数
            String supplierGoodsParam = benefitItemDO.getSupplierGoodsParam();

            if (!AppConstants.isReal()) {
                // 测试环境模拟成功
                return dev(supplierGoodsParam);
            }
            JSONObject param = JSONObject.parseObject(supplierGoodsParam);
            String goodsCode = param.getString(SupplierGoodsParameters.GOODS_CODE.getKey());

            rytSendOrderRsp = rytManager.sendOrder(requestOrderNo, account, goodsCode, rytProperties.getCommonOrderCallBackUrl(), reqBuild, respBuild);
            // 不抛错就是成功
            dispatchSuccess = true;
            if (Objects.nonNull(rytSendOrderRsp) && Objects.nonNull(rytSendOrderRsp.getOrderId())) {
                supplierOrderNo = String.valueOf(rytSendOrderRsp.getOrderId());
            }
        } catch (BizException e) {
            errorMessage = String.format("软游通下单业务失败: %s", e.getMessage());
            log.error("软游通下单失败, 订单号: {}, 失败信息: {}", extension.getChannelOrderNo(), e.getMessage());
        } catch (Exception e) {
            errorMessage = "软游通下单未知错误";
            log.error("软游通下单未知错误, 订单号: {}", extension.getChannelOrderNo(), e);
        }
        // 成功失败就是没有实时到账 如果成功也要等回调
        boolean realTime = false;
        return new DispatchResultDTO(dispatchSuccess, realTime)
                .setSupplierRequest(reqBuild.length() > 0 ? reqBuild.toString() : null)
                .setSupplierResponse(respBuild.length() > 0 ? respBuild.toString() : null)
                .setSupplierOrderNo(supplierOrderNo)
                .setErrorMessage(errorMessage);
    }

    private DispatchResultDTO dev(String supplierGoodsParam) {
        if (supplierGoodsParam.contains("yuelan_success")) {
            return new DispatchResultDTO(true, true)
                    .setSupplierRequest("ryt request")
                    .setSupplierResponse("ryt response")
                    .setSupplierOrderNo("ryt" + IdUtil.fastSimpleUUID())
                    .setErrorMessage(null);
        } else {
            return new DispatchResultDTO(false, false)
                    .setSupplierRequest("ryt request")
                    .setSupplierResponse("ryt response")
                    .setSupplierOrderNo("ryt" + IdUtil.fastSimpleUUID())
                    .setErrorMessage("ryt err");
        }

    }

}
