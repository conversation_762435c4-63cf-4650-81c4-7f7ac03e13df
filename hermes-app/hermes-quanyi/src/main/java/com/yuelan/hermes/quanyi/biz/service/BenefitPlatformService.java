package com.yuelan.hermes.quanyi.biz.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuelan.hermes.commons.enums.BizNoPrefixEnum;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.commons.enums.PayStatusEnum;
import com.yuelan.hermes.commons.enums.PreorderStatusEnum;
import com.yuelan.hermes.commons.util.ExecutorServiceUtils;
import com.yuelan.hermes.quanyi.biz.handler.BenefitPayHandler;
import com.yuelan.hermes.quanyi.biz.handler.CacheHandler;
import com.yuelan.hermes.quanyi.biz.manager.BenefitOrderManager;
import com.yuelan.hermes.quanyi.common.enums.BenefitPayChannelEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitUnicomPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitPlatformDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.util.LocalBizNoPlusUtils;
import com.yuelan.hermes.quanyi.common.util.PhoneSearch6Db;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.controller.request.benefitplatform.BenefitPlatformBaseReq;
import com.yuelan.hermes.quanyi.controller.request.benefitplatform.BenefitPlatformNotifyMediaReq;
import com.yuelan.hermes.quanyi.controller.request.benefitplatform.BenefitPlatformSmsCodeReq;
import com.yuelan.hermes.quanyi.controller.request.benefitplatform.BenefitPlatformUnicomReadReq;
import com.yuelan.hermes.quanyi.controller.response.benefitplatform.BenefitPlatformOrderRes;
import com.yuelan.hermes.quanyi.mapper.BenefitPlatformMapper;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/7/19
 * @description:
 */

@Service
@Slf4j
public class BenefitPlatformService {

    private static final int TIME_OUT = 10000;
    @Resource
    private BenefitOrderManager benefitOrderManager;

    @Resource
    private CacheHandler cacheHandler;

    @Resource
    private BenefitOrderDOService benefitOrderDOService;
    @Resource
    private BenefitPlatformMapper benefitPlatformMapper;
    @Resource
    private BenefitPlatformDOService benefitPlatformDOService;
    @Resource
    private BenefitBlackUserDOService benefitBlackUserDOService;

    private final Logger bizErrLog = LoggerFactory.getLogger("biz-error");

    private static PhoneSearch6Db phoneSearch6Db = PhoneSearch6Db.getInstance();

    /**
     * 对接平台请求权益平台向运营商发送验证码
     */
    public BenefitPayResultBO sendSmsCode(BenefitPlatformBaseReq req) {
        String phone = req.getMobile();
        RLock lock = cacheHandler.getBenefitPlatformOrderLock(phone);
        if (Objects.isNull(lock)) {
            throw BizException.create(BizErrorCodeEnum.SMS_SEND_BUSY);
        }
        try {
            BenefitProductDO productDO = benefitOrderManager.getValidProduct(req.getProdCode());
            productDO.setDistributionChannel(this.getDistributionChannel(req.getAppId(), req.getCommonId()));

            this.checkBlackUser(phone, req.getPackageName(), productDO, null);

            BenefitPayHandler payChannel = benefitOrderManager.getPayExistChannelHandler(BenefitPayChannelEnum.of(productDO.getPayChannelId()));
            BenefitPayResultBO resultBO = new BenefitPayResultBO();
            resultBO.setSuccess(Boolean.FALSE);

            UserBenefitOrderReq userBenefitOrderReq = BeanUtil.copyProperties(req, UserBenefitOrderReq.class);
            // 发送短信
            boolean sendSmsCode = payChannel.orderSendSmsCode(userBenefitOrderReq, productDO);
            if (sendSmsCode) {
                resultBO.setMessage("发送成功");
            } else {
                resultBO.setMessage("发送失败");
            }
            resultBO.setSuccess(sendSmsCode);
            return resultBO;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 创建下单
     */
    public BenefitPlatformOrderRes createOrder(BenefitPlatformSmsCodeReq req) {
        return this.getBenefitPlatformOrderRes(req);
    }

    public void checkBlackUser(String mobile, String packageName, BenefitProductDO productDO, BenefitOrderDO orderDO) {
        if (StringUtils.isBlank(mobile)) {
            return;
        }
        try {
            // 校验手机号是否是黑名单
            boolean valid = benefitBlackUserDOService.getValidUserByPhone(productDO.getPayChannelId(), mobile);
            if (valid) {
                throw BizException.create(BizErrorCodeEnum.BLACK_USER_PHONE);
            }
            // 校验包名是否是黑名单
            boolean byPackageName = benefitBlackUserDOService.getValidUserByPackageName(productDO.getPayChannelId(), productDO.getPayChannelPkgId(), packageName);
            if (byPackageName) {
                throw BizException.create(BizErrorCodeEnum.BLACK_USER_PACKAGE_NAME);
            }
        } catch (BizException e) {
            if (orderDO != null) {
                orderDO.setPayStatus(PayStatusEnum.FAIL.getCode());
                orderDO.setPayNotifyContent(JSON.toJSONString(BizResult.error(e)));
            }
            throw e;
        }
    }

    /**
     * 创建订单
     *
     * @param appId    媒体appId
     * @param commonId 推荐商品的id,通常是运营商的商品编号
     * @param phone    手机号
     * @param prodCode 产品编码,在平台上产品的编码
     */
    private BenefitOrderDO getBenefitOrderByPhoneAndProduct(String appId, String commonId, String phone, String prodCode, BenefitPlatformBaseReq req) {
        BenefitProductDO dbProductDO = benefitOrderManager.getValidProduct(prodCode);
        // 校验有没有支付成功,如果有,不能再次订购.
        benefitOrderManager.checkUserCanOrder(phone, dbProductDO.getProdId());
        // 获取手机号的归属地
        Integer areaCode = phoneSearch6Db.queryCityAdcode(phone);

        BenefitPayHandler payHandler = benefitOrderManager.getPayExistChannelHandler(BenefitPayChannelEnum.of(dbProductDO.getPayChannelId()));
        String distributionChannel = this.getDistributionChannel(appId, commonId);
        BenefitOrderDO orderDO = new BenefitOrderDO();
        orderDO.setOrderNo(LocalBizNoPlusUtils.genBillNo(BizNoPrefixEnum.ORDER_BENEFIT.getPrefix()));
        orderDO.setDistributionChannel(distributionChannel);
        orderDO.setPayChannelId(dbProductDO.getPayChannelId());
        orderDO.setPayChannel(payHandler.getChannel().getName());
        orderDO.setPayChannelPkgId(dbProductDO.getPayChannelPkgId());
        String pkgName = payHandler.getChannel().findPkgName(dbProductDO.getPayChannelPkgId());
        orderDO.setPayChannelPkgName(pkgName);
        orderDO.setPhone(phone);
        orderDO.setProdId(dbProductDO.getProdId());
        orderDO.setProdName(dbProductDO.getProdName());
        orderDO.setRedeemLimit(dbProductDO.getRedeemLimit());
        orderDO.setRedeemRemain(dbProductDO.getRedeemLimit());
        orderDO.setCycleType(dbProductDO.getCycleType());
        orderDO.setCycleRedeemLimit(dbProductDO.getCycleRedeemLimit());
        orderDO.setOrderAmount(dbProductDO.getPrice());
        orderDO.setPayStatus(PayStatusEnum.DEFAULT.getCode());
        orderDO.setPreorderStatus(PreorderStatusEnum.DEFAULT.getCode());
        orderDO.setOrderStatus(OrderStatusEnum.PROCESSING.getCode());
        orderDO.setCityAdcode(areaCode);
        // 包名
        if (StringUtils.isNotBlank(req.getPackageName())) {
            orderDO.setPackageName(req.getPackageName().substring(0, Math.min(64, req.getPackageName().length())));
        }

        // 推广渠道
        setChannelInfo(appId, orderDO);
        benefitOrderDOService.save(orderDO);
        return orderDO;
    }

    @NotNull
    private String getDistributionChannel(String appId, String commonId) {
        if (StringUtils.isBlank(commonId)) {
            return appId;
        }
        return appId + StrUtil.DASHED + commonId;
    }

    /**
     * 平台媒体回调
     *
     * @param orderDO 订单实体
     * @param msg     成功或错误信息
     */
    public void notifyMediaOrderResult(BenefitOrderDO orderDO, String msg) {
        if (orderDO == null || StringUtils.isEmpty(orderDO.getDistributionChannel())) {
            return;
        }
        BenefitPlatformDO platformDO = null;
        if (orderDO.getOutChannelId() != null) {
            platformDO = benefitPlatformDOService.getById(orderDO.getOutChannelId());
        } else {
            // 兼容之前的(后续要废弃)
            String distributionChannel = orderDO.getDistributionChannel();
            if (StringUtils.isNotBlank(distributionChannel)) {
                String[] split = distributionChannel.split(StrUtil.DASHED);
                String code = split[0];
                platformDO = benefitPlatformMapper.selectOne(new LambdaQueryWrapper<BenefitPlatformDO>().eq(BenefitPlatformDO::getPlatformCode, code).eq(BenefitPlatformDO::getStatus, 1));
            }
        }
        if (platformDO != null && StringUtils.isNotBlank(platformDO.getCallBackUrl())) {
            String callBackUrl = platformDO.getCallBackUrl().trim();
            BenefitPlatformNotifyMediaReq req = new BenefitPlatformNotifyMediaReq();
            req.setOrderNo(orderDO.getOrderNo());
            req.setSuccess(Objects.equals(orderDO.getPayStatus(), PayStatusEnum.SUCCESS.getCode()));
            req.setMsg(msg);
            String body = JSON.toJSONString(req);

            // 改为post json请求
            HttpRequest request = HttpRequest.post(callBackUrl)
                    .header("Content-Type", "application/json")
                    .timeout(TIME_OUT);
            request.body(body);
            log.info("平台回调媒体之前。{}", request);
            HttpResponse response = request.execute();
            log.info("平台回调媒体之后。{}", response);
            BizResult bizResult = JSON.parseObject(response.body(), BizResult.class);
            if (response.getStatus() != 200 || !bizResult.isSuccess()) {
                log.info("请求url:{},请求入参:{},出参:{}", callBackUrl, body, response.body());
            }

        }
    }

    /**
     * notifyMediaOrderResult 的异步版本
     */
    public void notifyMediaOrderResultAsync(BenefitOrderDO orderDO, String msg) {
        // 异步通知
        ExecutorServiceUtils.execute(() -> {
            try {
                notifyMediaOrderResult(orderDO, msg);
            } catch (Exception e) {
                log.error("通知媒体失败。订单id:{}", orderDO.getOrderId(), e);
            }
        });
    }

    /**
     * 联通沃阅读支付
     *
     * @param req 请求入参
     */
    public BenefitPlatformOrderRes unicomReadOrder(BenefitPlatformUnicomReadReq req) {
        return getBenefitPlatformOrderRes(req);
    }

    @NotNull
    private BenefitPlatformOrderRes getBenefitPlatformOrderRes(BenefitPlatformBaseReq req) {
        String phone = req.getMobile();
        RLock lock = cacheHandler.getBenefitPlatformOrderLock(phone);
        if (Objects.isNull(lock)) {
            throw BizException.create(BizErrorCodeEnum.ORDER_NOT_FINISH);
        }
        try {
            BenefitProductDO dbProductDO = benefitOrderManager.getValidProduct(req.getProdCode());
            dbProductDO.setDistributionChannel(this.getDistributionChannel(req.getAppId(), req.getCommonId()));

            BenefitPayHandler payHandler = benefitOrderManager.getPayExistChannelHandler(BenefitPayChannelEnum.of(dbProductDO.getPayChannelId()));
            // 去获取订单
            BenefitOrderDO orderDO = this.getBenefitOrderByPhoneAndProduct(req.getAppId(), req.getCommonId(), phone, req.getProdCode(), req);

            BenefitPlatformOrderRes res = new BenefitPlatformOrderRes();
            res.setSuccess(false);

            BenefitPayResultBO resultBO = null;
            // 预下单状态
            PreorderStatusEnum preorderStatus;
            String errorCode = "";
            try {
                this.checkBlackUser(phone, req.getPackageName(), dbProductDO, orderDO);

                UserBenefitOrderReq userBenefitOrderReq = BeanUtil.copyProperties(req, UserBenefitOrderReq.class);
                orderDO.setSmsCode(userBenefitOrderReq.getSmsCode());
                resultBO = payHandler.getPayUrl(userBenefitOrderReq, dbProductDO, orderDO);
                preorderStatus = resultBO.getSuccess() ? PreorderStatusEnum.SUCCESS : PreorderStatusEnum.FAIL;
                res.setSuccess(resultBO.getSuccess() ? Boolean.TRUE : Boolean.FALSE);
                if (resultBO instanceof BenefitUnicomPayResultBO) {
                    BenefitUnicomPayResultBO unicomPayResultBO = (BenefitUnicomPayResultBO) resultBO;
                    res.setPayUrl(unicomPayResultBO.getPayUrl());
                }
            } catch (Exception e) {
                preorderStatus = PreorderStatusEnum.FAIL;
                if (e instanceof BizException) {
                    bizErrLog.error("平台下单失败。手机号:{},产品code:{}", phone, req.getProdCode(), e);
                    BizException be = (BizException) e;
                    resultBO = new BenefitPayResultBO();
                    resultBO.setMessage(be.getMessage());
                    resultBO.setSuccess(Boolean.FALSE);
                    errorCode = be.getCode();
                } else {
                    bizErrLog.error("平台下单失败。手机号:{},产品code:{}", phone, req.getProdCode(), e);
                }
            }
            orderDO.setPreorderStatus(preorderStatus.getCode());
            if (PreorderStatusEnum.FAIL.equals(preorderStatus)) {
                orderDO.setOrderStatus(OrderStatusEnum.FAIL.getCode());
            }
            // 最后更新订单
            orderDO.setUpdateTime(new Date());
            benefitOrderDOService.updateById(orderDO);
            if (Objects.isNull(resultBO)) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "暂时无法订购");
            } else if (!resultBO.getSuccess()) {
                errorCode = StringUtils.defaultIfBlank(errorCode, BizErrorCodeEnum.ORDER_STATUS_ERROR.getCode());
                throw BizException.create(BizResult.error(errorCode, resultBO.getMessage()));
            }
            res.setOrderNo(orderDO.getOrderNo());
            res.setOrderTimestamp(System.currentTimeMillis());
            return res;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void setChannelInfo(String appId, BenefitOrderDO orderDO) {
        BenefitPlatformDO platformDO = benefitPlatformDOService.getOne(new LambdaQueryWrapper<BenefitPlatformDO>().eq(BenefitPlatformDO::getPlatformCode, appId));
        if (platformDO == null) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "appId错误");
        }
        if (platformDO.getStatus() == 0) {
            throw BizException.create(BaseErrorCodeEnum.AUTH_FAIL, "appId状态无效");
        }
        orderDO.setOutChannelId(platformDO.getId());
        orderDO.setOutChannelName(platformDO.getPlatformName());
    }

    public BenefitOrderDO getOrderByOutOrderNo(String phone, String outOrderNo) {
        if (StringUtils.isBlank(outOrderNo) || StringUtils.isBlank(phone)) {
            return null;
        }
        return benefitOrderDOService.getOne(new LambdaQueryWrapper<BenefitOrderDO>()
                .eq(BenefitOrderDO::getPhone, phone)
                .eq(BenefitOrderDO::getOutOrderNo, outOrderNo));
    }

    public BenefitOrderDO getShuKeEduOrder(Integer payChannelId, List<Integer> payChannelPkgIds, String extraData) {
        if (payChannelId == null || StringUtils.isBlank(extraData)) {
            return null;
        }
        return benefitOrderDOService.getOne(new LambdaQueryWrapper<BenefitOrderDO>()
                .eq(BenefitOrderDO::getPayChannelId, payChannelId)
                .in(BenefitOrderDO::getPayChannelPkgId, payChannelPkgIds)  // 修复：使用 in 而不是 eq
                .eq(BenefitOrderDO::getExtraData, extraData)
                .orderByDesc(BenefitOrderDO::getOrderId)
                .last("limit 1")
        );
    }

    public BenefitOrderDO getByOrderByOrderNo(String orderNo) {
        return benefitOrderDOService.getOne(new LambdaQueryWrapper<BenefitOrderDO>()
                .eq(BenefitOrderDO::getOrderNo, orderNo));
    }
}
