package com.yuelan.hermes.quanyi.common.util;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.Objects;

/**
 * <AUTHOR> 2024/5/20 下午7:41
 */
@Slf4j
public class OuterChannelSignUtil {

    /**
     * 外部渠道api的签名算法
     */
    public static <T> boolean verifySign(String secret, String sign, T data) {
        // 验证签名
        JSONObject dataJson = JSONObject.parseObject(JSONObject.toJSONString(data));
        StringBuilder dataStr = new StringBuilder();
        // 自然排序拼接
        dataJson.keySet().stream().sorted().forEach(key -> {
            Object value = dataJson.get(key);
            if (Objects.nonNull(value)) {
                dataStr.append(key).append("=").append(value).append("&");
            }
        });
        dataStr.append("secret=").append(secret);
        String mySign = DigestUtils.md5Hex(dataStr.toString());
        log.debug("dataStr={},sign={}", dataStr, mySign);
        return mySign.equalsIgnoreCase(sign);
    }

}
