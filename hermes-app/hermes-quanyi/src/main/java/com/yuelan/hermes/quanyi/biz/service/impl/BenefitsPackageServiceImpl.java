package com.yuelan.hermes.quanyi.biz.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuelan.hermes.commons.enums.UpOffStatusEnum;
import com.yuelan.hermes.quanyi.biz.service.BenefitsPackageItemService;
import com.yuelan.hermes.quanyi.biz.service.BenefitsPackageService;
import com.yuelan.hermes.quanyi.common.enums.DispatchTimingEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BenefitErrorEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageItemDO;
import com.yuelan.hermes.quanyi.common.util.MpPageUtil;
import com.yuelan.hermes.quanyi.controller.request.BenefitsPackageLimitReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitsPackageListReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitsPackageSaveReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitsPackageStatusReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitsPackageResp;
import com.yuelan.hermes.quanyi.mapper.BenefitsPackageMapper;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2025/4/21
 * @since 2025/4/21
 */
@Service
public class BenefitsPackageServiceImpl extends ServiceImpl<BenefitsPackageMapper, BenefitsPackageDO> implements BenefitsPackageService {

    @Autowired
    private BenefitsPackageItemService benefitsPackageItemService;

    @Override
    public BenefitsPackageDO getByCode(String packageCode) {
        return lambdaQuery()
                .eq(BenefitsPackageDO::getPackageCode, packageCode)
                .one();
    }

    @Override
    public PageData<BenefitsPackageResp> list(BenefitsPackageListReq req) {
        IPage<BenefitsPackageDO> page = MpPageUtil.convertPageRequest(req);
        page = baseMapper.selectPage(page, req.buildQueryWrapper());
        List<Long> ids = page.getRecords().stream().map(BenefitsPackageDO::getPackageId).collect(Collectors.toList());

        Map<Long, List<BenefitsPackageItemDO>> mapItem = new HashMap<>();
        if (!ids.isEmpty()) {
            //查询权益表，统计组合包的权益数量
            List<BenefitsPackageItemDO> itemDOList = benefitsPackageItemService.listByPackageIds(ids);
            mapItem = itemDOList.stream().collect(Collectors.groupingBy(BenefitsPackageItemDO::getPackageId));
        }

        Map<Long, List<BenefitsPackageItemDO>> finalMapItem = mapItem;
        List<BenefitsPackageResp> respList = page.getRecords().stream().map(obj -> {
            if (finalMapItem.isEmpty()) {
                return BenefitsPackageResp.buildResp(obj, 0L, 0L);
            }
            List<BenefitsPackageItemDO> items = finalMapItem.get(obj.getPackageId());
            if (Objects.isNull(items) || items.isEmpty()) {
                return BenefitsPackageResp.buildResp(obj, 0L, 0L);
            }
            Long rechargeCount = items.stream().filter(item -> Objects.equals(DispatchTimingEnum.RECHARGE.getCode(), item.getDispatchTiming())).count();
            Long chooseCount = items.stream().filter(item -> Objects.equals(DispatchTimingEnum.CHOOSE.getCode(), item.getDispatchTiming())).count();
            return BenefitsPackageResp.buildResp(obj, rechargeCount, chooseCount);
        }).collect(Collectors.toList());
        return PageData.create(respList, page.getTotal(), req.getPage(), req.getSize());
    }

    @Override
    public void save(BenefitsPackageSaveReq req) {
        req.setPackageId(null);
        BenefitsPackageDO packageDO = BenefitsPackageSaveReq.convert(req);
        packageDO.setStatus(UpOffStatusEnum.OFF.getCode());
        packageDO.setPackageCode(generatePackageCode());
        packageDO.setRedemptionLimit(0);
        save(packageDO);
    }

    @Override
    public void update(BenefitsPackageSaveReq req) {
        BenefitsPackageDO packageDO = getById(req.getPackageId());
        if (Objects.isNull(packageDO)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "权益组合包不存在");
        }
        packageDO = BenefitsPackageSaveReq.convert(req);
        updateById(packageDO);
    }

    @Override
    public void updateLimit(BenefitsPackageLimitReq req) {
        BenefitsPackageDO packageDO = getById(req.getPackageId());
        if (Objects.isNull(packageDO)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "权益组合包不存在");
        }
        packageDO.setRedemptionLimit(req.getRedemptionLimit());
        updateById(packageDO);
    }

    @Override
    public void updateStatus(BenefitsPackageStatusReq req) {
        BenefitsPackageDO packageDO = getById(req.getPackageId());
        if (Objects.isNull(packageDO)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "权益组合包不存在");
        }
        if (Objects.equals(UpOffStatusEnum.UP.getCode(), req.getStatus())) {
            List<BenefitItemDO> list = benefitsPackageItemService.listBenefitsByPackageId(req.getPackageId());
            if (list.isEmpty()) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "请先添加关联权益商品");
            }
            long rechargeCount = list.stream()
                    .filter(obj -> Objects.equals(DispatchTimingEnum.RECHARGE.getCode(), obj.getDispatchTiming()))
                    .count();
            long chooseCount = list.stream()
                    .filter(obj -> Objects.equals(DispatchTimingEnum.CHOOSE.getCode(), obj.getDispatchTiming()))
                    .count();
            if (Objects.equals(rechargeCount, 0L)) {
                if (Objects.equals(packageDO.getRedemptionLimit(), 0)) {
                    throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "最大兑换次数不能为0");
                }
            }
            if (chooseCount != 0 && packageDO.getRedemptionLimit() <= 0) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "请设置选发权益的最大兑换次数");
            }
        }
        packageDO.setStatus(req.getStatus());
        updateById(packageDO);
    }

    /**
     * 生成唯一编码
     * 随机生成 && 不存在数据库内
     * 8-10长度生成10次 连续10次出错 增长一位
     */
    private String generatePackageCode() {
        final int MAX_CODE_LENGTH = 15;
        boolean dbExist;
        int count = 0;
        int generateCount = 10;
        int generateLength = 8;
        String code;
        do {
            count++;
            if (count % generateCount == 0) {
                generateLength++;
            }
            if (generateLength > MAX_CODE_LENGTH) {
                throw BizException.create(BaseErrorCodeEnum.SYSTEM_BUSY, "商品码生成失败");
            }
            code = RandomUtil.randomString(generateLength);

            LambdaQueryWrapper<BenefitsPackageDO> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(BenefitsPackageDO::getPackageCode, code);
            dbExist = baseMapper.selectCount(wrapper) > 0L;
        } while (dbExist);
        return code;
    }

    @Override
    public BenefitsPackageDO packageAndItems(String packageCode) {
        BenefitsPackageDO packageDO = getByCode(packageCode);
        if (Objects.nonNull(packageDO)) {
            packageDO.setBenefits(benefitsPackageItemService.listBenefitsByPackageId(packageDO.getPackageId()));
        }
        return packageDO;
    }

    @Override
    public BenefitsPackageDO getAvailablePackageAndItems(String packageCode) {
        BenefitsPackageDO packageDO = this.packageAndItems(packageCode);
        BizException.assertCheck(
                packageDO != null && Objects.equals(packageDO.getStatus(), UpOffStatusEnum.UP.getCode()),
                BenefitErrorEnum.BENEFIT_PACKAGE_NOT_FOUND
        );
        return packageDO;
    }

}

