package com.yuelan.hermes.quanyi.common.pojo.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.yuelan.result.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR> 2024/5/17 下午4:02
 * 外部渠道 api可以调用电商卡部分接口
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "ecc_outer_channel")
public class EccOuterChannelDO extends BaseDO implements Serializable {


    private static final long serialVersionUID = 1L;
    /**
     * 外部渠道id
     */
    @TableId(value = "outer_channel_id", type = IdType.AUTO)
    private Long outerChannelId;

    /**
     * 渠道名字
     */
    @TableField(value = "channel_name")
    private String channelName;

    /**
     * 后台生成给渠道的参数
     */
    @TableField(value = "api_key")
    private String apiKey;

    /**
     * 后台生成的密钥
     */
    @TableField(value = "api_secret")
    private String apiSecret;

    /**
     * 白名单ip
     */
    @TableField(value = "ip_whitelist")
    private String ipWhitelist;

    /**
     * 联通zop发展人编号
     */
    @TableField(value = "zop_referrer_code")
    private String zopReferrerCode;


    /**
     * 是否禁用：0-表示正常，1-表示禁用
     */
    @TableField(value = "is_disabled")
    private Integer isDisabled;


    @TableLogic
    private Integer deleted;

}