package com.yuelan.hermes.quanyi.biz.service;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuelan.hermes.commons.enums.SpEnum;
import com.yuelan.hermes.commons.enums.ZopOrderStatusEnum;
import com.yuelan.hermes.quanyi.biz.handler.FileExportTaskHandler;
import com.yuelan.hermes.quanyi.biz.handler.impl.SysBusyLockFailureStrategy;
import com.yuelan.hermes.quanyi.common.enums.*;
import com.yuelan.hermes.quanyi.common.event.MallApiOrderBatchUpdateEvent;
import com.yuelan.hermes.quanyi.common.event.MallApiOrderUpdateEvent;
import com.yuelan.hermes.quanyi.common.pojo.bo.EccZopOrderDateTotalBO;
import com.yuelan.hermes.quanyi.common.pojo.converter.OrderTotalConverter;
import com.yuelan.hermes.quanyi.common.pojo.domain.*;
import com.yuelan.hermes.quanyi.common.pojo.excel.EccUnicomReceiveCardImportExcel;
import com.yuelan.hermes.quanyi.common.util.MpPageUtil;
import com.yuelan.hermes.quanyi.config.satoken.StpAdminUtil;
import com.yuelan.hermes.quanyi.controller.request.EccZopOrderListReq;
import com.yuelan.hermes.quanyi.controller.request.EccZopOrderTotalReq;
import com.yuelan.hermes.quanyi.controller.response.EccZopOrderDateTotalResp;
import com.yuelan.hermes.quanyi.controller.response.EccZopOrderResp;
import com.yuelan.hermes.quanyi.controller.response.EccZopOrderTotalResp;
import com.yuelan.hermes.quanyi.controller.response.FileExportTaskCreateResp;
import com.yuelan.hermes.quanyi.mapper.EccZopOrderDOMapper;
import com.yuelan.result.entity.KeyValue;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/5/4 下午10:02
 */
@Service
@RequiredArgsConstructor
public class EccZopOrderDOService extends ServiceImpl<EccZopOrderDOMapper, EccZopOrderDO> implements FileExportTaskHandler {

    private final EccZopOrderDOMapper eccZopOrderDOMapper;
    private final EccOuterChannelDOService outerChannelDOService;
    private final EccChannelDOService eccChannelDOService;
    private final EccProductDOService eccProductDOService;
    private final FileExportTaskDOService fileExportTaskDOService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final EccAreaService eccAreaService;

    @Resource
    private UnicomMallOrderRemoteService unicomMallOrderRemoteService;

    /**
     * 查询是否领取过这个号码的订单 暂时不限定订单是否成功
     */
    public Integer countByPhoneNum(String phoneNum) {
        return baseMapper.countByPhoneNum(phoneNum);
    }

    /**
     * 查询产品信息和渠道信息 （只查询领卡成功的）
     *
     * @param phones 手机号 list
     */
    public List<EccZopOrderDO> selectProdInfoAndChannelByPhonesAndProdId(List<String> phones, Long prodId) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .select(EccZopOrderDO::getPhone, EccZopOrderDO::getProdId, EccZopOrderDO::getChannelId, EccZopOrderDO::getChannelName, EccZopOrderDO::getCreateTime)
                .in(EccZopOrderDO::getPhone, phones)
                .eq(EccZopOrderDO::getProdId, prodId)
                .eq(EccZopOrderDO::getOrderSyncStatus, ZopOrderStatusEnum.SUCCESS.getCode())
                .list();
    }

    public PageData<EccZopOrderResp> pageList(EccZopOrderListReq req) {
        IPage<EccZopOrderDO> page = MpPageUtil.convertPageRequest(req);
        List<Long> outChannelLimit = outerChannelDOService.selectLimitChannelIds();
        List<Long> innerChannelLimit = eccChannelDOService.selectLimitChannelIds();
        page = eccZopOrderDOMapper.selectPage(page, req.buildQueryWrapper(outChannelLimit, innerChannelLimit, null, false));
        List<EccZopOrderResp> respList = page.getRecords().stream()
                .map(EccZopOrderResp::buildResp).collect(Collectors.toList());
        return PageData.create(respList, page.getTotal(), req.getPage(), req.getSize());
    }

    /**
     * 查询哪些 syncOrderNo 存在数据库
     */
    public List<EccZopOrderDO> listExistOrderByOrderNos(List<String> syncOrderNos) {
        return eccZopOrderDOMapper.listExistOrderBySyncOrderNo(syncOrderNos);
    }

    public List<EccZopOrderDO> listExistOrderByChosePhoneAndContactPhone(int orderSource, List<EccZopOrderDO> zopOrderDos) {
        return eccZopOrderDOMapper.listExistOrderByChosePhoneAndContactPhone(orderSource, zopOrderDos);
    }


    public EccZopOrderDO getBySyncOrderNo(String syncOrderNo) {
        return eccZopOrderDOMapper.getBySyncOrderNo(syncOrderNo);
    }

    public void updateStatusAndFirstRechargeBySyncOrderNo(String order, String state, Integer firstRecharge, String newPhone, String newRemark) {
        eccZopOrderDOMapper.updateStatusAndFirstRechargeBySyncOrderNo(order, state, firstRecharge, newPhone, newRemark);
    }

    public int getOuterChannelOrderCount(Long outChannelId, EccChannelTypeEnum channelTypeEnum, String channelOrderNo) {
        return eccZopOrderDOMapper.getOuterChannelOrderCount(outChannelId, channelTypeEnum.getType(), channelOrderNo);
    }

    public EccZopOrderDO getOuterChannelOrder(Long outChannelId, EccChannelTypeEnum channelTypeEnum, String channelOrderNo) {
        return eccZopOrderDOMapper.getOuterChannelOrder(outChannelId, channelTypeEnum.getType(), channelOrderNo);
    }

    public List<KeyValue<String, String>> unicomStatusOptions() {
        return Arrays.stream(ZopStateEnum.values())
                .map(enumVal -> new KeyValue<>(enumVal.getCode(), enumVal.getDesc()))
                .collect(Collectors.toList());
    }

    public List<EccZopOrderDO> listInnerChannelOrderByFailReason(List<String> failKeywords, LocalDateTime startTime, LocalDateTime endTime) {
        if (failKeywords.isEmpty()) {
            return new ArrayList<>();
        }
        String reg = String.join("|", failKeywords);
        return baseMapper.listInnerChannelOrderByFailReason(reg, startTime, endTime);
    }

    public int countByOriginalOrderNo(String originalOrderNo) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(EccZopOrderDO::getOriginalOrderNo, originalOrderNo)
                .count().intValue();
    }


    public List<EccZopOrderDO> listOrderByIdCardV2(String idCard, LocalDateTime startTime) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(EccZopOrderDO::getIdCard, idCard)
                .ge(EccZopOrderDO::getCreateTime, startTime)
                .list();
    }


    public EccZopOrderDO getLastReissueOrder(String originalOrderNo) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(EccZopOrderDO::getOriginalOrderNo, originalOrderNo)
                .orderByDesc(EccZopOrderDO::getCreateTime)
                .last("limit 1")
                .one();
    }


    /**
     * 只导入 网上商城端订单 的部分订单
     *
     * @param file excel
     */
    @Lock4j(acquireTimeout = 0L, failStrategy = SysBusyLockFailureStrategy.class)
    public void importUnicomOrder(MultipartFile file) throws IOException {
        final String systemSource = "网上商城端外";
        // "审核处理中", "待开户", "成功关闭", "新订单中心退单"
        // 待开户 = 领卡成功  成功关闭 + 已经激活 = 激活成功 成功关闭 + 首冲金额 > 0 = 首冲成功 新订单中心退单 = 退单的订单
        final List<String> filterUnicomOrderState = Arrays.asList("审核处理中", "待开户", "成功关闭", "新订单中心退单", "待派单");
        List<EccUnicomReceiveCardImportExcel> excelList = EasyExcel.read(file.getInputStream())
                .headRowNumber(2)
                .head(EccUnicomReceiveCardImportExcel.class)
                .sheet()
                .doReadSync();

        excelList = excelList.stream()
                .filter(row -> systemSource.equals(row.getSystemSource()))
                .filter(row -> filterUnicomOrderState.contains(row.getOrderStatus()))
                .collect(Collectors.toList());

        List<EccZopOrderDO> zopOrderDos = convertExcelToDo(excelList);
        if (!zopOrderDos.isEmpty()) {
            batchSaveOrUpdateBySyncOrderNo(zopOrderDos);
        }
    }

    public void batchSaveOrUpdateBySyncOrderNo(List<EccZopOrderDO> zopOrderDos) {
        if (zopOrderDos == null || zopOrderDos.isEmpty()) {
            return;
        }

        List<String> syncOrderNos = zopOrderDos.stream()
                .map(EccZopOrderDO::getSyncOrderNo)
                .collect(Collectors.toList());

        List<EccZopOrderDO> existingOrders = new ArrayList<>();
        // 分片查询 listExistOrderByOrderNos(syncOrderNos);
        ListUtil.partition(syncOrderNos, 100).forEach(orders -> {
            existingOrders.addAll(listExistOrderByOrderNos(orders));
        });
        List<String> existOrderNoList = existingOrders.stream().map(EccZopOrderDO::getSyncOrderNo).collect(Collectors.toList());
        // 没有被查询到的商城订单 尝试  用选择的手机号码和联系号码
        List<EccZopOrderDO> newOrders = new ArrayList<>();
        for (EccZopOrderDO zopOrderDO : zopOrderDos) {
            if (!existOrderNoList.contains(zopOrderDO.getSyncOrderNo())) {
                newOrders.add(zopOrderDO);
            }
        }
        if (!newOrders.isEmpty()) {
            ListUtil.partition(newOrders, 100).forEach(orders -> {
                existingOrders.addAll(listExistOrderByChosePhoneAndContactPhone(ZopOrderSource.ZOP_MALL_API.getCode(), orders));
            });
        }


        Map<String, EccZopOrderDO> existingOrderMap = existingOrders.stream()
                .collect(Collectors.toMap(
                        order -> order.getContactPhone() + "_" + order.getPhone(),
                        order -> order
                ));

        List<EccZopOrderDO> ordersToUpdate = new ArrayList<>();
        List<EccZopOrderDO> ordersToInsert = new ArrayList<>();
        List<EccZopOrderDO> oldMallApiOrders = new ArrayList<>();
        for (EccZopOrderDO order : zopOrderDos) {
            EccZopOrderDO existingOrder = existingOrderMap.get(order.getContactPhone() + "_" + order.getPhone());
            if (existingOrder != null) {
                EccZopOrderDO updatedOrder = JSON.parseObject(JSON.toJSONString(order), EccZopOrderDO.class);
                EccZopOrderDO simpleUpdateDo = new EccZopOrderDO();
                if (!Objects.equals(existingOrder.getPreOrderStatus(), order.getPreOrderStatus())) {
                    simpleUpdateDo.setPreOrderStatus(order.getPreOrderStatus());
                    updatedOrder.setPreOrderStatus(order.getPreOrderStatus());
                }
                simpleUpdateDo.setZopOrderId(existingOrder.getZopOrderId());


                updatedOrder.setChannelId(existingOrder.getChannelId());
                updatedOrder.setChannelType(existingOrder.getChannelType());
                updatedOrder.setChannelName(existingOrder.getChannelName());

                simpleUpdateDo.setSyncOrderNo(order.getSyncOrderNo());
                updatedOrder.setSyncOrderNo(order.getSyncOrderNo());

                simpleUpdateDo.setFirstRecharge(order.getFirstRecharge());
                updatedOrder.setFirstRecharge(order.getFirstRecharge());

                simpleUpdateDo.setZopOrderState(order.getZopOrderState());
                updatedOrder.setZopOrderState(order.getZopOrderState());

                ordersToUpdate.add(simpleUpdateDo);

                if (ZopOrderSource.ZOP_MALL_API.getCode().equals(existingOrder.getZopOrderSource())) {
                    // events.add(new MallApiOrderUpdateEvent(existingOrder, updatedOrder));
                    oldMallApiOrders.add(existingOrder);
                }
            } else {
                ordersToInsert.add(order);
            }
        }

        if (!ordersToUpdate.isEmpty()) {
            updateBatchById(ordersToUpdate, 500);
        }

        if (!ordersToInsert.isEmpty()) {
            saveBatch(ordersToInsert, 500);
        }

        if (!oldMallApiOrders.isEmpty()) {
            Map<Long, EccZopOrderDO> id2OrderMap = oldMallApiOrders.stream().collect(Collectors.toMap(EccZopOrderDO::getZopOrderId, order -> order));
            // 分批查询更新后的订单
            Map<Long, EccZopOrderDO> updatedOrderMap = new HashMap<>();
            ListUtil.partition(oldMallApiOrders, 100).forEach(orders -> {
                updatedOrderMap.putAll(listByIds(id2OrderMap.keySet())
                        .stream().collect(Collectors.toMap(EccZopOrderDO::getZopOrderId, order -> order)));
            });
            List<MallApiOrderUpdateEvent> events = new ArrayList<>();
            for (Long id : updatedOrderMap.keySet()) {
                EccZopOrderDO oldOrder = id2OrderMap.get(id);
                EccZopOrderDO updatedOrder = updatedOrderMap.get(id);
                if (updatedOrder != null) {
                    events.add(new MallApiOrderUpdateEvent(oldOrder, updatedOrder));
                }
            }
            MallApiOrderBatchUpdateEvent event = new MallApiOrderBatchUpdateEvent(events);
            applicationEventPublisher.publishEvent(event);
        }
    }

    /**
     * excel 转数据库对象
     *
     * @param excelRows excel 过滤后的数据j
     */
    private List<EccZopOrderDO> convertExcelToDo(List<EccUnicomReceiveCardImportExcel> excelRows) {
        List<EccProductDO> prodList = eccProductDOService.list();
        List<EccOuterChannelDO> outChannels = outerChannelDOService.list();

        return excelRows.stream().map(row -> {
            EccZopOrderDO eccZopOrderDO = new EccZopOrderDO();
            setBasicFields(eccZopOrderDO, row);
            matchAndSetProd(eccZopOrderDO, prodList);
            setAdditionalFields(eccZopOrderDO, row);
            return eccZopOrderDO;
        }).collect(Collectors.toList());
    }


    private void setBasicFields(EccZopOrderDO eccZopOrderDO, EccUnicomReceiveCardImportExcel row) {
        eccZopOrderDO.setZopOrderSource(ZopOrderSource.ZOP_MALL.getCode());
        eccZopOrderDO.setOrderNo(null);
        eccZopOrderDO.setIdCardName(row.getCustomerName());
        eccZopOrderDO.setIdCard(row.getIdCard());
        eccZopOrderDO.setZopGoodsId(row.getZopGoodsId());
        eccZopOrderDO.setPostProvince(row.getPostProvince());
        eccZopOrderDO.setPostCity(row.getPostCity());
        eccZopOrderDO.setPostDistrict(row.getPostDistrict());
        eccZopOrderDO.setAddress(row.getAddress());
        eccZopOrderDO.setContactPhone(row.getCustomerPhone());
        eccZopOrderDO.setPhone(row.getPhone());
        eccZopOrderDO.setZopReferrerCode(row.getReferrerCode());
        eccZopOrderDO.setSyncOrderNo(row.getOrderNo());
        if (Objects.nonNull(row.getFirstChargeAmount())) {
            int amount = Integer.parseInt(row.getFirstChargeAmount()) * 100;
            eccZopOrderDO.setFirstRecharge(amount == 0 ? null : amount);
        }
        ZopStateEnum zopStateEnum = parseZopStateEnum(row);
        if (zopStateEnum != null) {
            eccZopOrderDO.setZopOrderState(zopStateEnum.getCode());
        }
        // 数据库的原来的插入时间前端作为领取时间  orderTime 2024-08-01 18:17:09
        eccZopOrderDO.setCreateTime(safeParseDateTime(row.getOrderTime()));
    }

    private void setAdditionalFields(EccZopOrderDO eccZopOrderDO, EccUnicomReceiveCardImportExcel row) {
        String birth = null;
        if (row.getAge() != null) {
            birth = LocalDate.now().minusYears(Integer.parseInt(row.getAge())).toString();
        }
        if (Objects.nonNull(birth)) {
            eccZopOrderDO.setIdCard(row.getIdCard().replaceFirst("\\*{4}", birth.substring(0, 4)));
        }
        eccZopOrderDO.setRiskCheckStatus(ZopOrderStatusEnum.SUCCESS.getCode());
        eccZopOrderDO.setPreOrderStatus(ZopOrderStatusEnum.SUCCESS.getCode());
        eccZopOrderDO.setSelectType(PhoneNumSelectType.USER_SELECT.getCode());
        eccZopOrderDO.setSelectNumStatus(ZopOrderStatusEnum.SUCCESS.getCode());
        eccZopOrderDO.setOrderSyncStatus(ZopOrderStatusEnum.SUCCESS.getCode());
    }

    private void matchAndSetProd(EccZopOrderDO eccZopOrderDO, List<EccProductDO> prodList) {
        for (EccProductDO itemProd : prodList) {
            if (Objects.equals(itemProd.getSpGoodsId(), eccZopOrderDO.getZopGoodsId())) {
                eccZopOrderDO.setProdId(itemProd.getProdId());
                eccZopOrderDO.setProdName(itemProd.getProdName());
                break;
            }
        }
    }

    /**
     * 安全解析日期时间
     *
     * @param time 2024-08-01 18:17:09
     */
    private Date safeParseDateTime(String time) {
        try {
            LocalDateTime dateTime = LocalDateTimeUtil.parse(time, DatePattern.NORM_DATETIME_PATTERN);
            // LocalDateTime 转 date
            return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 主要是判断激活 首冲 退单
     */
    public ZopStateEnum parseZopStateEnum(EccUnicomReceiveCardImportExcel excel) {
        String orderStatus = excel.getOrderStatus();
        String firstChargeAmount = excel.getFirstChargeAmount();
        String activeStatus = excel.getActiveStatus();
        boolean isActive = Objects.equals("已激活", activeStatus);

        if (Objects.equals("待开户", orderStatus) ||
                Objects.equals("审核处理中", orderStatus) ||
                Objects.equals("待派单", orderStatus)
        ) {
            return null;
        } else if (Objects.equals("成功关闭", orderStatus)) {
            if (isActive) {
                return Long.parseLong(firstChargeAmount) > 0 ? ZopStateEnum.FIRST_CHARGE : ZopStateEnum.ACTIVE;
            } else {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "联通订单状态不能识别");
            }
        } else if (Objects.equals("新订单中心退单", orderStatus)) {
            return ZopStateEnum.CANCEL;
        } else {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "联通订单状态不能识别");
        }
    }

    private List<Long> getAllChannelLimit() {
        List<Long> outChannelLimit = outerChannelDOService.selectLimitChannelIds();
        List<Long> innerChannelLimit = eccChannelDOService.selectLimitChannelIds();
        List<Long> allChannelLimit = new ArrayList<>();
        if (outChannelLimit != null) {
            allChannelLimit.addAll(outChannelLimit);
        }
        if (innerChannelLimit != null) {
            allChannelLimit.addAll(innerChannelLimit);
        }
        return allChannelLimit;
    }

    /**
     * 电商卡领卡记录汇总
     */
    public EccZopOrderTotalResp total(EccZopOrderTotalReq req) {
        List<Long> allChannelLimit = this.getAllChannelLimit();
        if (Objects.nonNull(req.getEndTime())) {
            req.setEndTime(req.getEndTime().plusDays(1));
        }
        EccZopOrderDateTotalBO totalBO = eccZopOrderDOMapper.orderTotal(req, allChannelLimit);
        return OrderTotalConverter.toEccZopOrderDateTotalResp(totalBO);
    }

    /**
     * 电商卡领卡日期汇总分页
     */
    public PageData<EccZopOrderDateTotalResp> dateTotalPage(EccZopOrderTotalReq req) {
        List<Long> allChannelLimit = this.getAllChannelLimit();
        if (Objects.nonNull(req.getEndTime())) {
            req.setEndTime(req.getEndTime().plusDays(1));
        }

        EccZopOrderDateTotalBO totalBO = eccZopOrderDOMapper.orderTotal(req, allChannelLimit);
        EccZopOrderDateTotalResp totalResp = OrderTotalConverter.toEccZopOrderDateTotalResp(totalBO);
        totalResp.setGetDate("总计");
        List<EccZopOrderDateTotalResp> respList = new ArrayList<>();
        respList.add(totalResp);

        Long count = eccZopOrderDOMapper.orderDateTotalCount(req, allChannelLimit);
        List<EccZopOrderDateTotalBO> totalList = eccZopOrderDOMapper.orderDateTotal(req, allChannelLimit);
        List<EccZopOrderDateTotalResp> list = totalList.stream().map(OrderTotalConverter::toEccZopOrderDateTotalResp).collect(Collectors.toList());
        respList.addAll(list);

        return PageData.create(respList, count, req.getPage(), req.getSize());
    }

    public FileExportTaskCreateResp export(EccZopOrderListReq req, long adminId) {

        if (req.getEndTime() == null) {
            // 防止数据一直在变化
            req.setEndTime(LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
        } else {
            LocalDateTime endTime = LocalDateTimeUtil.parse(req.getEndTime(), DatePattern.NORM_DATETIME_PATTERN);
            // 如果结束时间大于当前时间
            if (endTime.isAfter(LocalDateTime.now())) {
                req.setEndTime(LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
            }
        }
        String param = JSONObject.toJSONString(req);
        JSONObject paramJson = JSONObject.parseObject(param);
        paramJson.put("adminId", adminId);

        FileExportTaskDO taskDO = fileExportTaskDOService.createDefaultTask(paramJson.toString(), getTaskCode(), "xlsx");
        return new FileExportTaskCreateResp(taskDO.getTaskId(), taskDO.getFileName());
    }

    @Override
    public FileExportTaskCodeEnum getTaskCode() {
        return FileExportTaskCodeEnum.ECC_ORDER_EXPORT;
    }

    @Override
    public void generateExportFile(FileExportTaskDO taskDO) {
        String evalParam = taskDO.getEvalParam();
        JSONObject paramJson = JSONObject.parseObject(evalParam);
        Long adminId = paramJson.getLong("adminId");
        paramJson.remove("adminId");
        EccZopOrderListReq req = JSON.parseObject(paramJson.toString(), EccZopOrderListReq.class);
        StpAdminUtil.switchTo(adminId, () -> {
            List<Long> outChannelLimit = outerChannelDOService.selectLimitChannelIds();
            List<Long> innerChannelLimit = eccChannelDOService.selectLimitChannelIds();

            long startId = 0L;

            try (ExcelWriter excelWriter = EasyExcel.write(taskDO.getFilePath(), EccZopOrderResp.class).build();) {
                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                while (true) {
                    Wrapper<EccZopOrderDO> wrapper = req.buildQueryWrapper(outChannelLimit, innerChannelLimit, startId, true);
                    List<EccZopOrderDO> list = eccZopOrderDOMapper.selectList(wrapper);
                    if (list.isEmpty()) {
                        break;
                    }
                    List<EccZopOrderResp> respList = list.stream()
                            .map(EccZopOrderResp::buildResp)
                            .collect(Collectors.toList());
                    excelWriter.write(respList, writeSheet);
                    // 更新起始ID
                    startId = list.get(list.size() - 1).getZopOrderId();
                }
            }
        });
    }


    public EccZopOrderDO getByOrderNo(String orderNo) {
        return new LambdaQueryChainWrapper<>(eccZopOrderDOMapper)
                .eq(EccZopOrderDO::getOrderNo, orderNo)
                .one();
    }

    @Transactional(rollbackFor = Exception.class)
    public void retryMallOrder(Long orderId) {
        EccZopOrderDO zopOrder = getById(orderId);
        Assert.notNull(zopOrder, "订单不存在");
        if (!ZopOrderSource.ZOP_MALL_API.getCode().equals(zopOrder.getZopOrderSource())) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "仅支持网上商城api订单重试");
        }
        if (!ZopOrderStatusEnum.FAIL.getCode().equals(zopOrder.getOrderSyncStatus())) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "非失败订单不可以重试");
        }
        EccProductDO productDO = eccProductDOService.getById(zopOrder.getProdId());
        EccAreaDO postAreaDo = eccAreaService.getOneAreaByCode(SpEnum.UNICOM,
                zopOrder.getPostProvinceCode(),
                zopOrder.getPostCityCode(),
                zopOrder.getPostDistrictCode());
        if (postAreaDo == null) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "邮寄地址不合法");
        }
        unicomMallOrderRemoteService.createMallOrder(zopOrder, productDO, Boolean.TRUE, postAreaDo);
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(EccZopOrderDO::getZopOrderId, orderId)
                .set(EccZopOrderDO::getOrderSyncStatus, ZopOrderStatusEnum.DEFAULT.getCode())
                .set(EccZopOrderDO::getOrderSyncResp, null)
                .set(EccZopOrderDO::getPhone, null)
                .update();
    }
}
