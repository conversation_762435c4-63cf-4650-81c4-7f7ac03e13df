package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.EccImeiService;
import com.yuelan.hermes.quanyi.controller.request.EccImeiReq;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.IOException;

/**
 * <AUTHOR> 2025/6/17
 * @since 2025/6/17
 */
@Validated
@Tag(name = "电商卡/后台api/imei管理")
@RestController
@RequestMapping("/a/ecc/imei")
@AllArgsConstructor
public class EccImeiController {

    private final EccImeiService eccImeiService;

    @Operation(summary = "分页")
    @PostMapping("/page")
    public BizResult<PageData<EccImeiResp>> page(@RequestBody @Validated EccImeiReq req) {
        return BizResult.create(eccImeiService.pageEccImei(req));
    }


    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download/template", produces = "application/octet-stream")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        eccImeiService.downloadTemplate(response);
    }

    @Operation(summary = "导入")
    @Parameters({
            @Parameter(name = "channelId", description = "渠道ID", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "productId", description = "产品ID", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "device", description = "设备信息", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "file", description = "文件", required = true, in = ParameterIn.QUERY)
    })
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BizResult<Void> importData(@RequestParam("channelId") @NotNull Long channelId,
                                      @RequestParam("channelType") @NotNull Integer channelType,
                                      @RequestParam("productId") @NotNull Long productId,
                                      @RequestParam("device") @NotEmpty String deviceInfo,
                                      @RequestParam("file") MultipartFile file) throws IOException {
        eccImeiService.importEccImei(file, channelType, channelId, productId, deviceInfo);
        return BizResult.ok();
    }

}
