package com.yuelan.hermes.quanyi.config.task;

import com.yuelan.hermes.quanyi.biz.handler.impl.BenefitHuBeiShuKePayChannel;
import com.yuelan.hermes.quanyi.common.listener.MallApiOrderUpdateListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR> 2025/3/24
 * @since 2025/3/24
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class CallbackConfig {

    private final CallbackRegistry callbackRegistry;
    private final BenefitHuBeiShuKePayChannel huBeiShuKePayChannel;

    @PostConstruct
    public void init() {
        // 注册订单回调处理器
        callbackRegistry.registerCallback(MallApiOrderUpdateListener.BUSINESS_TYPE, (task, success) -> {
            if (success) {
                log.info("订单回调成功: orderId={}", task.getBusinessId());
                // 处理订单回调成功的业务逻辑
            } else {
                log.error("订单回调失败: orderId={}", task.getBusinessId());
                // 处理订单回调失败的业务逻辑
            }
        });



    }
}
