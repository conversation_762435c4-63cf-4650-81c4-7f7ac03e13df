##### 接口描述

- 异步通知为玖佰SUP平台通知采购方商户订单充值结果的接口；

  请求由玖佰SUP平台发起，采购商户仅需开发同步返回信息部分即可。

##### 请求地址

- 订单提交接口中的”noticeUrl” 字段参数。

##### 请求方式

- HTTP的POST方式
- Content-Type： application/x-www-form-urlencoded

编码格式：所有数据编码均采用UTF-8编码，接收后参数均需要进行urlEncode解码1次。

##### 请求参数

| 字段名   | 变量名              | 说明                                                |
|-------|------------------|---------------------------------------------------|
| 商户号   | businessId       | 由平台分配每个商户唯一的一个商户号                                 |
| 商户订单号 | userOrderId      | 最大长度不超过32位的唯一流水号                                  |
| 充值凭证  | voucherNo        | 字符串,不参与签名,且仅为话费类型订单的充值凭证                          |
| 充值结果  | status           | 01 成功 02 失败                                       |
| 结果说明  | mes              | 对充值的结果予以说明； 特别是充值失败的时候需要对具体充值失败原因简单说明一下           |
| 结算总金额 | payoffPriceTotal | 系统和进货平台结算金额                                       |
| 卡密信息  | kmInfo           | kmInfo 为 3DES加密过的字符串。                             |
| 签名    | sign             | lcase(md5(businessId + userOrderId + status +密钥)) |

**kmInfo说明:**

```
复制加解密key：d656bd912ef847dc87d60701a0775937
加解密过程：
(1)KmInfo加密之前原串:
[{"hasCardNo":1,"cardNo":"123456","cardPwd":"ABCDEFG","outDate":"2024-12-11 23:59:59.000"}]
(2) 3DES加密后的kminfo:
aOleXNUkBerfbRKzLHqCAIAhQAugrZWDed+uS/tRbPbHU2lqkpk8/czTtmAl4f9m1nyga9w++3TK3LrmajPAYnvt/WQLYZadLYuhv0EXgYPhB6Yjj6xxUQKnjhATaHCE
(3) kmInfo 解密
DesUtil.des3Decrypt(kmInfo, key "utf-8");
参数说明：hasCardNo，1为有卡号，0为无卡号， cardNo：卡号，cardPwd：卡密，outDate：过期时间
```

##### 返回示例

```html
复制
<receive>ok</receive>
```

##### 返回参数说明

采购方收到玖佰SUP平台的异步通知结果后应同步返回<receive>ok</receive>，即表示采购方平台成功收到通知结果。

##### 备注

```html
复制1、异步结果通知以Post方式进行请求，采购方同步返回信息格式为string格式；
2、通知结果同步返回一定是要返回字符串形式的
<receive>ok</receive>才算成功；
3、返回
<receive>ok</receive>与订单成功与否无关，仅表示SUP平台异步通知成功；
4、异步通知失败后，SUP平台会每隔3分钟再通知一次，共计通知10次；
```

##### 附录 卡密解密示例

```javascript
复制package
com.jzy.api.util;
import org

.
apache.commons.codec.binary.Base64;
import org

.
apache.log4j.Logger;
import sun

.
misc.BASE64Decoder;
import sun

.
misc.BASE64Encoder;
import javax

.
crypto.\*
;
import javax

.
crypto.spec.DESKeySpec;
import javax

.
crypto.spec.DESedeKeySpec;
import java

.
io.IOException;
import java

.
net.URLEncoder;
import java

.
security.InvalidKeyException;
import java

.
security.Key;
import java

.
security.NoSuchAlgorithmException;
import java

.
security.SecureRandom;
import java

.
security.spec.InvalidKeySpecException;
public

class DesUtil {
    private static Logger
    logger = Logger.getLogger(DesUtil.class);
    private final
    static String
    DES = "DES";
    public final
    static String
    KEY
\
    _KEY = "A1B2C3D4E5F60708";
    public final
    static String
    KEY
\
    _LOTTERY = "E5F60708A1B2C3D4";//E5F60708A1B2C3D4

    // DES3加密格式
    private static final
    String
    DESC3 = "DESede";
    private static final
    String
    Algorithm = "DESede/ECB/PKCS5Padding";
/\*\*
\

    * DESC3加密

\*
\*
    @param src
    加密数据
\*
    @param key
    密钥
\*
    @ return
\*/
    public static String

    des3Eencrypt(String

    src
,
    String
    key
) {
    String
    ss = "";
    try {

    byte
\
    [
\]
    b = Base64.decodeBase64(key);
    DESedeKeySpec
    spec = new DESedeKeySpec(b);
    SecretKeyFactory
    keyfactory = SecretKeyFactory.getInstance(DESC3);
    Key
    deskey = keyfactory.generateSecret(spec);
    Cipher
    c1 = Cipher.getInstance(Algorithm);
    c1
.

    init(

    1
,
    deskey
)
    ;
    b = c1.doFinal(src.getBytes());
    ss = Base64.encodeBase64String(b);
}

catch
(NoSuchAlgorithmException
e
)
{
    e.printStackTrace();
}
catch
(NoSuchPaddingException
e
)
{
    e.printStackTrace();
}
catch
(Exception
e
)
{
    e.printStackTrace();
}
return ss;
}

/\*\*
     \*
DESC3解密
     \*
\* @param
src
解密数据1
     \* @param
key
密钥
     \* @param
encoding
编码格式
     \* @
return
     \*/
public
static
String
des3Decrypt(String
src, String
key, String
encoding
)
{
    try {
        byte\[\]
        s = Base64.decodeBase64(src);
        byte\[\]
        b = Base64.decodeBase64(key);
        DESedeKeySpec
        spec = new DESedeKeySpec(b);
        SecretKeyFactory
        keyfactory = SecretKeyFactory.getInstance(DESC3);
        Key
        deskey = keyfactory.generateSecret(spec);
        Cipher
        cipher = Cipher.getInstance(Algorithm);
        cipher.init(2, deskey);
        byte\[\]
        decryptData = cipher.doFinal(s);
        return new String(decryptData, encoding);
    } catch (Exception
    e
)
    {
        e.printStackTrace();
        return null;
    }
}

/\*\*
     \*
Description
根据键值进行加密
     \*
\* @param
data
     \* @param
key
加密键byte数组
     \* @
return
     \* @throws
Exception
     \*/
public
static
String
encrypt(String
data, String
key
)
{
    String
    strs = null;
    try {
        byte\[\]
        bt = encrypt(data.getBytes(), key.getBytes());
        strs = new BASE64Encoder().encode(bt);
    } catch (Exception
    e
)
    {
        e.printStackTrace();
    }
    return strs;
}

/\*\*
     \*
Description
根据键值进行解密
     \*
\* @param
data
     \* @param
key
加密键byte数组
     \* @
return
     \* @throws
IOException
     \* @throws
Exception
     \*/
public
static
String
decrypt(String
data, String
key
)
{
    byte\[\]
    bt = null;
    try {
        if (data == null)
            return null;
        BASE64Decoder
        decoder = new BASE64Decoder();
        byte\[\]
        buf = decoder.decodeBuffer(data);
        bt = decrypt(buf, key.getBytes());
    } catch (IOException
    e
)
    {
        e.printStackTrace();
    }
catch
    (Exception
    e
)
    {
        e.printStackTrace();
    }
    return new String(bt);
}

/\*\*
     \*
Description
根据键值进行加密
     \*
\* @param
data
     \* @param
key
加密键byte数组
     \* @
return
     \* @throws
Exception
     \*/
private
static
byte\[\]
encrypt(byte\[\]
data, byte\[\]
key
)
{
    try {
        // 生成一个可信任的随机数源
        SecureRandom
        sr = new SecureRandom();
        // 从原始密钥数据创建DESKeySpec对象
        DESKeySpec
        dks = new DESKeySpec(key);
        // 创建一个密钥工厂，然后用它把DESKeySpec转换成SecretKey对象
        SecretKeyFactory
        keyFactory = SecretKeyFactory.getInstance(DES);
        SecretKey
        securekey = keyFactory.generateSecret(dks);
        // Cipher对象实际完成加密操作
        Cipher
        cipher = Cipher.getInstance(DES);
        // 用密钥初始化Cipher对象
        cipher.init(Cipher.ENCRYPT\_MODE, securekey, sr
    )
        ;
        return cipher.doFinal(data);
    } catch (InvalidKeyException
    e
)
    {
        e.printStackTrace();
    }
catch
    (NoSuchAlgorithmException
    e
)
    {
        e.printStackTrace();
    }
catch
    (InvalidKeySpecException
    e
)
    {
        e.printStackTrace();
    }
catch
    (NoSuchPaddingException
    e
)
    {
        e.printStackTrace();
    }
catch
    (IllegalBlockSizeException
    e
)
    {
        e.printStackTrace();
    }
catch
    (BadPaddingException
    e
)
    {
        e.printStackTrace();
    }
    return null;
}

/\*\*
     \*
Description
根据键值进行解密
     \*
\* @param
data
     \* @param
key
加密键byte数组
     \* @
return
     \* @throws
Exception
     \*/
private
static
byte\[\]
decrypt(byte\[\]
data, byte\[\]
key
)
{
    try {
        // 生成一个可信任的随机数源
        SecureRandom
        sr = new SecureRandom();
        // 从原始密钥数据创建DESKeySpec对象
        DESKeySpec
        dks = new DESKeySpec(key);
        // 创建一个密钥工厂，然后用它把DESKeySpec转换成SecretKey对象
        SecretKeyFactory
        keyFactory = SecretKeyFactory.getInstance(DES);
        SecretKey
        securekey = keyFactory.generateSecret(dks);
        // Cipher对象实际完成解密操作
        Cipher
        cipher = Cipher.getInstance(DES);
        // 用密钥初始化Cipher对象
        cipher.init(Cipher.DECRYPT\_MODE, securekey, sr
    )
        ;
        return cipher.doFinal(data);
    } catch (InvalidKeyException
    e
)
    {
        e.printStackTrace();
    }
catch
    (NoSuchAlgorithmException
    e
)
    {
        e.printStackTrace();
    }
catch
    (InvalidKeySpecException
    e
)
    {
        e.printStackTrace();
    }
catch
    (NoSuchPaddingException
    e
)
    {
        e.printStackTrace();
    }
catch
    (IllegalBlockSizeException
    e
)
    {
        e.printStackTrace();
    }
catch
    (BadPaddingException
    e
)
    {
        e.printStackTrace();
    }
    return null;
}

public
static
void main(String\[\]
args
)
{
    String
    appId = "001";
    String
    activityCode = "0001";
    String
    mobilePhone = "13693678938";
    String
    prizeCode = "1";
    String
    name = "徐文鑫阿斯蒂芬爱的色放";
    String
    addr = "xxxx";
    System.out.println("appId:" + DesUtil.encrypt(appId, DesUtil.KEY\_LOTTERY
))
    ;
    System.out.println("activityCode:" + DesUtil.encrypt(activityCode, DesUtil.KEY\_LOTTERY
))
    ;
    System.out.println("mobilePhone:" + DesUtil.encrypt(mobilePhone, DesUtil.KEY\_LOTTERY
))
    ;
    System.out.println("prizeCode:" + DesUtil.encrypt(prizeCode, DesUtil.KEY\_LOTTERY
))
    ;
    System.out.println("name:" + DesUtil.encrypt(name, DesUtil.KEY\_LOTTERY
))
    ;
    System.out.println("name:" + URLEncoder.encode(DesUtil.encrypt(name, DesUtil.KEY\_LOTTERY
)))
    ;
    System.out.println("addr:" + DesUtil.encrypt(addr, DesUtil.KEY\_LOTTERY
))
    ;
    System.out.println(DesUtil.decrypt("y9VhmiHzKxuIUl/1NyN5lITLh+UkyLxczVmnbGj+I6JWtnUZ4nIPNg==", DesUtil.KEY\_LOTTERY
))
    ;
}
}
```
