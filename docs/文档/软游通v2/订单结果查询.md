##### 简要描述

- 订单结果查询

##### 请求地址

- 正式环境：[https://supq.900sup.cn/Service/CommOrderQry.ashx](https://supq.900sup.cn/Service/CommOrderQry.ashx)
-
沙箱环境：[http://suptest.900sup.cn:7103/Service/CommOrderQry.ashx](http://suptest.900sup.cn:7103/Service/CommOrderQry.ashx)

##### 请求方式

- HTTP 的 POST 方式
- Content-Type： application/x-www-form-urlencoded

##### 请求参数

| 参数名         | 必选 | 类型     | 说明                                          |
|-------------|----|--------|---------------------------------------------|
| businessId  | 是  | string | 由 SUP 系统分配每个商户唯一的一个商户号                      |
| userOrderId | 是  | string | 最大长度不超过 32 位的唯一流水                           |
| sign        | 是  | string | md5(businessId + userOrderId +key) MD5加密后小写 |

##### 请求示例

```
复制businessId=Num10001&userOrderId=e33ca9b66e18470c912bbe323097acd3&sign=212587de489669529e578fe61b24b8b3
```

##### 返回示例

```
复制<?xml version="1.0" encoding="utf-8"?>
<root>
     <businessId>Num10001</businessId>
     <userOrderId>e33ca9b66e18470c912bbe323097acd3</userOrderId>
     <status>02</status>
     <payoffPriceTotal>1.0000</payoffPriceTotal>
     <mes>充值失败，请联系客服协助处理！</mes>
     <kmInfo></kmInfo>
     <sign>7b53e16fb52bc29b505d8c52650ada9f</sign>
     <voucherNo></voucherNo>
</root>
```

##### 返回参数说明

| 参数名              | 类型      | 说明                                                                                   |
|------------------|---------|--------------------------------------------------------------------------------------|
| businessId       | string  | 由 SUP 系统分配每个商户唯一的一个商户号                                                               |
| userOrderId      | string  | 最大长度不超过 32 位的唯一流水号                                                                   |
| status           | string  | 01 成功（订单最终状态）；02 失败（订单最终状态） ；03 处理中（需要等待异步通知结果） ；04 订单不存在 ；05 未知错误 ；06 签名错误 ；07 参数有误 |
| mes              | string  | 对充值的结果予以说明，特别是充值失败的时候需要对具体充 值失败原因简单说明一下                                              |
| payoffPriceTotal | decimal | 系统和进货平台结算金                                                                           |
| kmInfo           | string  | kmInfo 为 3DES 加密过的字符串。                                                               |
| sign             | string  | md5 (businessId + userOrderId + status +密钥)                                          |
| voucherNo        | string  | 充值凭证                                                                                 |

**kmInfo示例：**

加解密key：d656bd912ef847dc87d60701a0775937

加解密过程：

- (1)KmInfo 加密之前原串: \[{“hasCardNo”:1,”cardNo”:”123456”,”cardPwd”:”ABCDEFG “,”outDate”:”2024-12-11 23:59:59.000”}\]
- (2)3DES 加密后的 kminfo: aOleXNUkBerfbRKzLHqCAIAhQAugrZWDed+uS/tRbPbH U2lqkpk8/czTtmAl4f9m1nyga9w++3TK3LrmajPAYnvt/W
  QLYZadLYuhv0EXgYPhB6Yjj6xxUQKnjhATaHCE
- (3)kmInfo 解密 DesUtil.des3Decrypt(kmInfo, key “utf-8”);  
  参数说明：hasCardNo，1 为有卡号，0 为无卡号，cardNo：卡号，cardPwd：卡密，outDate：过期时间

##### 备注

- 1、status = 01 或者 02 才是最终状态；
- 2、可查30天内的订单数据，查询频率为 1 分钟 1200 次，超过会返回查询频繁；
- 3、话费类型商品voucherNo表示充值凭证。
