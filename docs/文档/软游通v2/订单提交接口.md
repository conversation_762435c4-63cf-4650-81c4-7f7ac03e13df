##### 接口描述

- 商户通过该接口,将订单提交到玖佰SUP系统进行充值。

##### 接口地址

- 正式环境：[https://supi.900sup.cn/Service/OrderReceive.ashx](https://supi.900sup.cn/Service/OrderReceive.ashx)

  ```
  复制  沙箱环境：http://suptest.900sup.cn:7102/Service/OrderReceive.ashx
    沙箱businessId：Num10001
    沙箱KEY：86b67b65d54c4b64b3bb2db9818a02f2
    沙箱goodsId ：TestFail001jk //异步返回02失败
                  TestSuc001jk  //异步返回01成功
                  TestCami001jk //卡密测试
    沙箱goodsnum ：1
  ```

##### 请求方式

- HTTP的POST方式
- Content-Type： application/x-www-form-urlencoded

##### 请求参数

| 字段名    | 必选 | 变量名         | 说明                                                                          |
|--------|----|-------------|-----------------------------------------------------------------------------|
| 商户号    | 是  | businessId  | 由SUP系统分配每个商户唯一商户号，注意b不要大写                                                   |
| 订单号    | 是  | userOrderId | 由商户自定义，最大长度不超过32位的唯一流水号                                                     |
| 商品编号   | 是  | goodsId     | SUP系统商品编号，通过商务人员获取                                                          |
| 充值账号   | 是  | userName    | 具体充值用户名或游戏通行证账号                                                             |
| 商品名称   | 是  | gameName    | 待充值的商品名称，内容不做要求；完美点券必传“完美点券”，搜狐游戏必传游戏名称                                     |
| 游戏账号   | 否  | gameAcct    | 选填，游戏类商品选填，如魔兽世界的WOW1，中石油油卡时必传 手机号，中石化不用传！                                  |
| 游戏 区   | 否  | gameArea    | 选填，涉及区服的游戏类商品必填                                                             |
| 游戏 服   | 否  | gameSrv     | 选填，涉及区服的游戏类必填                                                               |
| 充值类型   | 否  | gameType    | 商品类型，如月卡、季卡、年卡，网易一卡通必填：帐号直充/点数寄售，盛大点券必填：盛大点券                                |
| 账号类型   | 否  | acctType    | 即充值帐号所属类型                                                                   |
| 商品面值   | 是  | goodsNum    | 充值总面值，单位是元，注意是商品面值，非笔数；                                                     |
| 充值ip   | 否  | orderIp     | 非必填，实际充值用户请求ip，涉及区域匹配的产品必填                                                  |
| 异步通知地址 | 是  | noticeUrl   | 该url地址不能带任何参数，若无需通知充值结果，可为空。                                                |
| 签名     | 是  | sign        | md5后32位小写：md5(businessId + userOrderId+ goodsId + goodsNum + orderIp + key) |

- **编码格式：所有数据编码均采用UTF-8编码。接口URL均需要进行urlEncode编码。**

##### 请求示例

```
复制BusinessId=Num10001&AcctType=手机号&GameAcct=&GameArea=&GameName=测试返回失败&GameSrv=&GameType=月卡&GoodsId=TestFail001jk&GoodsNum=1&NoticeUrl=http://supm2.900sup.cn/service/noticeMessage.ashx&OrderIp=&Sign=8fc1622304177543f7ad3c4375d64390&UserName=***********&CoopOrderSnap=&IsCalc=&UserOrderId=e33ca9b66e18470c912bbe323097acd3
```

```
复制签名示例：
md5(businessId + userOrderId+ goodsId + goodsNum + orderIp + key)
8fc1622304177543f7ad3c4375d64390=md5(Num10001e33ca9b66e18470c912bbe323097acd3TestFail001jk186b67b65d54c4b64b3bb2db9818a02f2)
```

##### 返回示例

```
复制  <?xml version="1.0" encoding="utf-8"?>
  <root>
        <result>返回编码</result>
        <mes>下单结果</mes>
  </root>
```

##### 返回参数说明

| 返回编码 | 下单结果描述                   |
|------|--------------------------|
| 01   | 下单成功，仅代表下单成功，不代表充值成功     |
| 02   | 下单失败                     |
| 03   | 订单号重复，校验周期为近30日内，建议走人工核查 |
| 04   | 已达到风控限制，佳之易平台设置的风控限制。    |

##### 备注

-
采购电费商品时需在gameSrv参数中按充电用户所在区域匹配传参该链接 [http://open.jiaofei100.com/Api/GetLifeSubInfo.aspx](http://open.jiaofei100.com/Api/GetLifeSubInfo.aspx)
文档中的GoodsID参数值。
- IP白名单限制、采购余额不足、商品编号错误及其它参数错误时下单均返回02失败。
